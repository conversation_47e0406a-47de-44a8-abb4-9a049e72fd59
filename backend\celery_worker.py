# /app/services/worker.py

from celery import Celery
from celery.utils.log import get_task_logger
import asyncio

from app.services import user_service
from app.config.config import settings
from app.utils.auth import get_graph_token

# Initialize Celery
celery_app = Celery('worker',
                    broker=settings.CELERY_BROKER_URL,
                    backend=settings.CELERY_RESULT_BACKEND)

logger = get_task_logger(__name__)

@celery_app.task(name='sync_users')
def sync_users():
    """Celery task to synchronize users from Microsoft Graph API"""
    try:
        # Create an event loop for running async code
        loop = asyncio.get_event_loop()
    except RuntimeError:
        # If no event loop exists, create a new one
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)

    try:
        # Get the token and run sync in the same event loop
        access_token = loop.run_until_complete(get_graph_token())
        users_count = loop.run_until_complete(
            user_service.sync_all_users(access_token)
        )
        logger.info(f"Successfully synchronized {users_count} users")
        return users_count
    except Exception as e:
        logger.error(f"Error synchronizing users: {str(e)}")
        raise
    finally:
        loop.close()

if __name__ == '__main__':
    import asyncio
    from app.services import user_service
    from app.utils.auth import get_graph_token

    async def main():
        user_service_instance = user_service.UserService()
        access_token = await get_graph_token()
        await user_service_instance.sync_all_users(access_token)

    asyncio.run(main())