# app.utils.helpers.py:

import re
from typing import List, Dict, Tuple, Optional, Any

def extract_output(text: str) -> Tuple[str, str, Optional[str], Optional[str], Optional[str], Optional[str], List[str], List[str]]:
    """
    Extracts <think>, <refine>, <evaluate> blocks, mermaid code (```mermaid```), 
    citations (<ref> blocks), follow-up queries (<followup> blocks), and returns 
    the remaining final response along with the estimated mode.

    Returns:
        Tuple of (final_response, mode, think_text, action_text, evaluate_text, mermaid_code, citations, followups)
    """

    def extract_block(tag: str) -> Optional[str]:
        matches = re.findall(rf"<{tag}>(.*?)</{tag}>", text, re.DOTALL)
        return "\n\n".join(match.strip() for match in matches) if matches else None

    def extract_list(tag: str) -> List[str]:
        return re.findall(rf"<{tag}>(.*?)</{tag}>", text, re.DOTALL)

    # Extract structured blocks
    think_text = extract_block("think")
    evaluate_text = extract_block("evaluate")
    refine_text = extract_block("refine")

    # Extract Mermaid Code
    mermaid_match = re.search(r"```mermaid(.*?)```", text, re.DOTALL)
    mermaid_code = mermaid_match.group(1).strip() if mermaid_match else None

    # Extract citations and follow-ups separately
    citations = extract_list("ref")
    followups = extract_list("followup")

    # Remove structured tags from final response
    cleaned_text = re.sub(r"<(think|refine|evaluate|ref|followup)>.*?</\1>", "", text, flags=re.DOTALL)
    cleaned_text = re.sub(r"```mermaid.*?```", "", cleaned_text, flags=re.DOTALL)
    final_response = cleaned_text.strip()

    # Estimate Mode
    if not think_text and not evaluate_text and not refine_text:
        mode = "quick"
    elif think_text and not evaluate_text and not refine_text:
        mode = "reasoning"
    elif think_text and (refine_text or evaluate_text):
        mode = "research"
    else:
        mode = "unknown"  # Fallback if mode cannot be determined

    return final_response, mode, think_text, evaluate_text, refine_text, mermaid_code, citations, followups

def ensure_system_prompt(
    messages: List[Dict[str, str]], system_prompt: str
) -> List[Dict[str, str]]:
    """Ensure the system prompt is the first message in the history."""
    if not messages or messages[0].get("role") != "system":
        return [{"role": "system", "content": system_prompt}] + messages
    return messages


def extract_structured_output(text: str) -> Tuple[Optional[str], Optional[str], str]:
    """
    Extracts reasoning (<think>...</think>), mermaid code (```mermaid ... ```),
    and returns the remaining final response (cleaned).
    """
    # Extract reasoning blocks
    thoughts = re.findall(r"<think>(.*?)</think>", text, re.DOTALL)
    reasoning = "\n\n".join(thoughts).strip() if thoughts else None

    # Extract first mermaid code block
    mermaid_match = re.search(r"```mermaid(.*?)```", text, re.DOTALL)
    mermaid_code = mermaid_match.group(1).strip() if mermaid_match else None

    # Remove all reasoning and mermaid blocks from final response
    cleaned_text = re.sub(r"<think>.*?</think>", "", text, flags=re.DOTALL)
    cleaned_text = re.sub(r"```mermaid.*?```", "", cleaned_text, flags=re.DOTALL)

    final_response = cleaned_text.strip()

    return reasoning, mermaid_code, final_response


def extract_cot_output(
    text: str,
) -> Tuple[Optional[str], Optional[str], Optional[str], Optional[str], str]:
    """
    Extracts <think>, <action>, <evaluate> blocks, mermaid code (```mermaid```),
    and returns the remaining final response and estimated mode ("reasoning" or "research").

    Returns:
        Tuple of (think_text, action_text, evaluate_text, mermaid_code, final_response, mode)
    """

    def extract_block(tag: str) -> Optional[str]:
        matches = re.findall(rf"<{tag}>(.*?)</{tag}>", text, re.DOTALL)
        return "\n\n".join(match.strip() for match in matches) if matches else None

    think_text = extract_block("think")
    action_text = extract_block("action")
    evaluate_text = extract_block("evaluate")

    mermaid_match = re.search(r"```mermaid(.*?)```", text, re.DOTALL)
    mermaid_code = mermaid_match.group(1).strip() if mermaid_match else None

    # Remove structured tags and mermaid from final response
    cleaned_text = re.sub(
        r"<(think|action|evaluate)>.*?</\1>", "", text, flags=re.DOTALL
    )
    cleaned_text = re.sub(r"```mermaid.*?```", "", cleaned_text, flags=re.DOTALL)
    final_response = cleaned_text.strip()

    # Estimate mode
    block_lengths = [len(b or "") for b in (think_text, action_text, evaluate_text)]
    num_blocks = sum(bool(b) for b in block_lengths)
    total_len = sum(block_lengths)

    return think_text, action_text, evaluate_text, mermaid_code, final_response


def split_thought_response(text: str) -> (Optional[str], str):
    """
    Extracts reasoning (<think>...</think>) and returns the remaining final response (cleaned).
    """
    thoughts = re.findall(r"<think>(.*?)</think>", text, re.DOTALL)
    reasoning = "\n\n".join(thoughts).strip() if thoughts else None
    final_response = re.sub(r"<think>.*?</think>", "", text, flags=re.DOTALL).strip()
    return reasoning, final_response


def extract_mermaid_block(text: str) -> Optional[str]:
    """Extracts the first mermaid code block from the given text."""
    match = re.search(r"```mermaid(.*?)```", text, re.DOTALL)
    return match.group(1).strip() if match else None


def extract_mermaid_and_citations(
    content: str, tool_results: List[Any]
) -> Tuple[str, List[str]]:
    """
    Extracts the first mermaid code block and any citations from the given content.
    """
    mermaid_code = ""
    citations = []
    if "```mermaid" in content:
        start = content.find("```mermaid") + 9
        end = content.find("```", start)
        mermaid_code = content[start:end].strip()
    for res in tool_results:
        if isinstance(res.result, dict) and "source" in res.result:
            citations.append(res.result["source"])
    return mermaid_code, citations


def extract_citations(content: str) -> List[str]:
    """Extracts citations from the given content."""
    citations = re.findall(r"\[\d+\]", content)
    return [c[1:-1] for c in citations]
