import logging
import httpx
import os
import re
import json
from dotenv import load_dotenv
from fastapi import FastAPI, Depends
from fastapi.responses import StreamingResponse
from typing import Optional

load_dotenv()

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

class RAGEngine:
    """
    Handles RAG query.
    """
    def __init__(self):
        self.rag_provider = "lightRAG"  # Default provider
        self.rag_api_url = os.getenv("LIGHTRAG_API_URL", "http://localhost:9621/query/stream")
        self.rag_api_key = os.getenv("LIGHTRAG_API_KEY", None)
        self.client = httpx.AsyncClient(timeout=60.0)
        logging.info("RAGEngine: RAGEngine initialized.")

    def extract_json_from_response(self, response):
        """
        Extracts JSON content from the response.

        Args:
            response (str): The response string.

        Returns:
            dict: The extracted JSON content.
        """
        try:
            # Try direct JSON parsing first
            parsed_response = json.loads(response)
            return parsed_response
        except json.JSONDecodeError:
            # If direct parsing fails, use regex extraction
            json_content = re.search(r'```json\n(.*?)\n```', response, re.DOTALL)
            if json_content:
                json_string = json_content.group(1)
                try:
                    return json.loads(json_string)
                except json.JSONDecodeError:
                    logging.error("RAGEngine:extract_json_from_response: Error: Invalid JSON format")
                    return None
            else:
                logging.error("RAGEngine:extract_json_from_response: Error: No JSON content found")
                return None

    async def stream_query(self, query: str):
        """
        Calls LightRAG /query/stream API and streams the response.

        Args:
            query: The query string.

        Returns:
            StreamingResponse: A streaming response containing the RAG query results.
        """
        headers = {"Content-Type": "application/json"}
        if self.rag_api_key:
            headers["Authorization"] = f"Bearer {self.rag_api_key}"

        request_body = {
            "query": query,
            "top_k": 3,
        }

        try:
            async with self.client.stream("POST", self.rag_api_url, json=request_body, headers=headers) as response:
                await response.aread() # Ensure headers are read and connection is established

                async def response_generator():
                    async for chunk in response.aiter_text():
                        yield chunk
                    await response.aclose() # Close the response stream after iteration

                return response_generator()

        except httpx.StreamError:
            logging.error("RAGEngine:stream_query: Stream error occurred.")
            raise
        except httpx.HTTPError as e:
            logging.error(f"RAGEngine:stream_query: Error calling LightRAG API: {e}")
            raise e

    async def query(self, query: str):
        """
        Calls LightRAG /query API for non-streaming response.

        Args:
            query (str): The query string.

        Returns:
            dict: The JSON response from the LightRAG API.
        """
        headers = {"Content-Type": "application/json"}
        if self.rag_api_key:
            headers["Authorization"] = f"Bearer {self.rag_api_key}"

        user_prompt = "[回傳結果請使用JSON格式：{ 'result': string, 'status': boolean }，result是結果，status是代表是否有相關資訊] "
        request_body = {
            "query": user_prompt + query,
            "top_k": 3,
        }

        try:
            async with httpx.AsyncClient(timeout=60.0) as client:
                response = await client.post(self.rag_api_url.replace('/stream', ''), json=request_body, headers=headers)
                response.raise_for_status()  # Raise an exception for HTTP errors (4xx or 5xx)
                return response.json()
        except httpx.HTTPStatusError as e:
            logging.error(f"RAGEngine:query: HTTP error occurred: {e.response.status_code} - {e.response.text}")
            raise e
        except httpx.RequestError as e:
            logging.error(f"RAGEngine:query: An error occurred while requesting {e.request.url!r}: {e}")
            raise e