# backend/app/api/ai_chat.py

from datetime import datetime, timezone
import json
import os
from pathlib import Path
import re
from functools import lru_cache
from fastapi import APIRouter, Depends, HTTPException, Request
from fastapi.responses import StreamingResponse, JSONResponse
from app.services.ai_chat_service import ChatService
from app.storage.ai_chat_storage import ChatStorage
from app.utils.logger import ColoredLogger

logger = ColoredLogger("app.core.ai_chat")

router = APIRouter()


@lru_cache()
def get_chat_service():
    return ChatService()


@router.post("/chat/start")
async def start_chat(
    request: Request, service: ChatService = Depends(get_chat_service)
):
    user_id = request.headers.get("user_id")
    data = await request.json()
    title = data.get("title")
    session_id = service.start_new_session(user_id, title)
    return {"session_id": session_id}

@router.get("/chat/sessions/{session_id}")
async def get_session(request: Request, session_id: str, service: ChatStorage = Depends()):
    user_id = request.headers.get("user_id")
    session = service.get_session(user_id, session_id)
    if not session:
        raise HTTPException(
            status_code=404, detail=f"Session {session_id} not found for {user_id}"
        )
    return session

@router.get("/chat/sessions")
async def list_sessions(request: Request, service: ChatStorage = Depends()):
    user_id = request.headers.get("user_id")
    sessions = service.list_sessions(user_id)
    if not sessions:
        raise HTTPException(status_code=404, detail=f"Sessions not found for {user_id}")
    return sessions


@router.delete("/chat/sessions/{session_id}")
async def delete_session(request: Request, session_id: str, service: ChatStorage = Depends()):
    user_id = request.headers.get("user_id")
    service.delete_session(user_id, session_id)
    return {
        "success": True,
        "data": f"Session {session_id} deleted for {user_id}",
        "timestamp": datetime.now(timezone.utc).isoformat(),
    }


@router.delete("/chat/sessions")
async def delete_session(request: Request, service: ChatStorage = Depends()):
    user_id = request.headers.get("user_id")
    service.delete_all_sessions(user_id)
    return {
        "success": True,
        "data": f"All session deleted for {user_id}",
        "timestamp": datetime.now(timezone.utc).isoformat(),
    }

@router.post("/chat/feedback/{session_id}/{message_id}")
async def update_message_feedback(request: Request, session_id: str, message_id: int, service: ChatService = Depends()):
    user_id = request.headers.get("user_id")
    data = await request.json()
    feedback = data.get("feedback")
    if not feedback:
        raise HTTPException(status_code=400, detail="Feedback is required")
    
    if service.update_message_feedback(user_id, session_id, message_id, feedback):
        return {
            "success": True,
            "data": f"Feedback for message {message_id} in session {session_id} updated for {user_id}",
            "timestamp": datetime.now(timezone.utc).isoformat()
        }
    else:
        raise HTTPException(status_code=500, detail="Failed to update feedback")

@router.get("/chat/feedback/{session_id}/{message_id}")
async def get_message_feedback(request: Request, session_id: str, message_id: int, service: ChatService = Depends()):
    user_id = request.headers.get("user_id")
    feedback = service.get_message_feedback(user_id, session_id, message_id)
    if not feedback:
        raise HTTPException(status_code=404, detail=f"Feedback for message {message_id} in session {session_id} not found for {user_id}")
    return {
        "success": True,
        "data": feedback,
        "timestamp": datetime.now(timezone.utc).isoformat()
    }

@router.get("/chat/tools")
async def get_chat_tools(request: Request, service: ChatService = Depends(get_chat_service)):
    """Return list of successfully initialized RAG and MCP servers along with their available tools."""
    try:
        # Simplify the response structure by extracting server configs and tools
        servers = []
        response = await service.get_lightrag_documents()
        if response:
            if response.get("statuses") and response.get("statuses").get("processed"):
                server = {
                    "name": "lightrag",
                    "description": "Use LightRAG to query internal knowledgebase",
                    "type": "rag",
                    "tools": [],
                    "enabled": True
                }
                for document in response.get("statuses").get("processed"):
                    server["tools"].append({
                        "name": document.get("file_path"),
                        "description": document.get("content_summary")
                    })
                servers.append(server)

        mcp_clients = request.app.state.mcp_clients  # Access the global mcp_clients
        for (
            name,
            client,
        ) in mcp_clients.items():  # Use mcp_clients to get initialized servers
            config = client.config  # Access the client's configuration
            tools = [{"name": tool.name, "description": tool.description} for tool in client.tools]
            servers.append({
                "name": name,
                "description": config["description"],
                "type": "mcp",
                "tools": tools,  # Include available tools
                "enabled": True
            })
        return {"success": True, "servers": servers}
    except (RuntimeError, ValueError, TypeError) as e:  # Replace with specific exceptions
        logger.error("GET /chat/tools: %s", str(e))
        return {"success": False, "error": str(e)}


@router.post("/chat/query")
async def query(request: Request, service: ChatService = Depends(get_chat_service)):
    try:
        data = await request.json()
        user_id = request.headers.get("user_id")

        session_id = data.get("session_id")
        query = data.get("query")
        enabled_tools = data.get("enabled_tools", [])
        mode = data.get("mode", "quick")     # Default mode
        provider_name = data.get("provider_name", "Aliyun")  # Default provider
        model_name = data.get("model_name", "qwen-plus-latest")  # Default model
        tools_enabled = any(tool.get("enabled", False) for tool in enabled_tools)
        logger.info("POST /chat/query: %s, %s, %s, %s, %s, %s, %s", user_id, session_id, query, tools_enabled, mode, provider_name, model_name)
        response = await service.process_query(
            user_id=user_id, 
            session_id=session_id, 
            query=query,
            enabled_tools=enabled_tools, 
            mode=mode,
            provider_name=provider_name, 
            model_name=model_name
        )
        return {
            "success": True,
            "message": response,
            "timestamp": datetime.now(timezone.utc).isoformat(),
        }
    except Exception as e:
        logger.error("POST /chat/query: %s", str(e))
        return {
            "success": False,
            "error": str(e),
            "timestamp": datetime.now(timezone.utc).isoformat(),
        }

@router.post("/chat/query/stream")
async def stream_query(request: Request, service: ChatService = Depends(get_chat_service)):
    user_id = request.headers.get("user_id")

    session_id = data.get("session_id")
    query = data.get("query")
    enabled_tools = data.get("enabled_tools", [])
    mode = data.get("mode", "quick") # Default mode
    provider_name = data.get("provider_name", "Aliyun")  # Default provider
    model_name = data.get("model_name", "qwen-plus-latest")  # Default model
    tools_enabled = any(tool.get("enabled", False) for tool in enabled_tools)
    logger.info("POST /chat/query/stream: %s, %s, %s, %s, %s, %s, %s", user_id, session_id, query, tools_enabled, mode, provider_name, model_name)
    async def event_stream():
        try:
            async for event in service.process_stream_query(
                user_id=user_id,
                session_id=session_id,
                query=query,
                enabled_tools=enabled_tools,
                mode=mode,
                provider_name=provider_name,
                model_name=model_name
            ):
                event["timestamp"] = datetime.now(timezone.utc).isoformat()
                yield f"data: {json.dumps(event)}\n\n"
        except Exception as e:
            logger.error("POST /chat/query/stream: %s", str(e), exc_info=True)
            error_event = {
                "type": "error",
                "data": f"Error: {str(e)}",
                "timestamp": datetime.now(timezone.utc).isoformat(),
            }
            yield f"data: {json.dumps(error_event)}\n\n"

    return StreamingResponse(event_stream(), media_type="text/event-stream")

@router.get("/chat/query/tts/{text}")
def get_tts(
    text: str, 
    service: ChatService = Depends()
):
    try:
        response = service.get_tts(text)
        logger.info("GET /chat/query/tts/%s: %s", text, len(response))
        return {
            "success": True,
            "data": response,
            "timestamp": datetime.now(timezone.utc).isoformat()
        }
    except Exception as e:
        logger.error("GET /chat/query/tts/%s: %s", text, str(e))
        return {
            "success": False,
            "error": str(e),
            "timestamp": datetime.now(timezone.utc).isoformat()
        }


@router.get("/chat/providers")
async def get_providers(request: Request, service: ChatService = Depends()):
    providers = service.get_providers_and_models()
    if not providers:
        raise HTTPException(status_code=404, detail="Providers not found")
    return providers


@router.get("/chat/metrics")
async def get_metrics(limit: int = 100):
    chat_metrics_file = os.getenv("CHAT_METRICS_FILE", "./logs/chat_metrics.log")
    LOG_FILE = Path(chat_metrics_file)

    # Pattern to extract [METRIC] {...json...}
    METRIC_REGEX = re.compile(r'\[METRIC\] ({.*?})(?=\s*\d{4}-\d{2}-\d{2}|\s*$)', re.DOTALL)

    try:
        text = LOG_FILE.read_text(encoding="utf-8", errors="ignore")
        print(text)
        entries = METRIC_REGEX.findall(text)
        parsed = [json.loads(e) for e in entries[-limit:]]
        return JSONResponse(content=parsed)
    except Exception as e:
        return JSONResponse(status_code=500, content={"error": str(e)})