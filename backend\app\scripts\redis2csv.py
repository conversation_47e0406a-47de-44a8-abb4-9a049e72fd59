# Run:
# python d:/source/mep/backend/app/scripts/redis2csv.py output.csv

import csv
import os
import sys
from pathlib import Path

# Add the parent directory to sys.path to import from app
parent_dir = str(Path(__file__).resolve().parent.parent)
if parent_dir not in sys.path:
    sys.path.append(parent_dir)

from storage.user_storage import UserStorage

def dump_departments_to_csv(output_file: str):
    """Dump all departments to a CSV file."""
    storage = UserStorage()
    departments = storage.get_all_departments()

    if not departments:
        print("No departments found")
        return

    # Get all unique fields from all departments to use as CSV headers
    headers = set()
    for department in departments:
        headers.update(department.keys())
    headers = sorted(list(headers))  # Sort headers for consistency

    # Write to CSV
    with open(output_file, 'w', newline='', encoding='utf-8') as f:
        writer = csv.DictWriter(f, fieldnames=headers)
        writer.writeheader()
        writer.writerows(departments)
    
    print(f"Successfully exported {len(departments)} departments to {output_file}")

def dump_user_profiles_to_csv(output_file: str):
    """Dump all user profiles to a CSV file, excluding photo data."""
    storage = UserStorage()
    user_ids = storage.get_all_oids()
    
    # Get all user profiles
    profiles = []
    for user_id in user_ids:
        profile = storage.get_user_profile(user_id)
        if profile:
            # Remove photo data if present
            if 'photo' in profile:
                del profile['photo']
            profiles.append({**profile, 'user_id': user_id})
    
    if not profiles:
        print("No user profiles found")
        return
    
    # Get all unique fields from all profiles to use as CSV headers
    headers = set()
    for profile in profiles:
        headers.update(profile.keys())
    headers = sorted(list(headers))  # Sort headers for consistency
    
    # Write to CSV
    with open(output_file, 'w', newline='', encoding='utf-8') as f:
        writer = csv.DictWriter(f, fieldnames=headers)
        writer.writeheader()
        writer.writerows(profiles)
    
    print(f"Successfully exported {len(profiles)} user profiles to {output_file}")

if __name__ == '__main__':
    output_file = 'departments.csv'
    if len(sys.argv) > 1:
        output_file = sys.argv[1]
    
    dump_departments_to_csv(output_file)