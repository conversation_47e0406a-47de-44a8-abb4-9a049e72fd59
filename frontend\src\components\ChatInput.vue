<template>
  <v-card
    class="pa-2 ma-4 elevation-2"
    rounded="xl"
    color="surface-lighten-1"
    style="max-width: 100%;"
  >
    <!-- Text input area -->
    <v-textarea
      v-model="inputValue"
      auto-grow
      :placeholder="placeholder"
      rows="1"
      max-rows="5"
      variant="plain"
      hide-details
      style="width: 100%;"
      @keydown.enter.prevent="(e) => {
        if (!e.shiftKey) {
          emit('send')
        } else {
          inputValue += '\n'
        }
      }"
    />

    <!-- Bottom action row -->
    <div class="d-flex justify-space-between align-end mt-1">
      <!-- Left-side controls -->
      <div class="d-flex align-center flex-wrap">
        <template v-if="!mdAndDown">
          <v-btn icon size="small" variant="text" class="mr-1" @click="emit('add')">
            <v-icon>mdi-plus</v-icon>
          </v-btn>
          <v-chip-group column>
            <v-chip
              v-for="tag in externalTags"
              :key="tag.id"
              class="ma-1"
              size="small"
              :variant="tag.id === selectedTag ? 'flat' : 'outlined'"
              @click="emit('update:selectedTag', tag.id)"
            >
              {{ tag.name }}
            </v-chip>
          </v-chip-group>
        </template>
        <template v-else>
          <v-btn icon size="small" variant="text" class="mr-1" @click="emit('add')">
            <v-icon>mdi-plus</v-icon>
          </v-btn>
          <v-menu>
            <template v-slot:activator="{ props }">
              <v-btn icon size="small" variant="text" v-bind="props" class="mr-1">
                <v-icon>mdi-tune-variant</v-icon>
              </v-btn>
            </template>
            <v-list>
              <v-list-item
                v-for="tag in externalTags"
                :key="tag.id"
                @click="emit('update:selectedTag', tag.id)"
                :class="{ 'selected-tag': tag.id === selectedTag, 'unselected-tag': tag.id !== selectedTag }"
              >
                <v-list-item-title>{{ tag.name }}</v-list-item-title>
              </v-list-item>
            </v-list>
          </v-menu>
        </template>
      </div>

      <!-- Right-side icons -->
      <div class="d-flex align-center">
        <v-btn icon size="small" variant="text" :color="isRecording ? 'error' : 'primary'" @click="emit('mic')">
          <v-icon>{{ isRecording ? 'mdi-microphone-off' : 'mdi-microphone' }}</v-icon>
        </v-btn>
        <v-btn icon size="small" variant="text" @click="emit('audio')">
          <v-icon>mdi-waveform</v-icon>
        </v-btn>
      </div>
    </div>
  </v-card>
</template>

<script setup>
import { computed } from 'vue'
import { useDisplay } from 'vuetify'

const { mdAndDown } = useDisplay()

const props = defineProps({
  modelValue: String,
  placeholder: { type: String, default: '請問任何問題' },
  externalTags: { type: Array, default: () => [] },
  isRecording: Boolean,
  selectedTag: String,
})

const emit = defineEmits(['add', 'update:selectedTag', 'mic', 'audio', 'send', 'update:modelValue'])

const inputValue = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
})
</script>

<style scoped>
.v-textarea textarea {
  color: rgb(var(--v-theme-on-surface)) !important;
}

.hover-card:hover {
  background-color: rgb(var(--v-theme-secondary)) !important; /* Change to desired hover color */
  color: rgb(var(--v-theme-on-secondary)) !important; /* Adjust text color */
  cursor: pointer;
}

.selected-tag {
  background-color: rgb(var(--v-theme-secondary)) !important; /* Change to desired hover color */
  color: rgb(var(--v-theme-on-secondary)) !important; /* Adjust text color */
  font-weight: bold;
}

.unselected-tag {
  background-color: rgb(var(--v-theme-background)) !important; /* Change to desired hover color */
  color: rgb(var(--v-theme-on-background)) !important; /* Adjust text color */
  font-weight: bold;
}
</style>
