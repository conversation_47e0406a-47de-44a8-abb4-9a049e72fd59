import { ref } from 'vue'

export const menus = ref([
    {
      titleKey: 'menus.aboutMoxa.title',
      items: [
        { titleKey: 'menus.aboutMoxa.submenus.companyProfile', link: 'https://www.moxa.com/tw/about-us'  },
        { titleKey: 'menus.aboutMoxa.submenus.qualityPolicy', link: 'https://moxa.sharepoint.com/sites/LifeMoxa/SitePages/%E5%93%81%E8%B3%AA%E8%88%87%E5%AE%89%E5%85%A8%E6%94%BF%E7%AD%96.aspx?csf=1&web=1&e=BVxtks&xsdata=*******************************************************************************************************************************************************************************************************************************************************************************************************************%3d&sdata=Nm1uaFF1ZkpYelNrN0s4T1JzM2EwdVdIM3BDZkFleE9pSUdBaWgva1JMND0%3d'   },
        { titleKey: 'menus.aboutMoxa.submenus.ehsSustainability', link: 'https://moxa.sharepoint.com/sites/EHS2'   },
        { titleKey: 'menus.aboutMoxa.submenus.mibHeadquarters', link: 'https://moxa.sharepoint.com/sites/MIBMoxaInc.Building/SitePages/LeadershipHome.aspx'   },
      ]
    },
    {
      titleKey: 'menus.announcements.title',
      items: [
        { titleKey: 'menus.announcements.submenus.career', link: 'http://mhqehrvm01.moxa.com/ReqList/'  },
        { titleKey: 'menus.announcements.submenus.events', link: '/calendar'   },
        { titleKey: 'menus.announcements.submenus.channelMoxa', link: 'https://moxa.sharepoint.com/sites/ChannelMoxa'   },
      ]
    },
    {
      titleKey: 'menus.benefitsAndMe.title',
      items: [
        { titleKey: 'menus.benefitsAndMe.submenus.ewc', link: 'https://moxa.sharepoint.com/sites/MOXAEWC'   },
        { titleKey: 'menus.benefitsAndMe.submenus.groupIssurance', link: 'https://moxa.sharepoint.com/sites/HR-INSURANCE/SitePages/%E5%9C%98%E4%BF%9D-%E7%95%B6%E5%B9%B4%E5%BA%A6%E5%9C%98%E9%AB%94%E4%BF%9D%E9%9A%AA%E6%96%B9%E6%A1%88.aspx'  },
        { titleKey: 'menus.benefitsAndMe.submenus.travelIssurance', link: 'https://moxa.sharepoint.com/sites/HR-INSURANCE/SitePages/%E5%B7%AE%E6%97%85-MHQ%E5%B7%AE%E6%97%85%E8%BE%A6%E6%B3%95.aspx'  },
        { titleKey: 'menus.benefitsAndMe.submenus.employeeCare', link: 'https://moxa.sharepoint.com/sites/LifeMoxa/SitePages/%E5%B0%88%E6%A1%88%E4%BB%8B%E7%B4%B9.aspx'   },
      ]
    },
    {
      titleKey: 'menus.culture.title',
      items: [
        { titleKey: 'menus.culture.submenus.holidays', link: '/holidays'  },
        { titleKey: 'menus.culture.submenus.volunteer', link: 'https://moxa.sharepoint.com/sites/LifeMoxa/SitePages/%E4%BC%81%E6%A5%AD%E6%B4%BB%E5%8B%95%E5%BF%97%E5%B7%A5.aspx'   },
        { titleKey: 'menus.culture.submenus.readingHours', link: 'https://moxa.sharepoint.com/sites/LifeMoxa/SitePages/%E8%AE%80%E6%80%9D%E6%A8%82-Reading-Hour.aspx?csf=1&web=1&e=ZgPquE&xsdata=*******************************************************************************************************************************************************************************************************************************************************************************************************************%3d&sdata=dTd6K3AvbnFHU2R5VWo5cWF2bDB1WVQ0UUlXTTRXeldtTWpVYW4zbW5lVT0%3d'   },
        { titleKey: 'menus.culture.submenus.rdValues', link: 'https://moxa.sharepoint.com/sites/Moxa43'   },
      ]
    },
    {
      titleKey: 'menus.myColleagues.title',
      items: [
        { titleKey: 'menus.myColleagues.submenus.whereColleagues', link: '/office-floorplan'  },
        { titleKey: 'menus.myColleagues.submenus.newEmployees', link: '/new-employees'   },
        { titleKey: 'menus.myColleagues.submenus.organizationChart', link: '/organization-chart' }
      ]
    },
    {
      titleKey: 'menus.foundation.title',
      items: [
        { titleKey: 'menus.foundation.submenus.foundationWebsite', link: 'https://www.moxa.org.tw/'  },
        { titleKey: 'menus.foundation.submenus.moxaLava', link: 'https://moxa.sharepoint.com/sites/Volunteer'   },
      ]
    },
  ])