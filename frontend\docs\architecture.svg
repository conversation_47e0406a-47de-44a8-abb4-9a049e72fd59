<?xml version="1.0" encoding="UTF-8"?>
<svg width="1200" height="800" xmlns="http://www.w3.org/2000/svg">
  <!-- 定義重用的樣式 -->
  <defs>
    <style type="text/css">
      .title { font-size: 24px; font-weight: bold; fill: #333; }
      .subtitle { font-size: 18px; font-weight: bold; fill: #666; }
      .label { font-size: 14px; fill: #444; }
      .connection { stroke: #666; stroke-width: 2; fill: none; }
      .module { fill: #fff; stroke: #333; stroke-width: 2; }
      .frontend { fill: #e3f2fd; }
      .backend { fill: #f3e5f5; }
      .component { fill: #fff; stroke: #666; stroke-width: 1; }
    </style>
  </defs>

  <!-- 標題 -->
  <text x="600" y="40" class="title" text-anchor="middle">MEP System Architecture</text>

  <!-- 前端部分 -->
  <g transform="translate(50,80)">
    <!-- 前端容器 -->
    <rect x="0" y="0" width="500" height="600" rx="10" class="module frontend"/>
    <text x="250" y="30" class="subtitle" text-anchor="middle">Frontend (Vue.js)</text>

    <!-- UI組件層 -->
    <g transform="translate(20,60)">
      <rect x="0" y="0" width="460" height="200" rx="5" class="component"/>
      <text x="230" y="25" class="subtitle" text-anchor="middle">UI Components</text>
      
      <!-- 主要頁面組件 -->
      <rect x="20" y="40" width="200" height="30" rx="5" class="component"/>
      <text x="120" y="60" class="label" text-anchor="middle">OfficeFloorplanPage</text>
      
      <rect x="240" y="40" width="200" height="30" rx="5" class="component"/>
      <text x="340" y="60" class="label" text-anchor="middle">ChatBotPage</text>

      <!-- 卡片組件 -->
      <rect x="20" y="90" width="420" height="90" rx="5" class="component"/>
      <text x="230" y="110" class="label" text-anchor="middle">Dashboard Cards</text>
      <text x="230" y="130" class="label" text-anchor="middle" font-size="12">(BenefitPoints, AnnualLeaves, TodoList,</text>
      <text x="230" y="150" class="label" text-anchor="middle" font-size="12">Sales, Weather, PowerBI, etc.)</text>
    </g>

    <!-- 功能層 -->
    <g transform="translate(20,280)">
      <rect x="0" y="0" width="460" height="150" rx="5" class="component"/>
      <text x="230" y="25" class="subtitle" text-anchor="middle">Core Features</text>

      <rect x="20" y="40" width="200" height="90" rx="5" class="component"/>
      <text x="120" y="70" class="label" text-anchor="middle">Office Management</text>
      <text x="120" y="90" class="label" text-anchor="middle" font-size="12">(Floor Plan, Simulation)</text>
      <text x="120" y="110" class="label" text-anchor="middle" font-size="12">(Employee Management)</text>

      <rect x="240" y="40" width="200" height="90" rx="5" class="component"/>
      <text x="340" y="70" class="label" text-anchor="middle">Dashboard System</text>
      <text x="340" y="90" class="label" text-anchor="middle" font-size="12">(Widget Management)</text>
      <text x="340" y="110" class="label" text-anchor="middle" font-size="12">(Data Visualization)</text>
    </g>

    <!-- 服務層 -->
    <g transform="translate(20,450)">
      <rect x="0" y="0" width="460" height="130" rx="5" class="component"/>
      <text x="230" y="25" class="subtitle" text-anchor="middle">Services and Utils</text>

      <rect x="20" y="40" width="130" height="70" rx="5" class="component"/>
      <text x="85" y="75" class="label" text-anchor="middle">API Services</text>
      <text x="85" y="95" class="label" text-anchor="middle" font-size="12">(User, Chat)</text>

      <rect x="170" y="40" width="130" height="70" rx="5" class="component"/>
      <text x="235" y="75" class="label" text-anchor="middle">State Management</text>
      <text x="235" y="95" class="label" text-anchor="middle" font-size="12">(Pinia Store)</text>

      <rect x="320" y="40" width="120" height="70" rx="5" class="component"/>
      <text x="380" y="75" class="label" text-anchor="middle">i18n</text>
      <text x="380" y="95" class="label" text-anchor="middle" font-size="12">(Localization)</text>
    </g>
  </g>

  <!-- 後端部分 -->
  <g transform="translate(650,80)">
    <!-- 後端容器 -->
    <rect x="0" y="0" width="500" height="600" rx="10" class="module backend"/>
    <text x="250" y="30" class="subtitle" text-anchor="middle">Backend Services</text>

    <!-- API層 -->
    <g transform="translate(20,60)">
      <rect x="0" y="0" width="460" height="150" rx="5" class="component"/>
      <text x="230" y="25" class="subtitle" text-anchor="middle">API Gateway</text>

      <rect x="20" y="40" width="200" height="90" rx="5" class="component"/>
      <text x="120" y="70" class="label" text-anchor="middle">RESTful APIs</text>
      <text x="120" y="90" class="label" text-anchor="middle" font-size="12">(User Management)</text>
      <text x="120" y="110" class="label" text-anchor="middle" font-size="12">(Office Management)</text>

      <rect x="240" y="40" width="200" height="90" rx="5" class="component"/>
      <text x="340" y="70" class="label" text-anchor="middle">WebSocket</text>
      <text x="340" y="90" class="label" text-anchor="middle" font-size="12">(Real-time Updates)</text>
      <text x="340" y="110" class="label" text-anchor="middle" font-size="12">(Chat System)</text>
    </g>

    <!-- 業務邏輯層 -->
    <g transform="translate(20,230)">
      <rect x="0" y="0" width="460" height="200" rx="5" class="component"/>
      <text x="230" y="25" class="subtitle" text-anchor="middle">Business Logic</text>

      <rect x="20" y="40" width="200" height="70" rx="5" class="component"/>
      <text x="120" y="75" class="label" text-anchor="middle">User Service</text>
      <text x="120" y="95" class="label" text-anchor="middle" font-size="12">(Authentication)</text>

      <rect x="240" y="40" width="200" height="70" rx="5" class="component"/>
      <text x="340" y="75" class="label" text-anchor="middle">Office Service</text>
      <text x="340" y="95" class="label" text-anchor="middle" font-size="12">(Space Management)</text>

      <rect x="20" y="120" width="200" height="70" rx="5" class="component"/>
      <text x="120" y="155" class="label" text-anchor="middle">Chat Service</text>
      <text x="120" y="175" class="label" text-anchor="middle" font-size="12">(Message Processing)</text>

      <rect x="240" y="120" width="200" height="70" rx="5" class="component"/>
      <text x="340" y="155" class="label" text-anchor="middle">Widget/Layout Service</text>
      <text x="340" y="175" class="label" text-anchor="middle" font-size="12">(Layout Management)</text>
    </g>

    <!-- 數據層 -->
    <g transform="translate(20,450)">
      <rect x="0" y="0" width="460" height="130" rx="5" class="component"/>
      <text x="230" y="25" class="subtitle" text-anchor="middle">Data Layer</text>

      <rect x="20" y="40" width="200" height="70" rx="5" class="component"/>
      <text x="120" y="75" class="label" text-anchor="middle">Database</text>
      <text x="120" y="95" class="label" text-anchor="middle" font-size="12">(User, Office Data)</text>

      <rect x="240" y="40" width="200" height="70" rx="5" class="component"/>
      <text x="340" y="75" class="label" text-anchor="middle">Cache</text>
      <text x="340" y="95" class="label" text-anchor="middle" font-size="12">(Redis)</text>
    </g>
  </g>

  <!-- 連接線 -->
  <g>
    <!-- 前端到後端的連接 -->
    <path d="M 550,200 L 650,200" class="connection"/>
    <path d="M 550,350 L 650,350" class="connection"/>
    <path d="M 550,500 L 650,500" class="connection"/>
  </g>
</svg>