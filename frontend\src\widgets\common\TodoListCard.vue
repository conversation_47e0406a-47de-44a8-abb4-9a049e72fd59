<script setup>
import { ref, reactive } from 'vue'
import { useI18n } from 'vue-i18n'
import { useUserStore } from '@/stores/userStore'

const { t, locale } = useI18n()
const userStore = useUserStore()

const emit = defineEmits(['editTodo', 'deleteTodo', 'toggleStatus', 'updateTodoCount']) // Added updateTodoCount

// Use reactive() to make these variables reactive
const state = reactive({
  todoDialog: false,
  editedTodoIndex: -1,
  editedTodo: {
    id: 0,
    description: '',
    dueDate: '',
    status: false
  },
  defaultTodo: {
    id: 0,
    description: '',
    dueDate: '',
    status: false
  }
})

const headers = [
  { title: t('common.description'), key: 'description', sortable: true },
  { title: t('toDoCard.dueDate'), key: 'dueDate', sortable: true },
  { title: t('toDoCard.status'), key: 'status', sortable: true },
  { title: t('common.action'), key: 'actions', sortable: false }
]


function editTodo(item) {
  state.editedTodoIndex = userStore.getTodoList.indexOf(item)
  state.editedTodo = Object.assign({}, item)
  state.todoDialog = true
}

function deleteTodo(item) {
  const index = userStore.getTodoList.indexOf(item)
  if (confirm(t('common.deleteConfirmation'))) {
    userStore.deleteTodo(index)
    emit('updateTodoCount')
  }
}

function close() {
  state.todoDialog = false
  state.editedTodo = Object.assign({}, state.defaultTodo)
  state.editedTodoIndex = -1
}

function save() {
  if (state.editedTodoIndex > -1) {
    userStore.updateTodo(state.editedTodoIndex, state.editedTodo)
  } else {
    userStore.addTodo(state.editedTodo)
  }
  emit('updateTodoCount')
  close()
}

function updateTodoCount() {
  emit('updateTodoCount') // Emit the event
}

function toggleStatus(item) {
  const index = userStore.getTodoList.indexOf(item)
  userStore.toggleTodoStatus(index)
  emit('updateTodoCount')
}
</script>

<template>
  <v-card height="100%" class="todo-list-card">
    <v-card-title class="d-flex align-center">
      <v-icon icon="mdi-format-list-checks" color="primary" class="mr-2"></v-icon>
      {{ t('toDoCard.title')}}
      <v-spacer></v-spacer>
      <v-btn
        color="primary"
        size="small"
        prepend-icon="mdi-plus"
        @click="state.todoDialog = true"
      >
        {{ t('common.add') }}
      </v-btn>
    </v-card-title>
    <v-card-text>
      <v-data-table
        :headers="headers"
        :items="userStore.getTodoList"
        :items-per-page="5"
        class="elevation-1"
      >
        <template v-slot:item.status="{ item }">
          <v-btn
            :color="item.status ? 'success' : 'warning'"
            size="small"
            variant="tonal"
            @click="toggleStatus(item)"
          >
            {{ item.status ? t('toDoCard.done') : t('toDoCard.pending') }}
          </v-btn>
        </template>
        <template v-slot:item.actions="{ item }">
          <div class="d-flex">
            <v-btn
              icon="mdi-pencil"
              size="small"
              color="primary"
              variant="text"
              class="mr-2"
              @click="editTodo(item)"
            ></v-btn>
            <v-btn
              icon="mdi-delete"
              size="small"
              color="error"
              variant="text"
              @click="deleteTodo(item)"
            ></v-btn>
          </div>
        </template>
      </v-data-table>
    </v-card-text>
  </v-card>
  <v-dialog v-model="state.todoDialog" max-width="500px">
    <v-card>
      <v-card-title>
        <span>{{ state.editedTodoIndex === -1 ? t('common.add') : t('common.edit') }}</span>
      </v-card-title>
      <v-card-text>
        <v-container>
          <v-row>
            <v-col cols="12">
              <v-text-field
                v-model="state.editedTodo.description"
                :label="t('common.description')"
                required
              ></v-text-field>
            </v-col>
            <v-col cols="12">
              <v-text-field
                v-model="state.editedTodo.dueDate"
                :label="t('toDoCard.dueDate')"
                type="date"
                required
              ></v-text-field>
            </v-col>
          </v-row>
        </v-container>
      </v-card-text>
      <v-card-actions>
        <v-spacer></v-spacer>
        <v-btn color="error" variant="text" @click="close">{{ t('common.close') }}</v-btn>
        <v-btn color="primary" variant="text" @click="save">{{ t('common.save') }}</v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

