<script setup>
import { ref } from 'vue'

// const props = defineProps({
//   links: {
//     type: Array,
//     required: true,
//     default: () => []
//   }
// })

const links = ref([
  { title: 'ESG', icon: 'mdi-leaf', color: 'green' },
  { title: 'HR', icon: 'mdi-account-group', color: 'blue' },
  { title: 'Finance', icon: 'mdi-cash', color: 'green-darken-2' },
  { title: 'Legal', icon: 'mdi-gavel', color: 'deep-purple' },
  { title: 'Administration', icon: 'mdi-domain', color: 'blue-grey' },
  { title: 'Quality Management', icon: 'mdi-check-decagram', color: 'teal' },
  { title: 'IT', icon: 'mdi-laptop', color: 'indigo' },
  { title: 'Manufacturing', icon: 'mdi-factory', color: 'brown' },
  { title: 'Sales Operations', icon: 'mdi-store', color: 'orange' },
  { title: 'Product Operations', icon: 'mdi-package-variant', color: 'cyan' }
])

</script>

<template>
  <v-card height="100%" class="useful-links-card">
    <v-card-title class="d-flex align-center">
      <v-icon icon="mdi-link" color="primary" class="mr-2"></v-icon>
      Useful Links
    </v-card-title>
    <v-card-text>
      <div class="d-flex flex-wrap justify-space-around align-center">
        <v-btn
          v-for="link in links"
          :key="link.title"
          :color="link.color"
          :prepend-icon="link.icon"
          class="ma-1"
          variant="tonal"
        >
          {{ link.title }}
        </v-btn>
      </div>
    </v-card-text>
  </v-card>
</template>