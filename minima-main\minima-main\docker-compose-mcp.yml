version: '3.9'

services:
  # Qdrant 向量數據庫
  qdrant:
    image: qdrant/qdrant:latest
    container_name: qdrant
    ports:
      - "6333:6333"
      - "6334:6334"
    expose:
      - 6333
      - 6334
      - 6335
    volumes:
      - ./qdrant_data:/qdrant/storage
    environment:
      QDRANT__LOG_LEVEL: "INFO"
    networks:
      - http-mcp-network
    restart: unless-stopped

  # 索引器服務 (端口 8003)
  indexer:
    build:
      context: ./indexer
      dockerfile: Dockerfile
      args:
        EMBEDDING_MODEL_ID: ${EMBEDDING_MODEL_ID:-sentence-transformers/all-MiniLM-L6-v2}
        EMBEDDING_SIZE: ${EMBEDDING_SIZE:-384}
    container_name: indexer
    volumes:
      - ${LOCAL_FILES_PATH:-../../backend/app/document}:/usr/src/app/local_files/
      - ./indexer:/usr/src/app
      - ./indexer_data:/indexer/storage
    ports:
      - "8003:8001"  # 外部 8003 映射到內部 8001，避免與後端系統衝突
    environment:
      - PYTHONPATH=/usr/src
      - PYTHONUNBUFFERED=TRUE
      - LOCAL_FILES_PATH=${LOCAL_FILES_PATH:-../../backend/app/document}
      - EMBEDDING_MODEL_ID=${EMBEDDING_MODEL_ID:-sentence-transformers/all-MiniLM-L6-v2}
      - EMBEDDING_SIZE=${EMBEDDING_SIZE:-384}
      - CONTAINER_PATH=/usr/src/app/local_files/
    depends_on:
      - qdrant
    networks:
      - http-mcp-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8001/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # HTTP MCP 服務器 (端口 8001)
  http-mcp-server:
    build:
      context: ./mcp-server
      dockerfile: Dockerfile.http
    container_name: http-mcp-server
    ports:
      - "8001:8001"  # HTTP MCP 服務器使用 8001 端口
    environment:
      - PYTHONPATH=/app/src
      - PYTHONUNBUFFERED=1
      - PYTHONIOENCODING=utf-8
      - INDEXER_URL=http://indexer:8001  # 內部網絡中索引器的地址
    volumes:
      - ./mcp-server/logs:/app/logs
    depends_on:
      - indexer
    networks:
      - http-mcp-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8001/health"]
      interval: 30s
      timeout: 10s
      retries: 3

networks:
  http-mcp-network:
    driver: bridge
    name: http-mcp-network

volumes:
  qdrant_data:
    driver: local
  indexer_data:
    driver: local
  mcp_logs:
    driver: local