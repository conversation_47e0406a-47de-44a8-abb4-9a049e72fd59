FRONTEND_URL=http://localhost:5173
REDIS_URL=redis://localhost:6379
# Backup directory for Redis
BACKUP_DIR=./backups

LOG_LEVEL=DEBUG
# LOG_FILE=./logs/app.log
CHAT_METRICS_FILE=./logs/chat_metrics.log

# For Celery worker
CELERY_BROKER_URL=redis://localhost:6379/0
CELERY_RESULT_BACKEND=redis://localhost:6379/1

GRAPH_API_BASE = "https://graph.microsoft.com/v1.0"
AAD_CLIENT_ID=Your client ID
AAD_CLIENT_SECRET=Your client secret
AAD_TENANT_ID=Your tenant ID
AAD_REDIRECT_URI=http://localhost:8000/auth/callback
# AAD_SCOPE=https://graph.microsoft.com/.default

### DIP
DIP_HR_BASE=https://bpm.mgwp.moxa.com/v1
X_API_KEY=Your X-API-KEY

### RAG
LIGHTRAG_HOST=http://localhost:9621

### OpenAI
OPENAI_API_BASE=https://api.openai.com/v1
OPENAI_API_KEY=Your API key
### Aliyun
ALIYUN_API_BASE=https://dashscope.aliyuncs.com/compatible-mode/v1
ALIYUN_API_KEY=Your API key
ALIYUN_TTS_BASE=https://dashscope.aliyuncs.com/api/v1/services/aigc/multimodal-generation/generation
### Google Gemini
GOOGLE_API_BASE=https://generativelanguage.googleapis.com/v1beta/openai/
GOOGLE_API_KEY=Your API key
### DeepSeek
DEEPSEEK_API_BASE=https://api.deepseek.com
DEEPSEEK_API_KEY=Your API key
### OpenRouter
OPENROUTER_API_BASE=https://openrouter.ai/api/v1
OPENROUTER_API_KEY=Your API key

### Jira & Confluence
CONFLUENCE_URL=https://wiki.moxa.com
CONFLUENCE_USERNAME=Your username
CONFLUENCE_PERSONAL_TOKEN=Your personal token
JIRA_URL=https://jira-dc.moxa.com
JIRA_USERNAME=Your username
JIRA_PERSONAL_TOKEN=Your personal token

### CWA Wether API
CWA_API_KEY=Your API key
