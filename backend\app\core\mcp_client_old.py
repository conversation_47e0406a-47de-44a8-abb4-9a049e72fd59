import asyncio
import logging
import os
from dotenv import load_dotenv
from typing import Any, List

from fastmcp import Client
from fastmcp.client.transports import StreamableHttpTransport
from app.core.mcp_tool import MCPTool

logging.basicConfig(level=logging.DEBUG)
load_dotenv()

class MCPClient:
    """MCPClient manages connections to MCP server using FastMCP."""

    def __init__(self, name: str, config: dict[str, Any]) -> None:
        self.name: str = name
        self.config: dict[str, Any] = config
        self.client: Client | None = None
        self.tools: List[MCPTool] = []
        
        # Create transport based on config
        if config.get("url"):
            # Use StreamableHttp transport for HTTP URLs
            self.transport = StreamableHttpTransport(
                url=config["url"],
                headers=config.get("headers", {})
            )
        else:
            # For local server, use the command and args
            self.transport = config["command"]
            if config.get("args"):
                self.transport = [self.transport] + config["args"]

    async def initialize(self, timeout: float = 60.0) -> None:
        """Initialize the server connection."""
        logging.info("MCPClient: Starting initialization for %s", self.name)
        
        try:
            # Create FastMCP client with timeout
            self.client = Client(
                self.transport,
                timeout=timeout
            )
            
            # Initialize connection using async context
            async with self.client:
                # Test connection
                await self.client.ping()
                
                # Fetch and store available tools
                tools_result = await self.client.list_tools()
                self.tools = [
                    MCPTool(
                        tool.name,
                        tool.description,
                        tool.inputSchema
                    ) for tool in tools_result
                ]
                
                logging.info("MCPClient: Successfully completed initialization for %s", self.name)
                logging.info("MCPClient: Available tools for %s: %s", self.name, self.tools)

        except Exception as e:
            logging.error("MCPClient: Error initializing server %s: %s", self.name, str(e))
            await self.cleanup()
            raise

    async def list_tools(self) -> List[MCPTool]:
        """List available tools from the server."""
        if not self.client:
            raise RuntimeError(f"Server {self.name} not initialized before listing tools")

        tools = await self.client.list_tools()
        return [
            MCPTool(
                tool.name,
                tool.description,
                tool.inputSchema
            ) for tool in tools
        ]

    async def execute_tool(
        self,
        tool_name: str,
        arguments: dict[str, Any],
        retries: int = 2,
        delay: float = 1.0,
    ) -> Any:
        """Execute a tool with retry mechanism."""
        if not self.client:
            raise RuntimeError(f"Server {self.name} not initialized before executing tool")

        attempt = 0
        while attempt < retries:
            try:
                logging.info("MCPClient: Executing %s...", tool_name)
                result = await self.client.call_tool(tool_name, arguments)
                return result

            except Exception as e:
                attempt += 1
                logging.warning(
                    "MCPClient: Error executing tool: %s. Attempt %s of %s.",
                    str(e), attempt, retries
                )
                if attempt < retries:
                    logging.info("MCPClient: Retrying in %s seconds...", delay)
                    await asyncio.sleep(delay)
                else:
                    logging.error("MCPClient: Max retries reached. Failing.")
                    raise

    async def cleanup(self) -> None:
        """Clean up server resources."""
        if self.client:
            self.client = None
            self.tools = []

    async def __aenter__(self):
        """Enter the async context manager."""
        await self.initialize()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Exit the async context manager."""
        await self.cleanup()