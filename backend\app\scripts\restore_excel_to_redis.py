import redis
import pandas as pd
import os
import argparse
from app.utils.logger import ColoredLogger

logger = ColoredLogger("app.scripts")

# Configuration
REDIS_URL = os.getenv("REDIS_URL", "redis://localhost:6379")

def get_redis_connection():
    """Establishes a connection to Redis."""
    try:
        r = redis.Redis.from_url(REDIS_URL)
        r.ping()
        logger.info(f"Successfully connected to Redis at {REDIS_URL}")
        return r
    except redis.exceptions.ConnectionError as e:
        logger.error(f"Could not connect to Redis: {e}")
        raise

def restore_redis_from_excel(r: redis.Redis, excel_file_path: str, flush_before_restore: bool = False):
    """Restores data from an Excel backup file to Redis."""
    try:
        df_dict = pd.read_excel(excel_file_path, sheet_name=None)
    except FileNotFoundError:
        logger.error(f"Backup file not found: {excel_file_path}")
        raise
    except Exception as e:
        logger.error(f"Error reading Excel file {excel_file_path}: {e}")
        raise

    if flush_before_restore:
        logger.info("Flushing all data from Redis before restore...")
        r.flushdb()
        logger.info("Redis flushed.")

    pipe = r.pipeline()
    
    for sheet_name, df in df_dict.items():
        logger.info(f"Restoring data from sheet: {sheet_name} ({len(df)} records)")
        for _, row in df.iterrows():
            key, value, ttl = row['Key'], row['Value'], row['TTL']
            
            pipe.delete(key)  # Ensure clean restore
            
            if sheet_name == 'strings':
                pipe.set(key, value)
            elif sheet_name == 'lists':
                pipe.rpush(key, *eval(value))  # Convert string representation back to list
            elif sheet_name == 'sets':
                pipe.sadd(key, *eval(value))  # Convert string representation back to set
            elif sheet_name == 'zsets':
                pipe.zadd(key, eval(value))  # Convert string representation back to dict {member: score}
            elif sheet_name == 'hashes':
                pipe.hmset(key, eval(value))  # Convert string representation back to dict
            
            if pd.notna(ttl) and ttl > 0:
                pipe.expire(key, int(ttl))

        pipe.execute()
        logger.info(f"Restored {len(df)} records from {sheet_name}")

    logger.info(f"Successfully restored Redis data from {excel_file_path}")

def main():
    parser = argparse.ArgumentParser(description="Restore Redis data from an Excel backup file.")
    parser.add_argument("backup_file", help="Path to the Excel backup file.")
    parser.add_argument("--flush", action="store_true", help="Flush all Redis data before restoring.")
    args = parser.parse_args()

    if not os.path.exists(args.backup_file):
        logger.error(f"Backup file does not exist: {args.backup_file}")
        return

    try:
        r = get_redis_connection()
        restore_redis_from_excel(r, args.backup_file, args.flush)
    except Exception as e:
        logger.error(f"An error occurred during the restore process: {e}")

if __name__ == "__main__":
    main()
