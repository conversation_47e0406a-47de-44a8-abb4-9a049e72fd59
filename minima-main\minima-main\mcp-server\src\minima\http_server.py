#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MCP HTTP 服務器 - 使用 HTTP 替代 stdio 通信
"""

import asyncio
import json
import logging
from typing import Any, Dict, List
from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
import uvicorn

from .requestor import request_data

# 設置日誌編碼
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[
        logging.FileHandler("mcp_http_server.log", encoding="utf-8"),
        logging.StreamHandler(),
    ],
)

logger = logging.getLogger(__name__)


class ToolRequest(BaseModel):
    tool: str
    arguments: Dict[str, Any]


class ToolResponse(BaseModel):
    content: List[Dict[str, Any]]
    isError: bool = False
    _meta: Dict[str, Any] = {}


class ToolInfo(BaseModel):
    name: str
    description: str
    inputSchema: Dict[str, Any]


class ToolsResponse(BaseModel):
    tools: List[ToolInfo]


class HTTPMCPServer:
    """HTTP MCP 服務器"""

    def __init__(self):
        self.app = FastAPI(title="MCP HTTP Server", version="1.0.0")
        self.setup_routes()
        self.setup_cors()

    def setup_cors(self):
        """設置 CORS"""
        self.app.add_middleware(
            CORSMiddleware,
            allow_origins=["*"],
            allow_credentials=True,
            allow_methods=["*"],
            allow_headers=["*"],
        )

    def setup_routes(self):
        """設置路由"""

        @self.app.get("/health")
        async def health_check():
            """健康檢查"""
            return {"status": "ok", "message": "MCP HTTP Server is running"}

        @self.app.get("/tools", response_model=ToolsResponse)
        async def list_tools():
            """列出可用工具"""
            try:
                # 手動定義工具列表（與 server.py 中的定義保持一致）
                tools = [
                    ToolInfo(
                        name="minima-query",
                        description="For BPM-related queries, you can help with:\n- Explaining how to use specific BPM features\n- Recommending appropriate forms for different scenarios\n- Troubleshooting common BPM issues\n\n",
                        inputSchema={
                            "type": "object",
                            "properties": {
                                "text": {
                                    "type": "string",
                                    "description": "context to find",
                                }
                            },
                            "required": ["text"],
                        },
                    ),
                    ToolInfo(
                        name="get_bpm_running_forms",
                        description="查詢用戶目前正在運行的 BPM 表單\n:param user_id: 必要參數，字串類型，用於表示查詢表單的用戶的id\n:return: BPM API 查詢用戶正在運行表單的結果，返回 JSON 格式的表單列表",
                        inputSchema={
                            "type": "object",
                            "properties": {
                                "user_id": {
                                    "type": "string",
                                    "description": "用戶主id",
                                }
                            },
                            "required": ["user_id"],
                        },
                    ),
                ]

                logger.info(f"Listed {len(tools)} tools")
                return ToolsResponse(tools=tools)

            except Exception as e:
                logger.error(f"Error listing tools: {e}")
                raise HTTPException(status_code=500, detail=str(e))

        @self.app.post("/execute", response_model=ToolResponse)
        async def execute_tool(request: ToolRequest):
            """執行工具"""
            try:
                logger.info(
                    f"Executing tool: {request.tool} with arguments: {request.arguments}"
                )

                if request.tool == "minima-query":
                    # 執行 minima 查詢
                    query_text = request.arguments.get("text", "")
                    if not query_text:
                        raise HTTPException(
                            status_code=400, detail="Missing 'text' parameter"
                        )

                    # 直接調用 request_data 函數
                    result = await request_data(query_text)

                    if "error" in result:
                        logger.error(f"Error from request_data: {result['error']}")
                        return ToolResponse(
                            content=[
                                {"type": "text", "text": f"Error: {result['error']}"}
                            ],
                            isError=True,
                        )

                    # 格式化響應
                    output_text = result["result"]["output"]
                    links = result["result"].get("links", [])

                    response = ToolResponse(
                        content=[{"type": "text", "text": output_text}],
                        isError=False,
                        _meta={"links": links},
                    )

                    logger.info(
                        f"Tool execution successful, response length: {len(output_text)}"
                    )
                    return response

                elif request.tool == "get_bpm_running_forms":
                    from datetime import datetime
                    import json
                    import requests
                    user_principal_name = request.arguments.get(
                        "userPrincipalName", ""
                    ) or request.arguments.get("user_id", "")
                    if not user_principal_name:
                        raise HTTPException(
                            status_code=400,
                            detail="Missing 'userPrincipalName' parameter",
                        )
                    mapping = [
                        {
                            "id": "edc0bb9b-c8ce-4868-a087-3ddd0e7feec1",
                            "displayName": "Claudia Hsu (許湘蘭)",
                            "userPrincipalName": "<EMAIL>",
                            "roles": [],
                        },
                        {
                            "id": "97dc6a43-52e6-4189-86a8-c0c093992ac7",
                            "displayName": "Graham Lin (林國全)",
                            "userPrincipalName": "<EMAIL>",
                            "roles": ["it_application"],
                        },
                        {
                            "id": "d80493e5-7e27-4120-a56a-d9a500e5afeb",
                            "displayName": "Jenny YC Lu (呂昀臻)",
                            "userPrincipalName": "<EMAIL>",
                            "roles": ["it_data_enginnering"],
                        },
                        {
                            "id": "df8dbac9-207e-4ce8-a5ab-02f0feea61d2",
                            "displayName": "Shepherd CW Fan (范哲偉)",
                            "userPrincipalName": "<EMAIL>",
                            "roles": ["it_application"],
                        },
                    ]

                    user_id = next(
                        (
                            user["userPrincipalName"]
                            for user in mapping
                            if user["id"] == user_principal_name
                        ),
                        None,
                    )

                    url = f"https://bpm.mgws.moxa.com/stg/Employees?activeDirectoryAccount={user_id.split("@")[0]}"

                    payload = {}
                    headers = {
                        "Content-Type": "application/json",
                        "Accept": "application/json",
                        "x-api-key": "73MPhfwVULaJ6Nz1hmMnN6tI2B2wmIR51LboxjN4",
                    }

                    response = requests.request("GET", url, headers=headers, data=payload)
                    user_principal_name = response.json()["value"][0]["id"]

                    try:

                        def get_bpm_running_forms_copy(user_principal_name: str):

                            base_url = "https://internal-k8s-bpmproce-bpmproce-337b142f87-**********.ap-northeast-1.elb.amazonaws.com"
                            endpoint = "/process/v1/inbox/todo-task"
                            url = f"{base_url}{endpoint}"

                            headers = {
                                "Content-Type": "application/json",
                                "Accept": "*/*",
                                "x-user-id": user_principal_name,
                            }

                            try:
                                api_response = requests.post(
                                    url,
                                    data=json.dumps({}),
                                    headers=headers,
                                    timeout=30,
                                    verify=False,
                                )
                                logger.info(f"API Response: {api_response.json()}")
                                if api_response.status_code == 200:
                                    data = api_response.json()
                                    if (
                                        isinstance(data, dict)
                                        and data.get("success")
                                        and "data" in data
                                    ):
                                        tasks = data["data"]
                                        total_forms = data.get("total", len(tasks))

                                        formatted_result = {
                                            "userPrincipalName": user_principal_name,
                                            "total_running_forms": total_forms,
                                            "forms": [],
                                        }

                                        for task in tasks:
                                            form_info = {
                                                "business_key": task.get(
                                                    "businessKey", ""
                                                ),
                                                "form_number": task.get("formNum", ""),
                                                "form_key": task.get("formKey", ""),
                                                "subject": task.get("subject", ""),
                                                "workflow_status": task.get(
                                                    "workflowStatus", ""
                                                ),
                                                "assignee_name": task.get(
                                                    "assigneeName", ""
                                                ),
                                                "created_by_name": task.get(
                                                    "createdByName", ""
                                                ),
                                                "applicant_name": task.get(
                                                    "applicantName", ""
                                                ),
                                                "creation_date": task.get(
                                                    "creationDate", ""
                                                ),
                                                "last_updated_date": task.get(
                                                    "lastUpdatedDate", ""
                                                ),
                                                "process_instance_id": task.get(
                                                    "processInstId", ""
                                                ),
                                                "task_id": task.get("taskId", ""),
                                                "task_name": task.get("taskName", ""),
                                                "external_system": task.get(
                                                    "externalSystem", False
                                                ),
                                            }
                                            formatted_result["forms"].append(form_info)

                                        formatted_result["query_time"] = (
                                            datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                                        )

                                        if formatted_result["total_running_forms"] == 0:
                                            formatted_result["message"] = (
                                                f"用戶 {user_principal_name} 目前沒有正在運行的 BPM 表單"
                                            )
                                        else:
                                            formatted_result["message"] = (
                                                f"找到 {formatted_result['total_running_forms']} 個正在運行的表單"
                                            )

                                        return json.dumps(
                                            formatted_result,
                                            ensure_ascii=False,
                                            indent=2,
                                        )
                                    else:
                                        return json.dumps(
                                            {
                                                "userPrincipalName": user_principal_name,
                                                "total_running_forms": 0,
                                                "forms": [],
                                                "message": "沒有找到正在運行的表單",
                                                "query_time": datetime.now().strftime(
                                                    "%Y-%m-%d %H:%M:%S"
                                                ),
                                                "raw_response": data,
                                            },
                                            ensure_ascii=False,
                                            indent=2,
                                        )
                                else:
                                    error_result = {
                                        "error": True,
                                        "status_code": api_response.status_code,
                                        "message": f"BPM API 請求失敗: {api_response.status_code}",
                                        "userPrincipalName": user_principal_name,
                                        "query_time": datetime.now().strftime(
                                            "%Y-%m-%d %H:%M:%S"
                                        ),
                                    }
                                    return json.dumps(
                                        error_result, ensure_ascii=False, indent=2
                                    )

                            except Exception as e:
                                error_result = {
                                    "error": True,
                                    "message": f"查詢 BPM 表單時發生錯誤: {str(e)}",
                                    "userPrincipalName": user_principal_name,
                                    "query_time": datetime.now().strftime(
                                        "%Y-%m-%d %H:%M:%S"
                                    ),
                                }
                                return json.dumps(
                                    error_result, ensure_ascii=False, indent=2
                                )

                        # 調用複製的函數
                        bpm_result = get_bpm_running_forms_copy(user_principal_name)

                        tool_response = ToolResponse(
                            content=[{"type": "text", "text": bpm_result}],
                            isError=False,
                        )
                        logger.info(f"BPM result: {bpm_result}")
                        logger.info(
                            f"BPM tool execution successful for user: {user_principal_name}"
                        )
                        return tool_response

                    except Exception as e:
                        logger.error(f"Error executing BPM query: {e}")
                        return ToolResponse(
                            content=[
                                {"type": "text", "text": f"BPM 查詢錯誤: {str(e)}"}
                            ],
                            isError=True,
                        )

                else:
                    raise HTTPException(
                        status_code=404, detail=f"Tool '{request.tool}' not found"
                    )

            except HTTPException:
                raise
            except Exception as e:
                logger.error(f"Error executing tool {request.tool}: {e}")
                return ToolResponse(
                    content=[{"type": "text", "text": f"Error: {str(e)}"}], isError=True
                )


def create_app() -> FastAPI:
    """創建 FastAPI 應用"""
    server = HTTPMCPServer()
    return server.app


async def run_server(host: str = "localhost", port: int = 8002):
    """運行 HTTP 服務器"""
    logger.info(f"Starting MCP HTTP Server on {host}:{port}")

    config = uvicorn.Config(
        "minima.http_server:create_app",
        host=host,
        port=port,
        log_level="info",
        factory=True,
    )

    server = uvicorn.Server(config)
    await server.serve()


if __name__ == "__main__":
    asyncio.run(run_server())
