<template>
    <div class="dashboard">  
      <Line v-if="chartData.datasets.length" :data="chartData" :options="chartOptions" />
      <p v-else>No data matches current filters</p>
      <v-row class="mt-4">
        <v-col cols="12" sm="12">
          <v-radio-group v-model="selectedMode" inline label="Mode">
            <v-radio v-for="mode in modes" :label="mode" :value="mode" />
          </v-radio-group>
        </v-col>
        <v-col cols="12" sm="12">
          <v-radio-group v-model="selectedMetric" inline label="Elapsed">
            <v-radio label="Total TTFR" value="total_ttfr" />
            <v-radio label="TTFR1" value="ttfr1" />
            <v-radio label="TTFT1" value="ttft1" />
            <v-radio label="RAG Retrieval Time" value="rag_retrieval_time" />
            <v-radio label="Tool Call Time" value="total_tool_call_time" />
            <v-radio label="TTFR2" value="ttfr2" />
            <v-radio label="TTFT2" value="ttft2" />
          </v-radio-group>
        </v-col>
        <v-col cols="12" sm="12">
          <v-radio-group v-model="selectedScale" inline label="Time Scale" @change="setScale">
            <v-radio v-for="scale in scales" :label="scale" :value="scale" />
          </v-radio-group>
        </v-col>
      </v-row>
    </div>
</template>  
<script setup>
import { ref, onMounted, computed, reactive, watch } from 'vue'
import { Line } from 'vue-chartjs'
import {
    Chart as ChartJS,
    LineElement,
    PointElement,
    LinearScale,
    TimeScale,
    Title,
    Tooltip,
    Legend,
    CategoryScale,
    Filler
  } from 'chart.js'
import 'chartjs-adapter-date-fns'
  
ChartJS.register(LineElement, PointElement, LinearScale, TimeScale, Title, Tooltip, Legend, CategoryScale, Filler)

const rawMetrics = ref([])
const selectedMode = ref(null)
const selectedMetric = ref('total_ttfr')
const selectedScale = ref('minute')

const modes = ['quick', 'reasoning', 'research']
const scales = ['year', 'month', 'day', 'hour', 'minute', 'second']

const chartData = computed(() => {
  if (!rawMetrics.value.length) return { labels: [], datasets: [] }

  const filtered = rawMetrics.value.filter(item =>
    (!selectedMode.value || item.mode === selectedMode.value) &&
    item[selectedMetric.value] !== undefined
  )

  const groups = {}
  for (const m of filtered) {
    const key = `${m.provider_name} – ${m.model_name}`
    if (!groups[key]) groups[key] = []
    const yVal =
      Array.isArray(m[selectedMetric.value]) ? m[selectedMetric.value][0] : m[selectedMetric.value]
    groups[key].push({ x: m.streaming_started, y: yVal })
  }

  return {
    datasets: Object.entries(groups).map(([label, data], i) => ({
      label,
      data: data.sort((a, b) => new Date(a.x) - new Date(b.x)),
      borderColor: `hsl(${(i * 60) % 360}, 70%, 50%)`,
      backgroundColor: 'transparent',
      tension: 0.3
    }))
  }
})

const chartOptions = {
  responsive: true,
  plugins: {
    legend: { position: 'bottom' },
    title: { display: true, text: 'LLM Latency by Model' }
  },
  scales: {
    x: {
      type: 'time',
      time: { unit: 'minute', tooltipFormat: 'PPpp' },
      title: { display: true, text: 'Timestamp' }
    },
    y: { title: { display: true, text: 'Seconds' } }
  }
}

const setScale = () => {
  chartOptions.scales.x.time.unit = selectedScale.value
  console.log(selectedScale.value)
}

const fetchMetrics = async () => {
    const API_URL = import.meta.env.VITE_BACKEND_API_URL || 'http://localhost:8000'
    const res = await fetch(`${API_URL}/chat/metrics`) // adjust as needed
    const metrics = await res.json()
    rawMetrics.value = metrics
}
  
onMounted(fetchMetrics)
</script>
  
<style scoped>
.dashboard {
  max-width: 1000px;
  margin: 2rem auto;
}
</style>
  