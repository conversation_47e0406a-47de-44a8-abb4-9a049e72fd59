<script setup>
import { ref } from 'vue'
import { useI18n } from 'vue-i18n'

const { t, locale } = useI18n()

const announcements = ref([
  {
    id: 1,
    title: '【四零四公告】M2025001 - 2025年股務時程表',
    releaseDate: '2025-03-11',
    pdfUrl: '/announcements/【四零四公告】M2025001 - 2025年股務時程表.pdf'
  },
  {
    id: 2,
    title: '【Inc. Announcement】M2024008_2025 Organization and Personnel Changes',
    releaseDate: '2023-07-01',
    pdfUrl: '/announcements/【Inc. Announcement】M2024008_2025 Organization and Personnel Changes.pdf'
  },
  {
    id: 3,
    title: '【Inc. Announcement】M2024007_MCC委員會成員公告',
    releaseDate: '2023-06-30',
    pdfUrl: '/announcements/【Inc. Announcement】M2024007_MCC委員會成員公告.pdf'
  },
  {
    id: 4,
    title: '【Inc. Announcement】M2024006_Announcement letter from <PERSON><PERSON>, Chairperson and Founder',
    releaseDate: '2025-03-11',
    pdfUrl: '/announcements/【Inc. Announcement】M2024006_Announcement letter from JE Hsu, Chairperson and Founder.pdf'
  },
  {
    id: 5,
    title: '【Inc. Announcement】M2024005_Organization and Personnel Changes',
    releaseDate: '2023-07-01',
    pdfUrl: '/announcements/【Inc. Announcement】M2024005_Organization and Personnel Changes.pdf'
  },
  {
    id: 6,
    title: '【Inc. Announcement】M2024004_Moxa總部大樓專案委員會',
    releaseDate: '2023-06-30',
    pdfUrl: '/announcements/【Inc. Announcement】M2024004_Moxa總部大樓專案委員會.pdf'
  },
  {
    id: 7,
    title: '【四零四公告】M2024001- 2024年股務時程表',
    releaseDate: '2025-03-11',
    pdfUrl: '/announcements/【四零四公告】M2024001- 2024年股務時程表.pdf'
  },
  {
    id: 8,
    title: '【Inc. Announcement】M2023006_2024 Organization and Personnel Changes',
    releaseDate: '2023-07-01',
    pdfUrl: '/announcements/【Inc. Announcement】M2023006_2024 Organization and Personnel Changes.pdf'
  },
  {
    id: 9,
    title: '【四零四公告】M2023002- 更新2023年股務時程表',
    releaseDate: '2023-06-30',
    pdfUrl: '/announcements/【四零四公告】M2023002- 更新2023年股務時程表.pdf'
  },
])

const formatDate = (dateString) => {
  return new Date(dateString).toLocaleDateString(locale.value, {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  })
}
</script>

<template>
  <v-card class="announcement-card" elevation="1">
    <v-card-title class="d-flex align-center">
      <v-icon icon="mdi-bullhorn" color="primary" class="mr-2"></v-icon>
      {{ t('announcements.title') }}
    </v-card-title>
    <v-card-text class="announcement-list-container">
      <v-list class="announcement-list">
        <v-list-item
          v-for="announcement in announcements"
          :key="announcement.id"
          :href="announcement.pdfUrl"
          target="_blank"
          class="announcement-item"
        >
          <v-tooltip :text="announcement.title" location="top">
            <template v-slot:activator="{ props }">
              <div v-bind="props">
                <v-list-item-title class="text-subtitle-1">
                  {{ announcement.title }}
                </v-list-item-title>
                <v-list-item-subtitle class="text-caption">
                  {{ t('announcements.released') }}: {{ formatDate(announcement.releaseDate) }}
                </v-list-item-subtitle>
              </div>
            </template>
          </v-tooltip>
          <template v-slot:append>
            <v-icon icon="mdi-file-pdf-box" color="error"></v-icon>
          </template>
        </v-list-item>
      </v-list>
    </v-card-text>
  </v-card>
</template>

<style scoped>
.announcement-card {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.announcement-list-container {
  flex-grow: 1;
  overflow: hidden;
}

.announcement-list {
  height: 400px;
  overflow-y: auto;
}

.announcement-item {
  border-bottom: 1px solid rgba(var(--v-border-color), var(--v-border-opacity));
  transition: background-color 0.2s;
}

.announcement-item:last-child {
  border-bottom: none;
}

.announcement-item:hover {
  background-color: rgba(var(--v-theme-primary), 0.1);
}
</style>