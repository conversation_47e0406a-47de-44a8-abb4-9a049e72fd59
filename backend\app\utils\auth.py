import httpx
from fastapi import HTTPException
import os
from datetime import datetime, timedelta
from app.utils.logger import ColoredLogger

logger = ColoredLogger("app.core.mcp_client")

CLIENT_ID = os.getenv("AAD_CLIENT_ID")
CLIENT_SECRET = os.getenv("AAD_CLIENT_SECRET")
TENANT_ID = os.getenv("AAD_TENANT_ID")


class TokenManager:
    def __init__(self):
        self._token = None
        self._expiry = None
        # Add buffer time before actual expiration (e.g., 5 minutes)
        self._expiry_buffer = timedelta(minutes=5)

    async def get_token(self) -> str:
        # Check if we have a valid token
        if self._token and self._expiry and datetime.now() < (self._expiry - self._expiry_buffer):
            return self._token
        
        # If not, get a new token
        return await self._fetch_new_token()
    
    async def _fetch_new_token(self) -> str:
        token_url = f"https://login.microsoftonline.com/{TENANT_ID}/oauth2/v2.0/token"
        data = {
            "client_id": CLIENT_ID,
            "client_secret": CLIENT_SECRET,
            "scope": "https://graph.microsoft.com/.default",
            "grant_type": "client_credentials"
        }

        async with httpx.AsyncClient() as client:
            response = await client.post(token_url, data=data)
            if response.status_code != 200:
                logger.error(f"Failed to get token: {response.text}")
                raise HTTPException(status_code=500, detail="Failed to get Graph API token")
            
            data = response.json()
            self._token = data["access_token"]
            # Calculate token expiry time
            self._expiry = datetime.now() + timedelta(seconds=data["expires_in"])
            logger.debug(f"New token obtained, expires at {self._expiry}")
            return self._token

    async def force_refresh(self) -> str:
        """Force refresh the token regardless of expiration time"""
        return await self._fetch_new_token()

# Create a global token manager instance
token_manager = TokenManager()

# Update the existing function to use TokenManager
async def get_graph_token() -> str:
    return await token_manager.get_token()

# Add a new function to force token refresh
async def refresh_graph_token() -> str:
    return await token_manager.force_refresh()