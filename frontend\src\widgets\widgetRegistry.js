// src/widgets/widgetRegistry.js
import { defineAsyncComponent } from 'vue'
// import Spinner from "@/components/Spinner.vue"
// import LoadError from "@/components/LoadError.vue"

export const widgetRegistry = [
    {
      i: "announcement-list",
      name: "Announcement List",
      component: () => import("@/widgets/common/AnnouncementListCard.vue"),
      restrictedRoles: [],
      minW: 3,
      minH: 12,
      static: false,
    },
    {
        i: "annual-leaves",
        name: "Annual Leaves",
        component: () => import("@/widgets/common/AnnualLeavesCard.vue"),
        restrictedRoles: [],
        minW: 2,
        minH: 4,
        static: false,
    },
    {
        i: "benefit-points",
        name: "Benefit Points",
        component: () => import("@/widgets/common/BenefitPointsCard.vue"),
        restrictedRoles: [],
        minW: 2,
        minH: 4,
        static: false,
    },
    {
        i: "company-news",
        name: "Company News",
        component: () => import("@/widgets/common/CompanyNewsCard.vue"),
        restrictedRoles: [],
        minW: 3,
        minH: 12,
        static: false,
    },
    {
        i: "countdown-timer",
        name: "Countdown Timer",
        component: () => import("@/widgets/common/CountdownTimerCard.vue"),
        restrictedRoles: [],
        minW: 2,
        minH: 4,
        static: false,
    },
    {
        i: "ehs-news",
        name: "EHS News",
        component: () => import("@/widgets/common/EHSNewsCard.vue"),
        restrictedRoles: [],
        minW: 3,
        minH: 12,
        static: false,
    },
    {
        i: "snake-game",
        name: "Snake Game",
        component: () => import("@/widgets/common/SnakeGameCard.vue"),
        restrictedRoles: [],
        minW: 4,
        minH: 13,
        static: false,
    },
    {
        i: "todo",
        name: "To-Do",
        component: () => import("@/widgets/common/TodoCard.vue"),
        restrictedRoles: [],
        minW: 2,
        minH: 4,
        static: false,
    },
    {
        i: "todo-list",
        name: "To-Do List",
        component: () => import("@/widgets/common/TodoListCard.vue"),
        restrictedRoles: [],
        minW: 4,
        minH: 11,
        static: false,
    },
    {
        i: "upcoming-company-event",
        name: "Upcoming Company Event",
        component: () => import("@/widgets/common/UpcomingCompanyEventCard.vue"),
        restrictedRoles: [],
        minW: 3,
        minH: 12,
        static: false,
    },
    {
        i: "common-apps",
        name: "Common Apps",
        component: () => import("@/widgets/common/CommonAppsCard.vue"),
        restrictedRoles: [],
        minW: 2,
        minH: 3,
        static: false,
    },
    {
        i: "weather",
        name: "Weather",
        component: () => import("@/widgets/common/WeatherCard.vue"),
        restrictedRoles: [],
        minW: 2,
        minH: 7,
        static: false,
    },
    {
        i: "contract-status",
        name: "Contract Status",
        component: () => import("@/widgets/sales/ContractStatusCard.vue"),
        restrictedRoles: ["sales"],
        minW: 3,
        minH: 11,
        static: false,
    },
    {
        i: "pipeline-overview",
        name: "Pipeline Overview",
        component: () => import("@/widgets/sales/PipelineOverviewCard.vue"),
        restrictedRoles: ["sales"],
        minW: 5,
        minH: 10,
        static: false,
    },
    {
        i: "sales-apps",
        name: "Sales Apps",
        component: () => import("@/widgets/sales/SalesAppsCard.vue"),
        restrictedRoles: ["sales"],
        minW: 3,
        minH: 3,
        static: false,
    },
    {
        i: "sales-kpi",
        name: "Sales KPI",
        component: () => import("@/widgets/sales/SalesKPICard.vue"),
        restrictedRoles: ["sales"],
        minW: 2,
        minH: 4,
        static: false,
    },
    {
        i: "sales-target-tracker",
        name: "Sales Target Tracker",
        component: () => import("@/widgets/sales/SalesTargetTrackerCard.vue"),
        restrictedRoles: ["sales"],
        minW: 4,
        minH: 13,
        static: false,
    },
    {
        i: "ticket-board",
        name: "Ticket Board",
        component: () => import("@/widgets/engineering/TicketBoardCard.vue"),
        restrictedRoles: ["engineering", "it", "quality"],
        minW: 4,
        minH: 12,
        static: false,
    },
    {
        i: "bug-count",
        name: "Bug Count",
        component: () => import("@/widgets/engineering/BugCountCard.vue"),
        restrictedRoles: ["engineering", "it", "quality"],
        minW: 2,
        minH: 4,
        static: false,
    },
    // Add more widgets here
    {
        i: "cicd-status",
        name: "CI/CD Status",
        component: () => import("@/widgets/engineering/CICDStatusCard.vue"),
        restrictedRoles: ["engineering", "it", "quality"],
        minW: 4,
        minH: 10,
        static: false,
    },
]

const widgetMap = Object.fromEntries(
    widgetRegistry.map(widget => [widget.i, widget.component])
)

export function loadWidget(name) {
    const loader = widgetMap[name];
    if (!loader) throw new Error(`Unknown widget: ${name}`);
    return defineAsyncComponent({
      loader,
      // Optional: show a spinner or placeholder while loading
    //   loadingComponent: Spinner,
    //   errorComponent: LoadError,
      delay: 200, // ms before showing spinner
      timeout: 10000 // ms before treating as error
    });
}