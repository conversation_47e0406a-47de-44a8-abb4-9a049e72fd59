from typing import Dict, List
from app.storage.user_storage import UserStorage
from app.storage.floorplan_storage import FloorplanStorage
from app.utils.logger import ColoredLogger

logger = ColoredLogger("app.services.floorplan_service")

class FloorplanService:
    def __init__(self):
        self.floorplan_storage = FloorplanStorage()
        self.user_storage = UserStorage()
        logger.info("Floorplan Service Initialized")

    def get_all_buildings(self):
        buildings = self.floorplan_storage.get_all_buildings()
        return buildings

    def get_areas(self, building_id: str, floor_id: str):
        areas = self.floorplan_storage.get_areas(building_id, floor_id)
        if not areas:
            return {}
        new_areas = {}
        for area in areas:
            new_areas[area["id"]] = {
                "name": area["name"],
                "type": area["type"],
                "employee": self.user_storage.get_user_profile_by_upn(area["employee_upn"]) if area["employee_upn"] else None,
            }
        return new_areas

    def get_user_seat(self, user_id: str):
        upn = self.user_storage.get_upn_by_aad_id(user_id)
        if upn:
            return self.floorplan_storage.get_seat_assignment(upn)
        return None
