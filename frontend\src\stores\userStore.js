import { ref } from 'vue'
import { defineStore } from 'pinia'
import { getEmployeeProfile, getUserSettings, setUserSettings, getUserPreferences, setUserPreferences } from '@/services/userService'
import { defaultLayouts } from '@/stores/layoutStore'
import { widgetRegistry } from '@/widgets/widgetRegistry'

export const users = ref([
  {id: "edc0bb9b-c8ce-4868-a087-3ddd0e7feec1", settings: { roles: []}, preferences: { theme: '', language: 'en'}, profile: { displayName: "<PERSON> (許湘蘭)", id: "edc0bb9b-c8ce-4868-a087-3ddd0e7feec1", userPrincipalName:  "<PERSON><EMAIL>"}, employee_data: {}},
  {id: "97dc6a43-52e6-4189-86a8-c0c093992ac7", settings: { roles: ["it"] }, preferences: { theme: '', language: 'en'}, profile: { displayName: "<PERSON> (林國全)", id: "97dc6a43-52e6-4189-86a8-c0c093992ac7", userPrincipalName:  "<EMAIL>"}, employee_data: {}},
  {id: "d80493e5-7e27-4120-a56a-d9a500e5afeb", settings: { roles: ["it"] }, preferences: { theme: '', language: 'en'}, profile: { displayName: "Jenny YC Lu (呂昀臻)", id: "d80493e5-7e27-4120-a56a-d9a500e5afeb", userPrincipalName:  "<EMAIL>"}, employee_data: {}},
  {id: "c07fd839-0d70-4dca-bbce-4095fcbb7f6c", settings: { roles: ["it"] }, preferences: { theme: '', language: 'en'}, profile: { displayName: "Dylan LY Wung (翁立遠)", id: "c07fd839-0d70-4dca-bbce-4095fcbb7f6c", userPrincipalName:  "<EMAIL>"}, employee_data: {}},
  {id: "d7bc817b-2117-4acd-885e-055a01464837", settings: { roles: ["administration"] }, preferences: { theme: '', language: 'en'}, profile: { displayName: "Dora PH Lung (龍寶蘅)", id: "d7bc817b-2117-4acd-885e-055a01464837", userPrincipalName:  "<EMAIL>"}, employee_data: {}},
  {id: "ec8e11ef-9d36-4a6f-b17c-6159d7e3d44b", settings: { roles: ["hr"] }, preferences: { theme: '', language: 'en'}, profile: { displayName: "May Huang (黃子瑜)", id: "ec8e11ef-9d36-4a6f-b17c-6159d7e3d44b", userPrincipalName:  "<EMAIL>"}, employee_data: {}},
  {id: "c979999f-4c0a-4048-b9f2-a17dfd26f3ec", settings: { roles: ["hr"] }, preferences: { theme: '', language: 'en'}, profile: { displayName: "Cynthia Tsai (蔡杏李)", id: "c979999f-4c0a-4048-b9f2-a17dfd26f3ec", userPrincipalName:  "<EMAIL>"}, employee_data: {}},
  {id: "bea28208-3907-4fa5-bc19-acd06a81d208", settings: { roles: ["hr"] }, preferences: { theme: '', language: 'en'}, profile: { displayName: "Carol WC Chan (詹唯靖)", id: "bea28208-3907-4fa5-bc19-acd06a81d208", userPrincipalName:  "<EMAIL>"}, employee_data: {}},
  {id: "d331604c-274f-4556-9d27-de01c6926a92", settings: { roles: ["legal"] }, preferences: { theme: '', language: 'en'}, profile: { displayName: "ShengChi SC Teng (鄧盛琦)", id: "d331604c-274f-4556-9d27-de01c6926a92", userPrincipalName:  "<EMAIL>"}, employee_data: {}},
  {id: "dece32ce-de35-4ecd-95f8-89c3f980f50d", settings: { roles: ["finance"] }, preferences: { theme: '', language: 'en'}, profile: { displayName: "Ruth Chen (陳怡茹)", id: "dece32ce-de35-4ecd-95f8-89c3f980f50d", userPrincipalName:  "<EMAIL>"}, employee_data: {}},
  {id: "ddb2d5b7-f686-4df4-8d58-e47a63732e54", settings: { roles: ["finance"] }, preferences: { theme: '', language: 'en'}, profile: { displayName: "Joseph YF Tsai (蔡育甫)", id: "ddb2d5b7-f686-4df4-8d58-e47a63732e54", userPrincipalName:  "<EMAIL>"}, employee_data: {}},
  {id: "ca6a69f5-fe21-449c-9a4f-e33f71d21dc7", settings: { roles: ["sales"] }, preferences: { theme: '', language: 'en'}, profile: { displayName: "Edward Lu (呂百舟)", id: "ca6a69f5-fe21-449c-9a4f-e33f71d21dc7", userPrincipalName:  "<EMAIL>"}, employee_data: {}},
  {id: "cad229d7-27a3-4a24-a60b-baf4d37dab4e", settings: { roles: ["sales"] }, preferences: { theme: '', language: 'en'}, profile: { displayName: "Eddy SW Lin ( 林世偉)", id: "cad229d7-27a3-4a24-a60b-baf4d37dab4e", userPrincipalName:  "<EMAIL>"}, employee_data: {}},
  {id: "e9231655-cf92-4100-96e5-edc7be7f4570", settings: { roles: ["sales"] }, preferences: { theme: '', language: 'en'}, profile: { displayName: "Jasper Liu (劉孟迪)", id: "e9231655-cf92-4100-96e5-edc7be7f4570", userPrincipalName:  "<EMAIL>"}, employee_data: {}},
  {id: "96345590-12d3-400b-8f1e-b7f154ab72ee", settings: { roles: ["sales"] }, preferences: { theme: '', language: 'en'}, profile: { displayName: "Chad Chesney", id: "96345590-12d3-400b-8f1e-b7f154ab72ee", userPrincipalName:  "<EMAIL>"}, employee_data: {}},
  {id: "cc0e352b-582b-4143-93b0-371b645214f2", settings: { roles: ["sales"] }, preferences: { theme: '', language: 'en'}, profile: { displayName: "Stanley Lu (呂玄家)", id: "cc0e352b-582b-4143-93b0-371b645214f2", userPrincipalName:  "<EMAIL>"}, employee_data: {}},
  {id: "d000c0f4-bc78-4e20-b4b7-0bede23033ad", settings: { roles: ["customer_support"] }, preferences: { theme: '', language: 'en'}, profile: { displayName: "Garry Wei (魏銘達)", id: "d000c0f4-bc78-4e20-b4b7-0bede23033ad", userPrincipalName:  "<EMAIL>"}, employee_data: {}},
  {id: "c305e4af-390d-44f9-bc91-d36abb63398d", settings: { roles: ["customer_support"] }, preferences: { theme: '', language: 'en'}, profile: { displayName: "Robin CY Lin (林政諺)", id: "c305e4af-390d-44f9-bc91-d36abb63398d", userPrincipalName:  "<EMAIL>"}, employee_data: {}},
  {id: "f84d00d4-132a-422e-8677-e8ae42eefa1f", settings: { roles: ["customer_support"] }, preferences: { theme: '', language: 'en'}, profile: { displayName: "Sky Lin (林明宗)", id: "f84d00d4-132a-422e-8677-e8ae42eefa1f", userPrincipalName:  "<EMAIL>"}, employee_data: {}},
  {id: "0d31a4ee-cae9-425b-88a0-f291fc325435", settings: { roles: ["sales"] }, preferences: { theme: '', language: 'en'}, profile: { displayName: "Jack YF Liu (刘一凡)", id: "0d31a4ee-cae9-425b-88a0-f291fc325435", userPrincipalName:  "<EMAIL>"}, employee_data: {}},
  {id: "d1433f1e-7af3-489d-a590-2c0f49833ff3", settings: { roles: ["engineering"] }, preferences: { theme: '', language: 'en'}, profile: { displayName: "Tracy Wu (吳雅惠)", id: "d1433f1e-7af3-489d-a590-2c0f49833ff3", userPrincipalName:  "<EMAIL>"}, employee_data: {}},
  {id: "f5f098f0-64c0-4790-a15d-8604a5083e50", settings: { roles: ["engineering"] }, preferences: { theme: '', language: 'en'}, profile: { displayName: "Casper Yang (楊躍輝)", id: "f5f098f0-64c0-4790-a15d-8604a5083e50", userPrincipalName:  "<EMAIL>"}, employee_data: {}},
  {id: "fd43c3d3-c755-4a80-94e1-ce2d60a35d71", settings: { roles: ["engineering"] }, preferences: { theme: '', language: 'en'}, profile: { displayName: "Li Peng (彭立先)", id: "fd43c3d3-c755-4a80-94e1-ce2d60a35d71", userPrincipalName:  "<EMAIL>"}, employee_data: {}},
  {id: "bf5a0ba2-f6f2-4d73-b941-f0566811c90b", settings: { roles: ["engineering"] }, preferences: { theme: '', language: 'en'}, profile: { displayName: "Minna Lee (李安榆)", id: "bf5a0ba2-f6f2-4d73-b941-f0566811c90b", userPrincipalName:  "<EMAIL>"}, employee_data: {}},
  {id: "f744e63b-7419-4694-a344-a2a1268f217a", settings: { roles: ["engineering"] }, preferences: { theme: '', language: 'en'}, profile: { displayName: "Michael Juan (阮明松)", id: "f744e63b-7419-4694-a344-a2a1268f217a", userPrincipalName:  "<EMAIL>"}, employee_data: {}},
  {id: "db0890a9-8628-44d2-8373-8e6d216614b4", settings: { roles: ["engineering"] }, preferences: { theme: '', language: 'en'}, profile: { displayName: "Leon Liu (劉書賢)", id: "db0890a9-8628-44d2-8373-8e6d216614b4", userPrincipalName:  "<EMAIL>"}, employee_data: {}},
  {id: "d0bd07b1-fb03-40e9-80d7-6bf445809063", settings: { roles: ["engineering"] }, preferences: { theme: '', language: 'en'}, profile: { displayName: "Bocheng BC Chen (陳柏誠)", id: "d0bd07b1-fb03-40e9-80d7-6bf445809063", userPrincipalName:  "<EMAIL>"}, employee_data: {}},
  {id: "f30e14eb-80a0-4e01-b82a-0803e85aba13", settings: { roles: ["engineering"] }, preferences: { theme: '', language: 'en'}, profile: { displayName: "Eddy Kao (高啟益)", id: "f30e14eb-80a0-4e01-b82a-0803e85aba13", userPrincipalName:  "<EMAIL>"}, employee_data: {}},
  {id: "dd6ca39c-141b-43e7-ae57-3ff643c77375", settings: { roles: ["quality"] }, preferences: { theme: '', language: 'en'}, profile: { displayName: "Emily HI Chen (陳曉怡)", id: "dd6ca39c-141b-43e7-ae57-3ff643c77375", userPrincipalName:  "<EMAIL>"}, employee_data: {}},
  {id: "d0fd3387-b7fc-4f25-8cea-c556cd0d3412", settings: { roles: ["quality"] }, preferences: { theme: '', language: 'en'}, profile: { displayName: "TK Tsai (蔡德耕)", id: "d0fd3387-b7fc-4f25-8cea-c556cd0d3412", userPrincipalName:  "<EMAIL>", roles: ["quality"]}, employee_data: {}},
  {id: "d087014a-693e-4f61-a1cd-84d18ecabc61", settings: { roles: ["quality"] }, preferences: { theme: '', language: 'en'}, profile: { displayName: "Travy PJ Weng (翁珮容)", id: "d087014a-693e-4f61-a1cd-84d18ecabc61", userPrincipalName:  "<EMAIL>"}, employee_data: {}},
  {id: "ff1bbb0a-99ea-455f-8c41-08d05f43ab71", settings: { roles: ["marketing"] }, preferences: { theme: '', language: 'en'}, profile: { displayName: "Darren Chen (陳勝正)", id: "ff1bbb0a-99ea-455f-8c41-08d05f43ab71", userPrincipalName:  "<EMAIL>"}, employee_data: {}},
  {id: "e181517a-0666-405b-a3f2-e36c41997e2d", settings: { roles: ["marketing"] }, preferences: { theme: '', language: 'en'}, profile: { displayName: "Veronica YF Yu (游雅芬)", id: "e181517a-0666-405b-a3f2-e36c41997e2d", userPrincipalName:  "<EMAIL>"}, employee_data: {}},
  {id: "ddaed33f-e7d0-40a6-a3b3-516f2f83e1ca", settings: { roles: ["marketing"] }, preferences: { theme: '', language: 'en'}, profile: { displayName: "Amber CH Chen (陳家慧)", id: "ddaed33f-e7d0-40a6-a3b3-516f2f83e1ca", userPrincipalName:  "<EMAIL>"}, employee_data: {}},
  {id: "26ed2143-9e5f-4cb4-b70a-8a7294bfa6ea", settings: { roles: ["logistics"] }, preferences: { theme: '', language: 'en'}, profile: { displayName: "Michelle Tseng (曾靜宜)", id: "26ed2143-9e5f-4cb4-b70a-8a7294bfa6ea", userPrincipalName:  "<EMAIL>"}, employee_data: {}},
  {id: "e2d4ff8d-1d13-476a-8d3a-fd758fe1677b", settings: { roles: ["logistics"] }, preferences: { theme: '', language: 'en'}, profile: { displayName: "Phoebe YW Chen (陳雨雯)", id: "e2d4ff8d-1d13-476a-8d3a-fd758fe1677b", userPrincipalName:  "<EMAIL>"}, employee_data: {}},
  {id: "cfd8eb02-0954-4e52-b7b3-fabc07d5632a", settings: { roles: ["logistics"] }, preferences: { theme: '', language: 'en'}, profile: { displayName: "Susan CH Yu (余家紅)", id: "cfd8eb02-0954-4e52-b7b3-fabc07d5632a", userPrincipalName:  "<EMAIL>"}, employee_data: {}},
  {id: "2078cc0d-df03-47f4-a171-21420246a2ef", settings: { roles: ["manufacturing"] }, preferences: { theme: '', language: 'en'}, profile: { displayName: "Shihlin Kao (高世霖)", id: "2078cc0d-df03-47f4-a171-21420246a2ef", userPrincipalName:  "<EMAIL>"}, employee_data: {}},
  {id: "c2597251-1f54-41d0-bc15-3f3fa1579730", settings: { roles: ["manufacturing"] }, preferences: { theme: '', language: 'en'}, profile: { displayName: "Fanny HF Huang (黃筱芬)", id: "c2597251-1f54-41d0-bc15-3f3fa1579730", userPrincipalName:  "<EMAIL>"}, employee_data: {}},
  {id: "c984e70f-d9cc-4254-8f3c-a42da2882f06", settings: { roles: ["logistics"] }, preferences: { theme: '', language: 'en'}, profile: { displayName: "Peggy Chen (陳端梅)", id: "c984e70f-d9cc-4254-8f3c-a42da2882f06", userPrincipalName:  "<EMAIL>"}, employee_data: {}},
  {id: "c8b0b6dc-0e16-4e06-8fa9-08e90527b720", settings: { roles: ["logistics"] }, preferences: { theme: '', language: 'en'}, profile: { displayName: "Rebecca FH Chu (朱芳慧)", id: "c8b0b6dc-0e16-4e06-8fa9-08e90527b720", userPrincipalName:  "<EMAIL>"}, employee_data: {}}
])

export const useUserStore = defineStore('user', {
  state: () => {
    const granted_points = Math.floor(Math.random() * (3000 - 1000 + 1)) + 1000;
    const remaining_points = Math.floor(Math.random() * (granted_points + 1));
    const granted_leaves = Math.floor(Math.random() * (24 - 13 + 1)) + 13;
    const remaining_leaves = Math.floor(Math.random() * (granted_leaves + 1));

    return {
      currentUser: null,
      currentEmployeeData: {},
      currentSettings: {  roles: [] },
      currentPreferences: {
        language: 'en',
        theme: 'aqua-mint',
      },
      activeLayout: [],
      users: users.value,
      benefitPoints: {
        granted: granted_points,
        remaining: remaining_points
      },
      annualLeaves: {
        granted: granted_leaves,
        remaining: remaining_leaves
      },
      todoList: [
        { id: 1, description: 'Review Q2 Financial Report', dueDate: '2023-07-20', status: false },
        { id: 2, description: 'Prepare Team Meeting Agenda', dueDate: '2023-07-21', status: false },
        { id: 3, description: 'Update Project Timeline', dueDate: '2023-07-22', status: false },
        { id: 4, description: 'Submit Expense Reports', dueDate: '2023-07-25', status: false },
        { id: 5, description: 'Complete Training Module', dueDate: '2023-07-28', status: false }
      ]
  }},
  getters: {
    getCurrentUser: (state) => state.currentUser,
    getCurrentEmployeeData: (state) => state.currentEmployeeData,
    getCurrentSettings: (state) => state.currentSettings,
    getCurrentPreferences: (state) => state.currentPreferences,
    getActiveLayout: (state) => state.activeLayout,
    getBenefitPoints: (state) => state.benefitPoints,
    getAnnualLeaves: (state) => state.annualLeaves,
    getTodoList: (state) => state.todoList,
    getTodoTasks: (state) => ({
      total: state.todoList.length,
      completed: state.todoList.filter(task => task.status).length
    })
  },
  actions: {
    setUserAndLayout(userData) {
      this.currentUser = userData.profile;
      this.currentEmployeeData = userData.employee_data || {};
      this.currentSettings = userData.settings || {};
      this.currentPreferences = userData.preferences || {};
      this.activeLayout = this.getDefaultLayout();
      localStorage.setItem('user', JSON.stringify(userData));
    },
    getDefaultLayout() {
      if (!this.currentUser) {
        // Still need to process widgets for the default layout if no user
        const firstDefaultLayout = defaultLayouts().value[0];
        if (firstDefaultLayout && firstDefaultLayout.widgets && Array.isArray(firstDefaultLayout.widgets)) {
          const processedWidgets = firstDefaultLayout.widgets
            .map(layoutWidget => {
              const registryWidget = widgetRegistry.find(rw => rw.i === layoutWidget.i);
              if (!registryWidget) {
                console.warn(`Widget with id "${layoutWidget.i}" not found in widgetRegistry.`);
                return null;
              }
              return { ...registryWidget, ...layoutWidget };
            })
            .filter(widget => widget !== null);
          return processedWidgets;
        }
        return []; // Return empty array if default layout has no widgets
      }
      
      const userRoles = this.currentSettings.roles || []

      // Attempt to find a layout that matches one of the user's roles
      let layoutForUser = defaultLayouts().value.find(layout =>
        layout.applicableRoles.length > 0 &&
        layout.applicableRoles.some(role => userRoles.includes(role))
      );

      if (!layoutForUser) {
        // If no role-specific layout is found, find a generic layout (applicableRoles is empty)
        layoutForUser = defaultLayouts().value.find(layout =>
          layout.applicableRoles.length === 0
        );
      }

      // Fallback to the very first layout if no suitable layout is found
      let baseLayout = layoutForUser || defaultLayouts().value[0];

      if (baseLayout && baseLayout.widgets && Array.isArray(baseLayout.widgets)) {
        const processedWidgets = baseLayout.widgets
          .map(layoutWidget => {
            const registryWidget = widgetRegistry.find(rw => rw.i === layoutWidget.i);

            if (!registryWidget) {
              console.warn(`Widget with id "${layoutWidget.i}" not found in widgetRegistry.`);
              return null; 
            }

            const isRestricted = registryWidget.restrictedRoles && registryWidget.restrictedRoles.length > 0;
            let isAllowed = true;

            if (isRestricted) {
              isAllowed = registryWidget.restrictedRoles.some(restrictedRole =>
                userRoles.includes(restrictedRole)
              );
            }

            if (isAllowed) {
              return { ...registryWidget, ...layoutWidget };
            }
            return null; // Widget is not allowed for this user
          })
          .filter(widget => widget !== null); // Remove nulls (widgets not found or not allowed)

        // console.log(processedWidgets); // Log the array of widgets
        return processedWidgets; // Return only the array of processed widgets
      } else {
        console.warn('baseLayout.widgets is not defined or not an array, returning empty widget array.');
        return []; // Return an empty array if no widgets are found or processed
      }
    },
    async initializeStore() {
        const storedUser = localStorage.getItem('user')
        if (storedUser) {
            const parsedUser = JSON.parse(storedUser)
            if (parsedUser && parsedUser.id) {
              const result = await getEmployeeProfile(parsedUser.id)
              if (result.success) {
                this.setUserAndLayout(result.data);
                return
              }
            }
        }
        // If no stored user or error occurred, initialize with first user
        if (this.users.length > 0) {
            const result = await getEmployeeProfile(this.users[0].id)
            if (result.success) {
                this.setUserAndLayout(result.data);
            } else {
                this.setUserAndLayout(this.users[0])
                console.error(result.error)
            }
        }
    },
    async selectUser(userId) {
        const selectedUser = this.users.find(user => user.id === userId)
        if (selectedUser) {
          this.currentUser = selectedUser.profile
          const result = await getEmployeeProfile(selectedUser.id)
          if (result.success) {
            this.setUserAndLayout(result.data);
        } else {
            console.error(result.error)
          }
        }
      },
    async reset() {
        localStorage.clear()
        await this.initializeStore()
        location.reload()
    },
    async initializeUser() {
        if (!this.currentUser && this.users.value.length > 0) {
          this.currentUser = this.users.value[0].profile
          const result = await getEmployeeProfile(this.currentUser.id)
          if (result.success) {
            this.setUserAndLayout(result.data);
        } else {
            console.error(result.error)
          }
        }
    },
    updateUserProfile(key, value) {
      if (this.currentUser) {
        this.currentUser[key] = value
      }
    },
    updateTodo(index, todo) {
      if (index > -1 && index < this.todoList.length) {
        Object.assign(this.todoList[index], todo)
      }
    },
    addTodo(todo) {
      const newTodo = {
        ...todo,
        id: this.todoList.length + 1
      }
      this.todoList.push(newTodo)
    },
    deleteTodo(index) {
      if (index > -1 && index < this.todoList.length) {
        this.todoList.splice(index, 1)
      }
    },
    toggleTodoStatus(index) {
      if (index > -1 && index < this.todoList.length) {
        this.todoList[index].status = !this.todoList[index].status
      }
    },
    updateLanguage(lang) {
      if (this.currentPreferences) {
        this.currentPreferences.language = lang
        localStorage.setItem('language', lang)
        setUserPreferences(this.currentUser.id, this.currentPreferences)
      }
    },    
    updateTheme(theme) {
      if (this.currentPreferences) {
        this.currentPreferences.theme = theme
        localStorage.setItem('theme', theme)
        setUserPreferences(this.currentUser.id, this.currentPreferences)
      }
    }
  }
})
