from datetime import datetime
import json
import logging
import os
from typing import Optional
from dotenv import load_dotenv

# Load env vars if using a .env file
load_dotenv()

RESET_SEQ = "\033[0m"
COLOR_SEQ = "\033[1;%dm"
BLACK, RED, GREEN, YELLOW, BLUE, MAGENTA, CYAN, WHITE = range(8)

COLORS = {
    'DEBUG': BLUE,
    'INFO': GREEN,
    'WARNING': YELLOW,
    'ERROR': RED,
    'CRITICAL': MAGENTA,
}

class ColoredFormatter(logging.Formatter):
    def format(self, record):
        color = COLOR_SEQ % (30 + COLORS.get(record.levelname, WHITE))
        record.levelname = f"{color}{record.levelname}{RESET_SEQ}"
        return super().format(record)

class ColoredLogger(logging.Logger):
    def __init__(self, name: str, level=logging.DEBUG, log_file: Optional[str] = None):
        super().__init__(name, level)

        if not self.handlers:
            stream_handler = logging.StreamHandler()
            stream_handler.setFormatter(
                ColoredFormatter('%(asctime)s | %(levelname)s | %(module)s | %(funcName)s | %(message)s')
            )
            self.addHandler(stream_handler)

            if log_file:
                os.makedirs(os.path.dirname(log_file), exist_ok=True)
                file_handler = logging.FileHandler(log_file, mode="a", encoding="utf-8")
                file_handler.setFormatter(logging.Formatter(
                    '%(asctime)s | %(levelname)s | %(module)s | %(funcName)s | %(message)s'
                ))
                self.addHandler(file_handler)

    def log_metrics(self, metrics: dict):
        for key, value in metrics.items():
            if isinstance(value, datetime):
                metrics[key] = value.isoformat()
        formatted = json.dumps(metrics, ensure_ascii=False)
        self.info(f"[METRIC] {formatted}")

def _parse_log_level(env_value: str) -> int:
    return getattr(logging, env_value.upper(), logging.DEBUG)

# Extract values from environment
env_log_level = _parse_log_level(os.getenv("LOG_LEVEL", "DEBUG"))
env_log_file = os.getenv("LOG_FILE")

# Singleton logger instance
_logger = ColoredLogger("EnvLogger", level=env_log_level, log_file=env_log_file)

# Top-level exports
debug = _logger.debug
info = _logger.info
warning = _logger.warning
error = _logger.error
critical = _logger.critical
exception = _logger.exception
setLevel = _logger.setLevel
get_logger = lambda: _logger
