// Utility function to parse SVG file and extract area information
export function parseAreaFromId(id, separator = '-') {
  const parts = id.split(separator)
  if (parts.length < 4) return null

  return {
    building: parts[0],
    floor: parts[1],
    type: parts[2],
    number: parts[3],
    id: id
  }
}

export async function parseSVGAreas(svgPath) {
  try {
    const response = await fetch(svgPath)
    const svgText = await response.text()
    const parser = new DOMParser()
    const svgDoc = parser.parseFromString(svgText, 'image/svg+xml')
    
    const shapes = svgDoc.querySelectorAll('polygon, rect')
    const areas = []

    shapes.forEach(shape => {
      const isRect = shape.tagName.toLowerCase() === 'rect'
      const shapeCoordinates = []

      if (isRect) {
        const x = parseFloat(shape.getAttribute('x') || 0)
        const y = parseFloat(shape.getAttribute('y') || 0)
        const width = parseFloat(shape.getAttribute('width') || 0)
        const height = parseFloat(shape.getAttribute('height') || 0)
        
        // Convert rectangle to polygon points (clockwise from top-left)
        const points = `${x},${y} ${x + width},${y} ${x + width},${y + height} ${x},${y + height}`
        shape.setAttribute('points', points)
      }
      const id = shape.getAttribute('id')
      if (!id) return

      const areaInfo = parseAreaFromId(id)
      if (!areaInfo) return

      const points = shape.getAttribute('points')
      if (!points) return

      // Convert SVG points to Leaflet coordinates while preserving image proportions
      // Split points by both spaces and commas, filter out empty strings
      const pointValues = points.trim().split(/[\s,]+/).filter(val => val.length > 0)
      
      // Ensure we have complete pairs of coordinates
      if (pointValues.length % 2 !== 0) {
        console.warn('Invalid number of point values - must have pairs of coordinates')
        return
      }
      
      const coordinates = pointValues.reduce((acc, val, index, array) => {
        if (index % 2 === 0) {
          // Get coordinates in pixels and validate input values
          const x = parseFloat(val)
          const y = array[index + 1] !== undefined ? parseFloat(array[index + 1]) : undefined
          
          // Early validation of input coordinates
          if (x === undefined || y === undefined || isNaN(x) || isNaN(y)) {
            console.warn(`Invalid SVG point values: x=${val}, y=${array[index + 1] || 'undefined'}`)
            return acc
          }
          
          // SVG dimensions from the map configuration
          const svgWidth = 2504
          const svgHeight = 2475
          
          // Validate SVG dimensions
          if (svgWidth <= 0 || svgHeight <= 0) {
            console.warn('Invalid SVG dimensions')
            return acc
          }
          
          // Convert SVG coordinates to canvas coordinates
          const canvasX = x
          const canvasY = y
          
          // Store coordinates in canvas space
          acc.push([canvasX, canvasY])
        }
        return acc
      }, [])

      // Skip areas with invalid coordinates
      if (coordinates.length === 0) return

      areas.push({
        ...areaInfo,
        coordinates,
        name: `${areaInfo.type.charAt(0).toUpperCase() + areaInfo.type.slice(1)} ${areaInfo.number}`,
        assignedEmployees: []
      })
    })

    return areas
  } catch (error) {
    console.error('Error parsing SVG:', error)
    return []
  }
}