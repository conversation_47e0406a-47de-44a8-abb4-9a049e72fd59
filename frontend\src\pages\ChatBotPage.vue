<script setup>
import { ref, reactive, onMounted, nextTick, computed, onUnmounted } from 'vue'
import { useDisplay } from 'vuetify'
import { useI18n } from 'vue-i18n'
import { useLocalStorage } from '@vueuse/core'
// import { eventBus } from "@/eventBus";
import { useUserStore } from '@/stores/userStore'
import { getAvailableTools, startNewChat, 
  getSession, getSessions, deleteAllSessions, deleteSession,
  sendChatMsg, sendStreamingChatMsg,
  getProviders, updateMessageFeedback
  } from '@/services/chatService'
import { marked } from 'marked'
import ChatInput from '@/components/ChatInput.vue'
import ModelSelector from '@/components/ModelSelector.vue'
import MessageCard from '@/components/MessageCard.vue'
import MoxartIcon from '@/assets/moxart.svg'
import ToolsDialog from '@/components/ToolsDialog.vue'
import HistoryDialog from '@/components/HistoryDialog.vue'
import { speak, cancelSpeak } from '@/utils/speak'
import { speakStream } from '@/utils/speakStream'

const { t, locale } = useI18n()

// 添加響應式判斷
const { mobile, mdAndDown } = useDisplay()

const userStore = useUserStore()

const emit = defineEmits(['close'])

const MESSAGES_PER_PAGE = 20
const currentPage = ref(1)

const state = reactive({
  chatHistory: [],
  chatInput: '',
  selectedTag: 'quick',
  isTyping: false,
  error: null,
  isRecording: false,
  isPlaying: false,
  playNextMessage: false,
  isLoadingMore: false,
  isStreamingEnabled: true,
  isToolsEnabled: true,
  showToolsDialog: false,
  showHistoryDialog: false,
  conversations: [],
  currentConversationId: useLocalStorage('moxart-current-conversation', null),
  answeringMode: 'quick', // quick, reasoning, research
  providers: {},
  providerName: '',
  modelName: '',
})

const chatContainer = ref(null)

const messageCache = new Map()

const getCachedMessage = (message) => {
  messageCache.set(message.id, {
    ...message,
    id: message.id,
    message: message.message,
    sender: message.sender,
    tools: message.tools,
    think: message.think,
    rag: message.rag,
    evaluate: message.evaluate,
    refine: message.refine,
    refs: message.refs,
    followups: message.followups,
    mermaid: message.mermaid,
    feedback: message.feedback,
    timestamp: message.timestamp
  })
  return messageCache.get(message.id)
}

const visibleMessages = computed(() => {
  const start = Math.max(0, state.chatHistory.length - (currentPage.value * MESSAGES_PER_PAGE))
  const messages = state.chatHistory.slice(start).map(getCachedMessage)
  return messages
})

const createNewChat = async() => {
  try {
    const title = t('chatbot.newConversation')
    const response = await startNewChat(userStore.currentUser.id, title)
    if (response && response.session_id) {
      const newConversation = {
        id: response.session_id,
        messages: [],
        title,
        createdAt: formatDatetime(),
      }
      state.conversations.unshift(newConversation) // TODO: remove it from local storage?
      state.currentConversationId = response.session_id
      state.chatHistory = []

      // Add welcome message to new chat
      state.chatHistory.push({
        id: Date.now(),
        message: t('chatbot.welcomeMessage', { user_name: userStore.currentUser.displayName }),
        sender: 'bot',
        tools: [],
        think: '',
        rag: '',
        evaluate: '',
        refine: '',
        refs: [],
        followups: [],
        mermaid: '',
        feedback: 'neutral',
        timestamp: formatDatetime()
      })
    } else {
      state.error = t('chatbot.errorMessage') + ' (Failed to create new chat session)'
    }
  } catch (error) {
    state.error = t('chatbot.errorMessage') + ' (Failed to create new chat session)'
  }
}

const clearHistory = async () => {
  const response = await deleteAllSessions(userStore.currentUser.id)
  if (response.success!== false) {
    state.conversations = []
    state.currentConversationId = null
    state.chatHistory = []
    state.showHistoryDialog = false
    createNewChat()
  }
}

const updateFeedback = (messageId, feedback) => {
  const messageIndex = state.chatHistory.findIndex(msg => msg.id === messageId)
  if (messageIndex !== -1) {
    // Create a new object to ensure reactivity
    state.chatHistory[messageIndex] = {
      ...state.chatHistory[messageIndex],
        feedback: feedback
    }
    updateMessageFeedback(userStore.currentUser.id, state.currentConversationId, messageId, feedback)
  }
  else {
    console.error(`updateFeedback: Message with ID ${messageId} not found in chat history.`)
  }
}

const fetchProviders = async () => {
  const response = await getProviders()
  // console.log('fetchProviders:response:', response)
  if (response.success !== false) {
    state.providers = response.data || {}
    state.providerName = Object.keys(state.providers)[0]
    state.modelName = state.providers[state.providerName].models[0].name
  }
  else {
    state.providers = {}
    state.providerName = ''
    state.modelName = ''
  }
}

const selectModel = ({ provider, model }) => {
  state.providerName = provider
  state.modelName = model
  // console.log(`selectedModel: providerName: ${state.providerName}, modelName: ${state.modelName}`)
}

const fetchConversations = async () => {
  const response = await getSessions(userStore.currentUser.id)
  // console.log('fetchConversations:response:', response)
  if (response.success !== false) {
    state.conversations = response.data || []
  }
  else {
    state.conversations = []
  }
}

const loadConversation = async (conversationId) => {
  const response = await getSession(userStore.currentUser.id, conversationId)
  if (response.success !== false) {
    state.currentConversationId = conversationId
    state.chatHistory = (response.data.messages || []).map(message => ({
      id: message.id,
      message: message.content,
      sender: message.role === 'assistant' ? 'bot' : 'user',
      tools: message.options && message.options.tools? message.options.tools : [],
      rag: message.options && message.options.rag? message.options.rag : '',
      think: message.options && message.options.think? message.options.think : '',
      evaluate: message.options && message.options.evaluate? message.options.evaluate : '',
      refine: message.options && message.options.refine? message.options.refine : '',
      refs: message.options && message.options.refs? message.options.refs : [],
      followups: message.options && message.options.followups? message.options.followups : [],
      mermaid: message.options && message.options.mermaid_code? message.options.mermaid_code : '',
      feedback: message.options && message.options.feedback? message.options.feedback : 'neutral',
      timestamp: formatDatetime(message.timestamp)
    }))
    if (state.chatHistory.length === 0) {
      // Add welcome message to new chat
      state.chatHistory.push({
        id: Date.now(),
        message: t('chatbot.welcomeMessage', { user_name: userStore.currentUser.displayName }),
        sender: 'bot',
        tools: [],
        rag: '',
        think: '',
        evaluate: '',
        refine: '',
        refs: [],
        followups: [],
        mermaid: '',
        feedback: 'neutral',
        timestamp: formatDatetime()
      })      
    }
    currentPage.value = 1
    state.showHistoryDialog = false
    nextTick(() => {
      scrollToBottom()
    })
  }
}

const deleteConversation = async (conversationId) => {
  const response = await deleteSession(userStore.currentUser.id, conversationId)
  if (response.success !== false) {
    state.conversations = state.conversations.filter(conversation => conversation.session_id !== conversationId)
    state.showHistoryDialog = false
    if (state.conversations.length > 0) {
      await loadConversation(state.conversations[0].session_id)
    }
    else {
      createNewChat()
    }
  }
}

const handleResend = (text) => {
  state.chatInput = text
  sendMsg()
}

// const handleTTS = async (text) => {
//   if (state.isPlaying) {
//     eventBus.emit("stop-audio");
//     state.isPlaying = false
//     return
//   }
//   const response = await getTTS(text);
//   if (response.success && response.data) {
//     console.log("handleTTS:response.data:", response.data); // Debugging log

//     currentAudio.value = response.data.data;
//     if (currentAudio.value) {
//       eventBus.emit("play-audio");
//       state.isPlaying = true
//     }
//   } else {
//     console.error("handleTTS: Failed to get TTS audio:", response.error);
//   }
// };


const handleSpeak = (text) => {
  if (state.isPlaying) {
    cancelSpeak()
    state.isPlaying = false
    return
  }
  state.isPlaying = true
  speak(text, locale.value === 'en'? 'en-US' : locale.value).then(() => {
      state.isPlaying = false
    }).catch(() => {
      state.isPlaying = false
    })
}

const handleCopy = async (text) => {
  try {
    await navigator.clipboard.writeText(text)
  } catch (error) {
    console.error('handleCopy: Failed to copy text:', error)
    // Fallback for older browsers or when clipboard API is not available
    const textarea = document.createElement('textarea')
    textarea.value = text
    document.body.appendChild(textarea)
    textarea.select()
    try {
      await navigator.clipboard.writeText(textarea.value)
    } catch (e) {
      console.error('handleCopy: Fallback copy failed:', e)
    }
    document.body.removeChild(textarea)
  }
}

const handleAdd = () => {}

let recognition = null

const handleMic = () => {
  state.playNextMessage = false
  const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition

  if (!SpeechRecognition) {
    alert("Your browser does not support speech recognition.")
    return
  }

  if (recognition) {
    // Already recording: stop
    recognition.stop()
    // Manually trigger onend logic
    state.isRecording = false
    recognition = null
    if (state.chatInput.trim()) {
      state.playNextMessage = true
      sendMsg()
    }
    return
  }

  recognition = new SpeechRecognition()
  recognition.lang = locale.value === 'en'? 'en-US' : locale.value
  recognition.interimResults = true
  recognition.continuous = true
  state.isRecording = true

  let finalTranscript = ''
  let pauseTimeout = null
  const PAUSE_DURATION = 2000 // 2 seconds

  recognition.onresult = (event) => {
    let interimTranscript = ''
    for (let i = event.resultIndex; i < event.results.length; i++) {
      const result = event.results[i]
      if (result.isFinal) {
        finalTranscript += result[0].transcript + ' '
      } else {
        interimTranscript += result[0].transcript
      }
    }
    state.chatInput = finalTranscript + interimTranscript

    // Reset the pause timer on every result
    if (pauseTimeout) clearTimeout(pauseTimeout)
      pauseTimeout = setTimeout(() => {
        if (recognition) {
          recognition.stop() // This will trigger onend
        }
      }, PAUSE_DURATION)
    }

  recognition.onerror = (event) => {
    console.error('handleMic:❌ Speech recognition error:', event.error)
    state.isRecording = false
    recognition = null
    if (pauseTimeout) clearTimeout(pauseTimeout)
  }

  recognition.onend = () => {
    console.log('handleMic:🛑 Voice recognition ended')
    state.isRecording = false
    recognition = null

    if (state.chatInput.trim()) {
      state.playNextMessage = true
      sendMsg()  // ⬅️ Automatically send input
    }
    if (pauseTimeout) clearTimeout(pauseTimeout)
  }

  recognition.start()
}

const handleAudio = () => {}

const scrollToBottom = async () => {
  await nextTick()
  if (chatContainer.value) {
    const lastMessage = chatContainer.value.lastElementChild
    if (lastMessage) {
      const scrollOffset = lastMessage.offsetHeight + 16 // Add some padding
      chatContainer.value.scrollTop = chatContainer.value.scrollHeight - scrollOffset
    } else {
      chatContainer.value.scrollTop = chatContainer.value.scrollHeight
    }
  }
}

const debounce = (fn, delay) => {
  let timeoutId
  return (...args) => {
    clearTimeout(timeoutId)
    timeoutId = setTimeout(() => fn.apply(null, args), delay)
  }
}

const handleScroll = debounce(async (event) => {
  const { scrollTop } = event.target
  if (scrollTop === 0 && !state.isLoadingMore && state.chatHistory.length > currentPage.value * MESSAGES_PER_PAGE) {
    state.isLoadingMore = true
    const previousHeight = chatContainer.value.scrollHeight
    const previousScrollTop = chatContainer.value.scrollTop
    
    await nextTick()
    currentPage.value++
    await nextTick()
    
    const newScrollTop = chatContainer.value.scrollHeight - previousHeight + previousScrollTop
    requestAnimationFrame(() => {
      chatContainer.value.scrollTop = newScrollTop
    })
    
    state.isLoadingMore = false
  }
}, 50)

const sendMsg = async () => {
  const query = state.chatInput.trim()
  if (!query) return
  if (state.selectedTag === 'image')  return

  // console.log(state.availableTools)
  // Add user message
  state.chatHistory.push({
    id: Date.now(),
    message: query,
    sender: 'user',
    tools: [],
    rag: '',
    think: '',
    evaluate: '',
    refine: '',
    refs: [],
    followups: [],
    mermaid: '',
    feedback: 'neutral',
    timestamp: formatDatetime()
  })

  state.chatInput = ''
  await scrollToBottom()

  state.isTyping = true // Show typing indicator
  state.error = null
  state.answeringMode = state.selectedTag
  console.log('sendMsg:answeringMode:', state.answeringMode)

  let botMessageId = Date.now()
  try {
    if (state.isStreamingEnabled) { // Streaming mode
      await sendStreamingChatMsg(
        userStore.currentUser.id,
        state.currentConversationId,
        query,
        state.availableTools,
        state.answeringMode,
        state.providerName,
        state.modelName,
        (event) => {
          let botMsg = state.chatHistory.find(msg => msg.id === botMessageId)
          if (!botMsg) {
            botMsg = {
              id: botMessageId + 1,
              message: '',
              sender: 'bot',
              tools: [],
              rag: '',
              think: '',
              evaluate: '',
              refine: '',
              refs: [],
              followups: [],
              mermaid: '',
              feedback: 'neutral',
              timestamp: formatDatetime()
            }
            state.chatHistory.push(botMsg)
          }
          if (event.type === 'message_id') {
            botMsg.id = event.data
            botMsg.timestamp = event.timestamp
            botMessageId = event.data
          }
          else if (event.type === 'content') {
            botMsg.message += event.data || ''
            botMsg.timestamp = event.timestamp
            if (state.playNextMessage) {
              speakStream(event.data, locale.value === 'en'? 'en-US' : locale.value)
            }
          }
          else if (event.type === 'tool_call_start') {
            botMsg.tools.push(event.name || '')
            botMsg.timestamp = event.timestamp
          }
          else if (event.type === 'rag') {
            botMsg.rag += event.data || ''
            botMsg.timestamp = event.timestamp
          }
          else if (event.type === 'think') {
            botMsg.think = event.data || ''
            botMsg.timestamp = event.timestamp
          }
          else if (event.type === 'evaluate') {
            botMsg.evaluate = event.data || ''
            botMsg.timestamp = event.timestamp
          }
          else if (event.type === 'refine') {
            botMsg.refine = event.data || ''
            botMsg.timestamp = event.timestamp
          }
          else if (event.type === 'refs') {
            botMsg.refs = event.data || []
            botMsg.timestamp = event.timestamp
          }
          else if (event.type === 'followups') {
            botMsg.followups = event.data || []
            botMsg.timestamp = event.timestamp
          }
          else if (event.type === 'mermaid') {
            botMsg.mermaid = event.data || ''
            botMsg.timestamp = event.timestamp
          }
          else if (event.type === 'final') {
            botMsg.message = event.data
            botMsg.timestamp = event.timestamp
            speakStream('', locale.value === 'en'? 'en-US' : locale.value, true)
            if (state.playNextMessage) {
              state.playNextMessage = false
            }
          }
          else if (event.type === 'error') {
            botMsg.message += event.data || ''
            botMsg.timestamp = event.timestamp
          }
          scrollToBottom()
        }
      )
      // console.log('sendMsg:state.chatHistory: ', state.chatHistory)
    } else {
      // Non-streaming mode
      const response = await sendChatMsg(
        userStore.currentUser.id,
        state.currentConversationId,
        query,
        state.availableTools,
        state.answeringMode,
        state.providerName,
        state.modelName
      )
      // console.log('sendMsg:response:', response)
      if (response.success) {
        state.chatHistory.push({
          id: Date.now(),
          message: response.message.final_response,
          sender: 'bot',
          tools: response.message.tools,
          rag: response.message.rag,
          think: response.message.think,
          evaluate: response.message.evaluate,
          refine: response.message.refine,
          refs: response.message.refs,
          followups: response.message.followups,
          mermaid: response.message.mermaid_code,
          feedback: response.message.feedback,
          timestamp: response.timestamp
        })
        if (state.playNextMessage) {
              handleSpeak(response.message.final_response)
              state.playNextMessage = false
            }
      } else {
        state.error = response.error
        const errorMessage = response.error.includes('data_inspection_failed')
          ? t("chatbot.contentModerationError")
          : `${t("chatbot.errorMessage")} ${response.error}`
        state.chatHistory.push({
          id: Date.now(),
          message: errorMessage,
          tools: [],
          rag: '',
          think: '',
          evaluate: '',
          refine: '',
          refs: [],
          followups: [],
          mermaid: '',
          feedback: 'neutral',
          sender: 'bot',
          timestamp: formatDatetime(response.timestamp)
        })
      }
    }
  } catch (error) {
    state.error = 'Failed to communicate with the AI service'
    state.chatHistory.push({
      id: Date.now(),
      message: t("chatbot.unexpectedError"),
      sender: 'bot',
      tools: [],
      rag: '',
      think: '',
      evaluate: '',
      refine: '',
      refs: [],
      followups: [],
      mermaid: '',
      feedback: 'neutral',
      timestamp: formatDatetime()
    })
  } finally {
    state.isTyping = false
    await scrollToBottom()
  }
}

const formatDatetime = (timestamp) => {
  const dateObj = timestamp ? new Date(timestamp) : new Date()
  return dateObj.toLocaleDateString(locale.value, {
    month: 'long',
    day: 'numeric',
    weekday: 'long',
    year: 'numeric',
    hour: 'numeric',
    minute: 'numeric',
    second: 'numeric'  
  })
}


const getChatTools = async () => {
  try {
    const response = await getAvailableTools()
    if (!response.success) {
      console.error('getChatTools: Failed to fetch tools:', response.error)
      return
    }
    state.availableTools = response.servers
    // console.log('getChatTools: ', response.servers)
  } catch (error) {
    console.error('getChatTools: Failed to fetch tools:', error)
  }
}

onMounted(async () => {
  // eventBus.on("audio-playing", () => state.isPlaying = true); 
  await getChatTools()
  await userStore.initializeStore()
  await fetchConversations()
  await fetchProviders()

  if (state.conversations.length > 0) {
    await loadConversation(state.conversations[0].session_id)
    // console.log('onMounted:state.chatHistory:', state.chatHistory)
  }
  else {
    createNewChat()
  }

  await nextTick()
  scrollToBottom()
  // console.log('onMounted:ready')
})

onUnmounted(() => {
  // eventBus.off("audio-playing", () => state.isPlaying = false);
  state.currentConversationId = null
  state.conversations = []
  state.chatHistory = []
  state.chatInput = ''
  state.isTyping = false
  state.error = null
  state.isLoadingMore = false
  state.isStreamingEnabled = true

  // console.log('onUnmounted:ready')
})

</script>

<template>
  <v-card 
    :min-width="mdAndDown ? '100%' : '800px'" 
    class="chatbot-card mx-auto d-flex flex-column" 
    :style="mdAndDown ? 'height: 95vh; width: 100vw; margin: 0; border-radius: 0;' : 'height: 95vh;' "
    color="background"
    >
    <v-card-title class="chat-title d-flex align-center text-h5 elevation-0">
      <img :src="MoxartIcon" alt="Moxa" class="mr-2" 
        :style="mdAndDown ? 'width: 24px; height: 24px;' : 'width: 32px; height: 32px;'" />
      <span :class="mdAndDown ? 'text-h6' : 'text-h5'">Moxart</span>
      <ModelSelector 
        :providerName="state.providerName" 
        :modelName="state.modelName"
        :providers="state.providers"
        @selectModel="selectModel"
      />

      <v-spacer></v-spacer>
      <!-- 在手機模式下使用下拉選單整合按鈕 -->
      <template v-if="mdAndDown">
        <v-menu>
          <template v-slot:activator="{ props }">
            <v-btn
              icon="mdi-dots-vertical"
              v-bind="props"
              size="small"
            ></v-btn>
          </template>
          <v-list>
            <v-list-item @click="state.showHistoryDialog = true">
              <template v-slot:prepend>
                <v-icon>mdi-history</v-icon>
              </template>
              <v-list-item-title>{{ t('chatbot.showHistory') }}</v-list-item-title>
            </v-list-item>
            <v-list-item @click="state.showToolsDialog = true">
              <template v-slot:prepend>
                <v-icon>{{ state.isToolsEnabled ? 'mdi-toolbox' : 'mdi-toolbox-outline' }}</v-icon>
              </template>
              <v-list-item-title>{{ state.isToolsEnabled ? t('chatbot.disableTools') : t('chatbot.enableTools') }}</v-list-item-title>
            </v-list-item>
            <v-list-item @click="createNewChat">
              <template v-slot:prepend>
                <v-icon>mdi-plus</v-icon>
              </template>
              <v-list-item-title>{{ t('chatbot.newChat') }}</v-list-item-title>
            </v-list-item>
            <!-- <v-list-item @click="state.isStreamingEnabled = !state.isStreamingEnabled">
              <template v-slot:prepend>
                <v-icon>{{ state.isStreamingEnabled ? 'mdi-lightning-bolt' : 'mdi-lightning-bolt-outline' }}</v-icon>
              </template>
              <v-list-item-title>{{ state.isStreamingEnabled ? t('chatbot.disableStreaming') : t('chatbot.enableStreaming') }}</v-list-item-title>
            </v-list-item> -->
          </v-list>
        </v-menu>
      </template>
      <!-- 桌面模式保持原有按鈕佈局 -->
      <template v-else>
        <v-btn
          :icon="state.isToolsEnabled ? 'mdi-toolbox' : 'mdi-toolbox-outline'"
          :color="state.isToolsEnabled? 'primary' : undefined"
          class="mr-2" 
          @click="state.showToolsDialog = true"
          :title="state.isToolsEnabled? t('chatbot.disableTools') : t('chatbot.enableTools')"
          size="small"
        ></v-btn>
        <v-btn
          icon="mdi-history"
          class="mr-2"
          @click="state.showHistoryDialog = true"
          :title="t('chatbot.showHistory')"
          size="small"
        ></v-btn>
        <v-btn
          icon="mdi-plus"
          class="mr-2"
          @click="createNewChat"
          :title="t('chatbot.newChat')"
          size="small"
        ></v-btn>
        <!-- <v-btn
          :icon="state.isStreamingEnabled ? 'mdi-lightning-bolt' : 'mdi-lightning-bolt-outline'"
          :color="state.isStreamingEnabled ? 'primary' : undefined"
          class="mr-2"
          @click="state.isStreamingEnabled = !state.isStreamingEnabled"
          :title="state.isStreamingEnabled ? t('chatbot.disableStreaming') : t('chatbot.enableStreaming')"
          size="small"
        ></v-btn> -->
      </template>
    </v-card-title>
    <!-- 調整聊天記錄區域的內邊距 -->
    <v-card-text
      ref="chatContainer"
      class="chat-history pa-4 flex-grow-1 overflow-y-auto"
      :class="mdAndDown ? 'pa-2' : 'pa-4'"
      @scroll="handleScroll"
    >
      <div v-if="state.isLoadingMore" class="text-center pa-2">
        <v-progress-circular indeterminate size="24" />
      </div>
      <MessageCard
        :visibleMessages="visibleMessages"
        :userStore="userStore"
        :MoxartIcon="MoxartIcon"
        :isPlaying="state.isPlaying"
        :t="t"
        :marked="marked"
        :handleSpeak="handleSpeak"
        :handleCopy="handleCopy"
        :handleResend="handleResend"
        :updateFeedback="updateFeedback"
      />
      <div v-if="state.isTyping" class="d-flex mb-4">
        <div class="d-flex align-start">
          <v-avatar class="mx-2" size="32">
            <img :src="MoxartIcon" alt="Moxart" style="width: 20px; height: 20px;" />
          </v-avatar>
          <v-card
            color="surface-lighten-1"
            class="pa-3 rounded-lg typing-indicator elevation-2"
          >
            <div class="typing-dots">
              <span></span>
              <span></span>
              <span></span>
            </div>
          </v-card>
        </div>
      </div>
    </v-card-text>

    <ChatInput
      v-model="state.chatInput"
      :placeholder="t('chatbot.inputPlaceholder')"
      :isRecording="state.isRecording"
      :externalTags="[
        { id: 'quick', name: t('chatbot.tagQuick')}, 
        { id: 'think', name: t('chatbot.tagReason')}, 
        { id: 'research', name: t('chatbot.tagDeepResearch')}, 
        { id: 'image', name: t('chatbot.tagCreateImage')}
      ]"
      :selectedTag="state.selectedTag"
      @update:selectedTag="state.selectedTag = $event; console.log('selectedTag:', $event)"
      @send="sendMsg"
      @add="handleAdd"
      @mic="handleMic"
      @audio="handleAudio"
      class="flex-shrink-0"
    />
    <ToolsDialog
      :state="state"
      :t="t"
    />
    <HistoryDialog
      :state="state"
      :t="t"
      :loadConversation="loadConversation"
      :clearHistory="clearHistory"
      :deleteConversation="deleteConversation"
      :formatDatetime="formatDatetime"
    />
</v-card>
</template>

<style scoped>
/* 為手機模式添加樣式 */
@media (max-width: 960px) {
  .message-card {
    max-width: 85vw !important;
  }
  
  .markdown-content pre {
    max-width: 80vw;
    overflow-x: auto;
  }
}

.markdown-content :deep(p) {
  margin: 0.5em 0;
}

.markdown-content :deep(h1),
.markdown-content :deep(h2),
.markdown-content :deep(h3),
.markdown-content :deep(h4),
.markdown-content :deep(h5),
.markdown-content :deep(h6) {
  margin: 0.5em 0;
  font-weight: bold;
}

.markdown-content :deep(ul),
.markdown-content :deep(ol) {
  margin: 0.5em 0;
  padding-left: 1.5em;
}

.markdown-content :deep(code) {
  background-color: rgba(0, 0, 0, 0.05);
  padding: 0.2em 0.4em;
  border-radius: 3px;
  font-family: monospace;
}

.markdown-content :deep(pre code) {
  display: block;
  padding: 1em;
  overflow-x: auto;
  background-color: rgba(0, 0, 0, 0.05);
}

.markdown-content :deep(a) {
  color: rgb(var(--v-theme-on-surface));
  text-decoration: underline;
  font-style: italic;
}

.markdown-content :deep(a:hover) {
  text-decoration: underline;
}

.markdown-content :deep(blockquote) {
  margin: 0.5em 0;
  padding-left: 1em;
  border-left: 4px solid rgba(0, 0, 0, 0.1);
  color: rgba(0, 0, 0, 0.7);
}

.message-card {
  position: relative;
  transition: all 0.3s ease;
}

.typing-dots {
  display: flex;
  gap: 4px;
  padding: 4px 8px;
  background-color: rgb(var(--v-theme-surface));
  border-radius: 12px;
}

.typing-dots span {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: rgb(var(--v-theme-on-surface));
  animation: typing 1s infinite ease-in-out;
}

.typing-dots span:nth-child(1) { animation-delay: 0.2s; }
.typing-dots span:nth-child(2) { animation-delay: 0.4s; }
.typing-dots span:nth-child(3) { animation-delay: 0.6s; }

@keyframes typing {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-5px); }
}

.opacity-medium {
  opacity: 0.7;
}

.chatbot-card {
  height: 100%;
  display: flex;
  flex-direction: column;
  margin: 0 !important;
  border-radius: 0;
  width: 100% !important;
  max-width: 100% !important;
  overflow: hidden; /* Prevent scrolling inside the chatbot card */
}

.chat-title {
    position: sticky;
    top: 0;
    z-index: 2;
    flex-shrink: 0; /* Prevent shrinking */
    background-color: var(--v-theme-surface); /* Use the theme's surface color */
}

.chat-history {
  flex: 1 1 auto; /* Allow the chat history to grow and shrink */
  overflow-y: auto;
  padding-bottom: 1rem;
  /* height: inherit; */
}

/* Remove the popup-specific styles or modify them */
:deep(.v-overlay-container) .chatbot-card {
  height: 80vh;
  margin: auto !important;
  border-radius: 4px;
  width: 800px !important;
  max-width: 90vw !important;
}

.hover-card:hover {
  background-color: rgb(var(--v-theme-secondary)) !important; /* Change to desired hover color */
  color: rgb(var(--v-theme-on-secondary)) !important; /* Adjust text color */
  cursor: pointer;
}

.wrap-text {
  white-space: normal !important;
  word-wrap: break-word;
  text-align: center;
}

</style>