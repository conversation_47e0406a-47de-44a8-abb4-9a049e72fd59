// utils/speakStream.ts
let currentUtterance = null
let buffer = ''
let speaking = false

export function speakStream(textChunk, lang = 'en-US', flush = false) {
  if (!textChunk && !flush) return

  buffer += textChunk

  if (flush && buffer) {
    speakNow(buffer, lang)
    buffer = ''
    return
  }

  // If not currently speaking, speak the buffer so far
  if (!speaking && buffer.trim()) {
    speakNow(buffer, lang)
    buffer = ''
  }
}

function speakNow(text, lang) {
  speaking = true
  currentUtterance = new SpeechSynthesisUtterance(text)
  currentUtterance.lang = lang
  currentUtterance.onend = () => (speaking = false)
  speechSynthesis.speak(currentUtterance)
}
