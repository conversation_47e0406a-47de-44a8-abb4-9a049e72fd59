import logging
import json
import sys
from typing import Any, Dict, Optional, Union
from .json_fix import fix_json_string, safe_json_loads

logger = logging.getLogger(__name__)

def monkey_patch_json():
    """
    替換標準庫中的json.loads函數，使其能夠處理Unicode問題
    """
    original_loads = json.loads
    
    def patched_loads(s, *args, **kwargs):
        if isinstance(s, str):
            try:
                return original_loads(s, *args, **kwargs)
            except json.JSONDecodeError as e:
                logger.debug(f"JSON decode error in patched_loads: {e}")
                fixed = fix_json_string(s)
                if fixed != s:
                    return original_loads(fixed, *args, **kwargs)
                raise
        return original_loads(s, *args, **kwargs)
    
    # 替換標準庫函數
    json.loads = patched_loads
    logger.info("Successfully patched json.loads")

def patch_mcp():
    """
    修補MCP庫以處理JSON解析問題
    """
    try:
        # 首先修補json模塊
        monkey_patch_json()
        
        # 然後修補JSONRPCMessage
        from mcp.types import JSONRPCMessage
        original_model_validate_json = JSONRPCMessage.model_validate_json
        
        def patched_model_validate_json(json_data, **kwargs):
            logger.debug(f"Validating JSON data: {json_data[:100]}...")
            
            if isinstance(json_data, str):
                # 處理潛在的編碼問題
                try:
                    # 使用我們的修復函數
                    fixed_json = fix_json_string(json_data)
                    return original_model_validate_json(fixed_json, **kwargs)
                except Exception as e:
                    logger.error(f"Failed to validate JSON: {e}")
                    # 如果仍然失敗，嘗試更激進的修復
                    try:
                        # 移除所有可能導致問題的Unicode轉義序列
                        import re
                        cleaned = re.sub(r'\\u[0-9a-f]{4}', '?', json_data, flags=re.IGNORECASE)
                        return original_model_validate_json(cleaned, **kwargs)
                    except Exception as inner_e:
                        logger.error(f"Failed to fix JSON with aggressive cleaning: {inner_e}")
                        raise
            
            return original_model_validate_json(json_data, **kwargs)
        
        # 應用補丁
        JSONRPCMessage.model_validate_json = patched_model_validate_json
        logger.info("Successfully patched JSONRPCMessage.model_validate_json")
        
        # 修補MCP庫中的其他JSON處理函數
        try:
            from mcp.shared.session import Session
            original_process = None
            
            # 尋找處理消息的方法
            for attr_name in dir(Session):
                if attr_name.startswith('_process') and callable(getattr(Session, attr_name)):
                    method = getattr(Session, attr_name)
                    if 'message' in method.__code__.co_varnames:
                        original_process = method
                        break
            
            if original_process:
                def patched_process(self, message, *args, **kwargs):
                    logger.debug(f"Processing message: {message[:100] if isinstance(message, str) else message}")
                    
                    if isinstance(message, str):
                        # 嘗試修復消息
                        fixed_message = fix_json_string(message)
                        if fixed_message != message:
                            logger.debug(f"Fixed message: {fixed_message[:100]}")
                            return original_process(self, fixed_message, *args, **kwargs)
                    
                    return original_process(self, message, *args, **kwargs)
                
                # 應用補丁
                setattr(Session, original_process.__name__, patched_process)
                logger.info(f"Successfully patched Session.{original_process.__name__}")
        
        except Exception as e:
            logger.error(f"Failed to patch Session methods: {e}")
        
    except Exception as e:
        logger.exception(f"Failed to patch MCP: {e}")
