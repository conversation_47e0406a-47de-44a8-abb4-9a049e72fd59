<script setup>
import { ref } from 'vue'
import { useI18n } from 'vue-i18n';

const { t, locale } = useI18n();

// const props = defineProps({
//   companyNews: {
//     type: Array,
//     required: true,
//     default: () => []
//   }
// })

const companyNews = ref([
  {
    id: 1,
    title: 'Q2 Financial Results',
    date: '2023-07-15',
    content: 'Our company has achieved exceptional growth in Q2 2023, surpassing market expectations.',
    image: 'https://picsum.photos/800/400?random=1'
  },
  {
    id: 2,
    title: 'New Office Opening',
    date: '2023-07-10',
    content: 'We are excited to announce the opening of our new office in Singapore.',
    image: 'https://picsum.photos/800/400?random=2'
  },
  {
    id: 3,
    title: 'Employee Wellness Program',
    date: '2023-07-05',
    content: 'Launching our new employee wellness program focused on mental and physical health.',
    image: 'https://picsum.photos/800/400?random=3'
  }
])

const formatDate = (dateString) => {
  return new Date(dateString).toLocaleDateString(locale.value, {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  })
}
</script>

<template>
  <v-card height="100%" class="company-news-card">
    <v-card-title class="d-flex align-center">
      <v-icon icon="mdi-newspaper" color="primary" class="mr-2"></v-icon>
      {{ t('companyNews.title') }}
    </v-card-title>
    <v-card-text>
      <v-carousel
        cycle
        interval="5000"
        height="400"
        hide-delimiter-background
        show-arrows="hover"
      >
        <v-carousel-item
          v-for="news in companyNews"
          :key="news.title"
        >
          <v-sheet height="100%" class="d-flex flex-column">
            <v-img
              :src="news.image"
              height="300"
              cover
              class="flex-grow-0"
            ></v-img>
            <v-sheet class="pa-4">
              <div class="d-flex align-center justify-space-between mb-2">
                <div class="text-h6">{{ news.title }}</div>
                <div class="text-caption text-grey">{{ formatDate(news.date) }}</div>
              </div>
              <div class="text-body-1">{{ news.content }}</div>
            </v-sheet>
          </v-sheet>
        </v-carousel-item>
      </v-carousel>
    </v-card-text>
  </v-card>
</template>