from app.services import user_service
from app.storage import user_storage


def reconcile_user_oids(access_token: str):
    # Fetch all current user OIDs from the storage
    current_oids = set(user_storage.get_all_oids())

    oids = set(user_service.get_user_list(access_token))

    # Identify OIDs to add and remove
    oids_to_add = oids - current_oids
    oids_to_remove = current_oids - oids

    # Update Redis set accordingly
    if oids_to_add:
        user_storage.add_oids(oids_to_add)
    if oids_to_remove:
        user_storage.remove_oids(oids_to_remove)
