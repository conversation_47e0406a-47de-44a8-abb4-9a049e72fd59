import os
import uuid
import torch
import logging
import time
from dataclasses import dataclass
from typing import List, Dict
from pathlib import Path

from qdrant_client import QdrantClient
from langchain_qdrant import QdrantVectorStore
from langchain_huggingface import HuggingFaceEmbeddings
from qdrant_client.http.models import (
    Distance,
    VectorParams,
    Filter,
    FieldCondition,
    MatchValue,
)
from langchain.text_splitter import RecursiveCharacterTextSplitter

from langchain_community.document_loaders import (
    TextLoader,
    CSVLoader,
    Docx2txtLoader,
    UnstructuredExcelLoader,
    PyMuPDFLoader,
    UnstructuredPowerPointLoader,
)

from storage import MinimaStore, IndexingStatus

logger = logging.getLogger(__name__)


@dataclass
class Config:
    EXTENSIONS_TO_LOADERS = {
        ".pdf": PyMuPDFLoader,
        ".pptx": UnstructuredPowerPointLoader,
        ".ppt": UnstructuredPowerPointLoader,
        ".xls": UnstructuredExcelLoader,
        ".xlsx": UnstructuredExcelLoader,
        ".docx": Docx2txtLoader,
        ".doc": Docx2txtLoader,
        ".txt": TextLoader,
        ".md": TextLoader,
        ".csv": CSVLoader,
    }

    DEVICE = torch.device(
        "mps"
        if torch.backends.mps.is_available()
        else "cuda" if torch.cuda.is_available() else "cpu"
    )

    START_INDEXING = os.environ.get("START_INDEXING")
    LOCAL_FILES_PATH = os.environ.get("LOCAL_FILES_PATH")
    CONTAINER_PATH = os.environ.get("CONTAINER_PATH")
    QDRANT_COLLECTION = "mnm_storage"
    QDRANT_BOOTSTRAP = "qdrant"
    EMBEDDING_MODEL_ID = os.environ.get("EMBEDDING_MODEL_ID")
    EMBEDDING_SIZE = os.environ.get("EMBEDDING_SIZE")

    CHUNK_SIZE = 500
    CHUNK_OVERLAP = 200


class Indexer:
    def __init__(self):
        self.config = Config()
        self.qdrant = self._initialize_qdrant()
        self.embed_model = self._initialize_embeddings()
        self.document_store = self._setup_collection()
        self.text_splitter = self._initialize_text_splitter()

    def _initialize_qdrant(self) -> QdrantClient:
        return QdrantClient(host=self.config.QDRANT_BOOTSTRAP)

    def _initialize_embeddings(self) -> HuggingFaceEmbeddings:
        return HuggingFaceEmbeddings(
            model_name=self.config.EMBEDDING_MODEL_ID,
            model_kwargs={"device": self.config.DEVICE},
            encode_kwargs={"normalize_embeddings": False},
        )

    def _initialize_text_splitter(self) -> RecursiveCharacterTextSplitter:
        return RecursiveCharacterTextSplitter(
            chunk_size=self.config.CHUNK_SIZE, chunk_overlap=self.config.CHUNK_OVERLAP
        )

    def _setup_collection(self) -> QdrantVectorStore:
        if not self.qdrant.collection_exists(self.config.QDRANT_COLLECTION):
            self.qdrant.create_collection(
                collection_name=self.config.QDRANT_COLLECTION,
                vectors_config=VectorParams(
                    size=self.config.EMBEDDING_SIZE, distance=Distance.COSINE
                ),
            )
        self.qdrant.create_payload_index(
            collection_name=self.config.QDRANT_COLLECTION,
            field_name="fpath",
            field_schema="keyword",
        )
        return QdrantVectorStore(
            client=self.qdrant,
            collection_name=self.config.QDRANT_COLLECTION,
            embedding=self.embed_model,
        )

    def _create_loader(self, file_path: str):
        file_extension = Path(file_path).suffix.lower()
        loader_class = self.config.EXTENSIONS_TO_LOADERS.get(file_extension)

        if not loader_class:
            raise ValueError(f"Unsupported file type: {file_extension}")

        return loader_class(file_path=file_path)

    def _process_file(self, loader) -> List[str]:
        try:
            documents = loader.load_and_split(self.text_splitter)
            if not documents:
                logger.warning(f"No documents loaded from {loader.file_path}")
                return []

            for doc in documents:
                doc.metadata["file_path"] = loader.file_path

            uuids = [str(uuid.uuid4()) for _ in range(len(documents))]
            ids = self.document_store.add_documents(documents=documents, ids=uuids)

            logger.info(
                f"Successfully processed {len(ids)} documents from {loader.file_path}"
            )
            return ids

        except Exception as e:
            logger.error(f"Error processing file {loader.file_path}: {str(e)}")
            return []

    def index(self, message: Dict[str, any]) -> None:
        start = time.time()
        path, file_id, last_updated_seconds = (
            message["path"],
            message["file_id"],
            message["last_updated_seconds"],
        )
        logger.info(f"Processing file: {path} (ID: {file_id})")
        indexing_status: IndexingStatus = MinimaStore.check_needs_indexing(
            fpath=path, last_updated_seconds=last_updated_seconds
        )
        if indexing_status != IndexingStatus.no_need_reindexing:
            logger.info(f"Indexing needed for {path} with status: {indexing_status}")
            try:
                if indexing_status == IndexingStatus.need_reindexing:
                    logger.info(f"Removing {path} from index storage for reindexing")
                    self.remove_from_storage(files_to_remove=[path])
                loader = self._create_loader(path)
                ids = self._process_file(loader)
                if ids:
                    logger.info(f"Successfully indexed {path} with IDs: {ids}")
            except Exception as e:
                logger.error(f"Failed to index file {path}: {str(e)}")
        else:
            logger.info(
                f"Skipping {path}, no indexing required. timestamp didn't change"
            )
        end = time.time()
        logger.info(f"Processing took {end - start} seconds for file {path}")

    def purge(self, message: Dict[str, any]) -> None:
        existing_file_paths: list[str] = message["existing_file_paths"]
        files_to_remove = MinimaStore.find_removed_files(
            existing_file_paths=set(existing_file_paths)
        )
        if len(files_to_remove) > 0:
            logger.info(f"purge processing removing old files {files_to_remove}")
            self.remove_from_storage(files_to_remove)
        else:
            logger.info("Nothing to purge")

    def remove_from_storage(self, files_to_remove: list[str]):
        filter_conditions = Filter(
            must=[
                FieldCondition(key="fpath", match=MatchValue(value=fpath))
                for fpath in files_to_remove
            ]
        )
        response = self.qdrant.delete(
            collection_name=self.config.QDRANT_COLLECTION,
            points_selector=filter_conditions,
            wait=True,
        )
        logger.info(
            f"Delete response for {len(files_to_remove)} for files: {files_to_remove} is: {response}"
        )

    def find(self, query: str, file_filter: str = "") -> Dict[str, any]:
        try:
            logger.info(f"Searching for: {query}")
            if file_filter:
                logger.info(f"File filter: {file_filter}")

            # Execute similarity search, get more results for file selection analysis
            found = self.document_store.search(query, search_type="similarity", k=30)

            if not found:
                logger.info("No results found")
                return {"links": set(), "output": ""}

            # RAG-level single file selection logic
            file_groups = {}

            # Group search results by file
            for item in found:
                file_path = item.metadata.get("file_path", "")
                if file_path not in file_groups:
                    file_groups[file_path] = []
                file_groups[file_path].append(item)

            logger.info(f"Found results from {len(file_groups)} different files")

            # If file filter exists, prioritize matching files
            if file_filter:
                filtered_groups = {}
                for file_path, items in file_groups.items():
                    file_name = os.path.basename(file_path).lower()
                    if (
                        file_filter.lower() in file_name
                        or file_filter.lower() in file_path.lower()
                    ):
                        filtered_groups[file_path] = items

                if filtered_groups:
                    file_groups = filtered_groups
                    logger.info(f"Filtered to {len(file_groups)} files matching filter")

            # Select best file: prioritize file with most relevant results
            best_file = max(file_groups.keys(), key=lambda f: len(file_groups[f]))
            selected_items = file_groups[best_file]

            # Limit result count, select top 8 most relevant results
            max_results = 8
            if len(selected_items) > max_results:
                selected_items = selected_items[:max_results]

            logger.info(f"RAG selected single file: {os.path.basename(best_file)}")
            logger.info(
                f"Available files: {[os.path.basename(f) for f in file_groups.keys()]}"
            )
            logger.info(f"Returning {len(selected_items)} results from selected file")

            # Build response
            links = set()
            results = []

            for item in selected_items:
                path = item.metadata["file_path"].replace(
                    self.config.CONTAINER_PATH, self.config.LOCAL_FILES_PATH
                )
                links.add(f"file://{path}")
                results.append(item.page_content)

            output = {
                "links": links,
                "output": ". ".join(results),
                "source_file": os.path.basename(best_file),
                "total_files_found": len(file_groups),
                "selected_results_count": len(selected_items),
            }

            return output

        except Exception as e:
            logger.error(f"Search failed: {str(e)}")
            return {"error": "Unable to find anything for the given query"}

    def embed(self, query: str):
        return self.embed_model.embed_query(query)
