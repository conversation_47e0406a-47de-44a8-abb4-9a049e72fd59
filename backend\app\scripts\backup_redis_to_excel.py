# To run: 
# `python app\scripts\backup_redis_to_excel.py`
#
import redis
import os
import pandas as pd
from datetime import datetime
from app.utils.logger import ColoredLogger

logger = ColoredLogger("app.scripts")

# Configuration
REDIS_URL = os.getenv("REDIS_URL", "redis://localhost:6379")
BACKUP_DIR = os.getenv("BACKUP_DIR", "./backups")

def get_redis_connection():
    """Establishes a connection to Redis."""
    try:
        r = redis.Redis.from_url(REDIS_URL)
        r.ping()
        logger.info(f"Successfully connected to Redis at {REDIS_URL}")
        return r
    except redis.exceptions.ConnectionError as e:
        logger.error(f"Could not connect to Redis: {e}")
        raise

def backup_redis_to_excel(r: redis.Redis, excel_file_path: str):
    """Exports Redis data to an Excel file with multiple sheets."""
    data_dict = {
        'strings': [],
        'lists': [],
        'sets': [],
        'zsets': [],
        'hashes': []
    }

    keys = r.keys('*')
    logger.info(f"Found {len(keys)} keys to backup.")

    for key_bytes in keys:
        key = key_bytes.decode('utf-8')
        key_type = r.type(key).decode('utf-8')
        ttl = r.ttl(key)

        if key_type == 'string':
            data_dict['strings'].append([key, r.get(key).decode('utf-8'), ttl])
        elif key_type == 'list':
            data_dict['lists'].append([key, r.lrange(key, 0, -1), ttl])
        elif key_type == 'set':
            data_dict['sets'].append([key, list(r.smembers(key)), ttl])
        elif key_type == 'zset':
            data_dict['zsets'].append([key, list(r.zrange(key, 0, -1, withscores=True)), ttl])
        elif key_type == 'hash':
            data_dict['hashes'].append([key, r.hgetall(key), ttl])

    # Create a Pandas Excel writer
    with pd.ExcelWriter(excel_file_path, engine='openpyxl') as writer:
        for key_type, records in data_dict.items():
            df = pd.DataFrame(records, columns=['Key', 'Value', 'TTL'])
            df.to_excel(writer, sheet_name=key_type, index=False)

    logger.info(f"Successfully saved Redis data to {excel_file_path}")

def main():
    if not os.path.exists(BACKUP_DIR):
        os.makedirs(BACKUP_DIR)
        logger.info(f"Created backup directory: {BACKUP_DIR}")

    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    excel_file_name = f"redis_backup_{timestamp}.xlsx"
    excel_file_path = os.path.join(BACKUP_DIR, excel_file_name)

    try:
        r = get_redis_connection()
        backup_redis_to_excel(r, excel_file_path)
    except Exception as e:
        logger.error(f"An error occurred during the backup process: {e}")

if __name__ == "__main__":
    main()
