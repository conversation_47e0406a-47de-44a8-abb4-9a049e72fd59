<script setup>
import { ref } from 'vue'
import { useI18n } from 'vue-i18n';
import { useUserStore } from '@/stores/userStore';

const { t, locale } = useI18n();
const userStore = useUserStore();
</script>


<template>
  <v-card height="100%" class="todo-card">
    <v-card-title class="d-flex align-center">
      <v-icon icon="mdi-checkbox-marked-circle-outline" color="primary" class="mr-2" />
      {{ t('toDoCard.title') }}
    </v-card-title>
    <v-card-text>
      <div class="d-flex justify-space-between align-center mb-2">
        <div>
          <div class="text-subtitle-2">{{ t('toDoCard.completed') }} / {{ t('toDoCard.total') }}</div>
          <div class="text-h5 font-weight-bold error--text">{{ userStore.getTodoTasks.completed }} / {{ userStore.getTodoTasks.total }}</div>
        </div>
      </div>
      <v-progress-linear
        :model-value="(userStore.getTodoTasks.completed / userStore.getTodoTasks.total) * 100"
        color="success"
        height="10"
        rounded
      ></v-progress-linear>
    </v-card-text>
  </v-card>
</template>