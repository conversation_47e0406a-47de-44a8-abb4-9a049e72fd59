<template>
  <v-dialog v-model="state.showToolsDialog" max-width="600">
    <v-card>
      <v-card-title class="d-flex align-center text-h5">
        <v-icon icon="mdi-toolbox-outline" class="mr-2" color="primary"></v-icon>
        {{ t('chatbot.availableTools') }}
        <v-spacer></v-spacer>
        <v-btn icon="mdi-close" class="text-right" @click="state.showToolsDialog = false" size="small"></v-btn>
      </v-card-title>
      <v-card-text>
        <v-switch
          v-model="state.isToolsEnabled"
          :label="t('chatbot.enable')"
          class="mb-2"
        ></v-switch>
        <v-list>
        <v-list-group
          v-for="server in state.availableTools"
          :key="server.name"
          :value="server.name"
        >
          <template v-slot:activator="{ props }">
            <v-list-item
              v-bind="props"
            >
              <div class="d-flex align-center">
                <v-checkbox
                  v-model="server.enabled"
                  :disabled="!state.isToolsEnabled"
                  density="compact"
                  hide-details
                  class="mr-2"
                  @click.stop
                ></v-checkbox>
                <div>
                  <v-list-item-title>{{ server.name }}</v-list-item-title>
                  <v-list-item-subtitle>{{ server.description }}</v-list-item-subtitle>
                </div>
              </div>
            </v-list-item>
          </template>

          <div v-if="server.enabled">
            <v-list-item
              v-for="tool in server.tools"
              :key="tool.name"
              class="ml-5"
            >
              <div>
                <v-list-item-title>{{ tool.name }}</v-list-item-title>
                <v-list-item-subtitle>{{ tool.description }}</v-list-item-subtitle>
              </div>
            </v-list-item>
          </div>
        </v-list-group>
      </v-list>
      </v-card-text>
    </v-card>
  </v-dialog>
</template>
<script setup>
import { watch } from 'vue';

const props = defineProps({
  state: {
    type: Object,
    required: true
  },
  t: {
    type: Function,
    required: true
  }
});

watch(() => props.state.isToolsEnabled, (newValue) => {
  props.state.availableTools.forEach(server => {
    server.enabled = newValue;
  });
});
</script>