<script setup>
import { ref } from 'vue'
import { useI18n } from 'vue-i18n'

const { t } = useI18n()

// Sample data - replace with actual data source
const contracts = ref([
  {
    id: 1,
    name: 'Enterprise License Agreement',
    client: 'Acme Corp',
    value: 150000,
    status: 'pending',
    dueDate: '2023-12-31',
    lastUpdated: '2023-07-15'
  },
  {
    id: 2,
    name: 'Maintenance Contract',
    client: 'TechGlobal Inc',
    value: 75000,
    status: 'signed',
    dueDate: '2023-12-31',
    lastUpdated: '2023-07-10'
  },
  {
    id: 3,
    name: 'Support Services Agreement',
    client: 'InnoSys Ltd',
    value: 50000,
    status: 'pending',
    dueDate: '2023-09-30',
    lastUpdated: '2023-07-01'
  }
])

const headers = [
  { title: t("common.name"), key: 'name', sortable: true },
  { title: t("contractStatus.client"), key: 'client', sortable: true },
  { title: t("common.amount"), key: 'value', sortable: true },
  { title: t("common.status"), key: 'status', sortable: true },
  { title: t("common.dueDate"), key: 'dueDate', sortable: true },
  { title: t("common.lastUpdated"), key: 'lastUpdated', sortable: true }
]

const getStatusColor = (status) => {
  return status === 'signed' ? 'success' : 'warning'
}
</script>

<template>
  <v-card class="contract-status-card">
    <v-card-title class="d-flex align-center">
      <v-icon icon="mdi-file-document-outline" color="primary" class="mr-2" size="large"></v-icon>
      {{ t("contractStatus.title")  }}
    </v-card-title>

    <v-card-text>
      <v-data-table
        :headers="headers"
        :items="contracts"
        :items-per-page="5"
        class="elevation-1"
      >
        <template v-slot:item.value="{ item }">
          ${{ item.value.toLocaleString() }}
        </template>

        <template v-slot:item.status="{ item }">
          <v-chip
            :color="getStatusColor(item.status)"
            size="small"
          >
            {{ item.status }}
          </v-chip>
        </template>

        <template v-slot:item.dueDate="{ item }">
          {{ new Date(item.dueDate).toLocaleDateString() }}
        </template>

        <template v-slot:item.lastUpdated="{ item }">
          {{ new Date(item.lastUpdated).toLocaleDateString() }}
        </template>
      </v-data-table>
    </v-card-text>
  </v-card>
</template>

<style scoped>
.contract-status-card {
  height: 100%;
}
</style>