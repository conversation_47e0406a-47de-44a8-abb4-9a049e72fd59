import httpx
import logging
import os
import json
from typing import Dict, Any

logger = logging.getLogger(__name__)

REQUEST_DATA_URL = "http://localhost:8003/query"
REQUEST_HEADERS = {"Accept": "application/json", "Content-Type": "application/json"}


async def request_data(query: str) -> Dict[str, Any]:
    logger.info(f"Preparing request to indexer with query: {query}")
    logger.debug(f"Query encoding info - type: {type(query)}, repr: {repr(query)}")

    # Build request payload
    payload = {"query": query}

    # Ensure headers contain correct encoding information
    headers = {
        "Accept": "application/json",
        "Content-Type": "application/json; charset=utf-8",
    }

    logger.debug(f"Request URL: {REQUEST_DATA_URL}")
    logger.debug(f"Request headers: {headers}")

    # Manually convert payload to JSON to ensure proper Chinese character handling
    try:
        payload_json = json.dumps(payload, ensure_ascii=False)
        logger.debug(f"Request payload (JSON): {payload_json}")
    except Exception as e:
        logger.error(f"Failed to convert payload to JSON: {e}")
        payload_json = None

    async with httpx.AsyncClient() as client:
        try:
            logger.info(f"Sending request to indexer")

            # If manual JSON conversion succeeded, use it
            if payload_json:
                response = await client.post(
                    REQUEST_DATA_URL,
                    headers=headers,
                    content=payload_json.encode(
                        "utf-8"
                    ),  # Explicitly specify UTF-8 encoding
                    timeout=30.0,
                )
            else:
                # Otherwise use httpx's automatic JSON handling
                response = await client.post(
                    REQUEST_DATA_URL, headers=headers, json=payload, timeout=30.0
                )

            logger.debug(f"Response status code: {response.status_code}")
            logger.debug(f"Response headers: {response.headers}")

            response.raise_for_status()

            # Manually parse response content to ensure proper Chinese character handling
            try:
                response_text = response.text
                logger.debug(f"Response text: %s", response_text[:200])
                data = json.loads(response_text)
            except json.JSONDecodeError:
                logger.error(f"Failed to parse response as JSON: {response.text[:200]}")
                data = {"error": "Failed to parse response as JSON"}

            logger.info(f"Received successful response from indexer")
            return data
        except Exception as e:
            logger.exception(f"Error in request_data: {str(e)}")
            return {"error": f"Failed to get data from indexer: {str(e)}"}
