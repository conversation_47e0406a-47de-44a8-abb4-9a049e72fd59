# main.py

import asyncio
from contextlib import asynccontextmanager
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from dotenv import load_dotenv
from app.api.ai_chat import router as chat_router
from app.api.user import router as user_router
from app.api.floorplan import router as floorplan_router
from app.config.mcp_servers import MCP_SERVERS
from app.core.mcp_client import MCPClient
from app.utils.logger import ColoredLogger

logger = ColoredLogger("app.main")
load_dotenv()


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Lifespan event handler for FastAPI app."""
    global mcp_clients
    mcp_clients = {}
    app.state.mcp_clients = mcp_clients

    try:
        # Initialize all MCP clients from MCP_SERVERS with timeout
        for server_name, config in MCP_SERVERS.items():
            try:
                if not config["is_active"]:
                    continue
                client = MCPClient(name=server_name, config=config)
                await asyncio.wait_for(client.initialize(), timeout=60.0)
                mcp_clients[server_name] = client
                logger.info("MCP server %s initialized successfully", server_name)
            except asyncio.TimeoutError:
                logger.error("Timeout initializing MCP server %s", server_name)
                continue
            except (ConnectionError, ValueError) as e:
                logger.error("Error initializing MCP server %s: %s", server_name, str(e))
                continue
            except Exception as e:
                logger.error("Unexpected error initializing MCP server %s: %s", server_name, str(e))
                continue

        logger.info("All MCP servers initialized successfully. Total servers: %d", len(mcp_clients.keys()))

    except (ValueError, RuntimeError, TypeError) as e:  # Replace with specific exceptions
        logger.error("Critical error during MCP server initialization: %s", str(e))
        mcp_clients = {}
        app.state.mcp_clients = mcp_clients
        raise  # This will prevent the app from starting if initialization fails

    try:
        yield
    except asyncio.CancelledError:
        logger.info("Received shutdown signal, cleaning up...")
    finally:
        # Cleanup all clients with timeout and proper error handling
        cleanup_tasks = []
        for server_name, client in mcp_clients.items():
            try:
                # Special handling for problematic servers
                if server_name in ['mcp-it-helpdesk', 'mcp-atlassian']:
                    logger.info("Starting graceful cleanup for %s", server_name)
                    # Run cleanup synchronously to avoid task switching issues
                    await client.cleanup()
                    logger.info("Successfully cleaned up %s", server_name)
                else:
                    task = asyncio.create_task(client.cleanup())
                    cleanup_tasks.append(task)
            except (RuntimeError, asyncio.CancelledError) as e:
                logger.error("Error creating cleanup task for %s: %s", server_name, str(e))
                continue

        if cleanup_tasks:
            try:
                done, pending = await asyncio.wait(cleanup_tasks, timeout=10.0)
                for task in pending:
                    task.cancel()
                logger.info("Cleanup completed")
            except (RuntimeError, asyncio.CancelledError, ValueError) as e:
                logger.error("Error during cleanup: %s", str(e))


app = FastAPI(lifespan=lifespan)

app.include_router(chat_router)
app.include_router(user_router)
app.include_router(floorplan_router)

# Add CORS middleware configuration
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    # allow_origins=[os.getenv("FRONTEND_URL", "http://localhost:5173")],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)
