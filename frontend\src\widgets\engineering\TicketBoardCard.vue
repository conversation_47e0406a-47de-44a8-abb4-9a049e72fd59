<script setup>
import { ref, computed, onMounted } from 'vue'
import { useI18n } from 'vue-i18n'
import { useUserStore } from '@/stores/userStore'

const { t } = useI18n()
const userStore = useUserStore()

const tickets = ref([])

const headers = computed(() => [
  { title: t('common.title'), key: 'title', sortable: true },
  { title: t('common.type'), key: 'type', sortable: true },
  { title: t('common.status'), key: 'status', sortable: true },
  { title: t('common.priority'), key: 'priority', sortable: true },
  { title: t('common.dueDate'), key: 'dueDate', sortable: true },
  { title: t('common.action'), key: 'actions', sortable: false }
])

// Mock function - replace with actual API calls
async function fetchTickets() {
  // TODO: Implement actual API integration with Jira/GitLab
  tickets.value = [
    {
      id: 1,
      title: 'Example Ticket',
      type: 'Bug',
      status: 'In Progress',
      priority: 'High',
      dueDate: '2024-01-31'
    }
  ]
}

function openTicket(ticket) {
  // TODO: Implement ticket opening in Jira/GitLab
  window.open(ticket.url, '_blank')
}

onMounted(() => {
  fetchTickets()
})
</script>

<template>
  <v-card height="100%" class="ticket-board-card">
    <v-card-title class="d-flex align-center">
      <v-icon icon="mdi-ticket" color="primary" class="mr-2"></v-icon>
      {{ t('ticketBoard.title') }}
      <v-spacer></v-spacer>
      <v-btn
        color="primary"
        size="small"
        prepend-icon="mdi-refresh"
        @click="fetchTickets"
      >
        {{ t('common.refresh') }}
      </v-btn>
    </v-card-title>
    <v-card-text>
      <v-data-table
        :headers="headers"
        :items="tickets"
        :items-per-page="5"
        class="elevation-1"
      >
        <template v-slot:item.actions="{ item }">
          <v-btn
            size="small"
            color="primary"
            variant="text"
            @click="openTicket(item)"
          >
            {{ t('common.open') }}
          </v-btn>
        </template>
      </v-data-table>
    </v-card-text>
  </v-card>
</template>

<style scoped>
.ticket-board-card {
  overflow: auto;
}
</style>