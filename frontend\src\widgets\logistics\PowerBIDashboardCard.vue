<script setup>
import { ref } from 'vue'

const powerBiConfig = ref({
  reportId: 'your-report-id',
  embedUrl: 'https://app.powerbi.com/reportEmbed',
  accessToken: 'your-access-token'
})

const powerBiLoading = false
const powerBiError = null

</script>

<template>
  <v-card height="100%" class="powerbi-dashboard-card">
    <v-card-title class="d-flex align-center">
      <v-icon icon="mdi-chart-box" color="primary" class="mr-2"></v-icon>
      PowerBI Dashboard
    </v-card-title>
    <v-card-text class="pa-0 position-relative" style="height: calc(100% - 64px);">
      <div v-if="powerBiLoading" class="d-flex justify-center align-center" style="height: 100%">
        <v-progress-circular indeterminate color="primary"></v-progress-circular>
      </div>
      <div v-else-if="powerBiError" class="d-flex justify-center align-center" style="height: 100%">
        <v-alert type="error" text="Failed to load PowerBI dashboard"></v-alert>
      </div>
      <iframe
        v-else
        :src="`${powerBiConfig.embedUrl}?reportId=${powerBiConfig.reportId}&token=${powerBiConfig.accessToken}`"
        frameborder="0"
        allowfullscreen="true"
        style="width: 100%; height: 100%;"
        @load="powerBiLoading"
        @error="powerBiError"
      ></iframe>
    </v-card-text>
  </v-card>
</template>