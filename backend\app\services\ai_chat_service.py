# backend/app/services/ai_chat_service.py

import logging
import json
import os
import time
from typing import List, Dict, Any
import uuid
import dashscope
import httpx

from app import main
from app.config.prompts import SYSTEM_MESSAGE_WITH_TOOLS_AND_FORMATTING, SYSTEM_MESSAGE_NO_TOOLS_WITH_FORMATTING
from app.core.ai_chat_engine import ChatEngine
from app.core.mcp_client import MC<PERSON>lient
from app.utils.helpers import ensure_system_prompt, extract_structured_output
from app.schemas.ai_chat import CompletionResult
from app.storage.ai_chat_storage import ChatStorage
from app.core.llm_model_manager import LLMModelManager
from app.utils.logger import ColoredLogger

logging.basicConfig(level=logging.DEBUG)

class ChatService:
    def __init__(self):
        self.chat_storage = ChatStorage()
        self.client = ChatEngine()
        self.mcp_clients = main.app.state.mcp_clients
        self.llm_model_manager = LLMModelManager()
        logger.info("Chat Service Initialized")

    def start_new_session(self, user_id: str, title: str) -> str:
        session_id = f"sess_{uuid.uuid4().hex}"
        self.chat_storage.create_session(user_id, session_id, title)
        self.chat_storage.set_ttl(user_id, session_id, 30 * 24 * 60 * 60)
        return session_id

    def add_user_message(self, user_id: str, session_id: str, message: str) -> str:
        return self.chat_storage.add_message(user_id, session_id, role="user", content=message)

    def add_assistant_message(self, user_id: str, session_id: str, message: str, options: dict = {}):
        self.chat_storage.add_message(user_id, session_id, role="assistant", content=message, options=options)

    def get_full_conversation(self, user_id: str, session_id: str) -> dict | None:
        return self.chat_storage.get_session(user_id, session_id)

    def list_user_sessions(self, user_id: str):
        return self.chat_storage.list_sessions(user_id)

    def delete_session(self, user_id: str, session_id: str):
        self.chat_storage.delete_session(user_id, session_id)

    async def get_all_tools(self) -> List[dict]:
        """
        Asynchronously retrieves all available tools from the MCP clients.
        Each tool is represented as a dictionary with its name, description, and input schema.
        """
        all_tools = []
        for client_name, client in self.mcp_clients.items():
            try:
                tools = await client.list_tools()
                for tool in tools:
                    # Basic validation of tool structure
                    if (
                        hasattr(tool, "name")
                        and hasattr(tool, "description")
                        and hasattr(tool, "input_schema")
                    ):
                        tool_dict = {
                            "type": "function",
                            "function": {
                                "name": f"{client_name}_{tool.name}",
                                "description": tool.description,
                                # Ensure parameters is a dict, handle potential None or non-dict schema
                                "parameters": (
                                    tool.input_schema
                                    if isinstance(tool.input_schema, dict)
                                    else {"type": "object", "properties": {}}
                                ),
                            },
                        }
                        all_tools.append(tool_dict)
                    else:
                        logging.warning("ChatService: Tool from client %s is missing required attributes (name, description, input_schema): %s", client_name, tool)
            except (ConnectionError, TimeoutError, ValueError) as e:
                logging.error("ChatService: Failed to list tools for client %s: %s", client_name, e)
        return all_tools

    async def execute_tool(
        self, full_tool_name: str, arguments: dict, user_id: str = None
    ) -> Any:
        """
        Executes a specified tool with the provided arguments.
        The tool name should be in the format 'client_name_tool_name'.
        """
        client_name, tool_name = full_tool_name.split("_", 1)
        if client_name not in self.mcp_clients:
            # Log error and maybe return an error indicator instead of raising?
            # Depending on desired behavior if a tool specified by LLM doesn't exist.
            logging.error("ChatService: Client %s requested by LLM not found for tool %s", client_name, tool_name)
            raise ValueError(f"ChatService: Client {client_name} not found")
            # Alternatively: return type('obj', (object,), {'content': f'Error: Client {client_name} not found'})()
        try:
            return await self.mcp_clients[client_name].execute_tool(tool_name, arguments)
        except (ConnectionError, TimeoutError, ValueError) as e:
            logging.error("ChatService: Error executing tool %s with args %s: %s", full_tool_name, arguments, e)
            # Re-raise or return an error object/string
            # Returning error string to be added to messages
            return type(
                "obj",
                (object,),
                {"content": error_msg},
            )()

    async def process_query(self, user_id: str, session_id: str, query: str, is_tools_enabled: bool) -> CompletionResult:
        """
        Processes a user query with or without tools.
        When the frontend sends { session_id, new_user_message }, the backend should:
        1. Load previous history from Redis using session_id.
        2. Append new user message to the history.
        3. Trim messages if needed (based on token limits).
        4. Send to OpenAI (or local LLM) for chat completion.
           If tools are enabled, it first asks LLM with tool list,
           then executes the tools, and finally returns the final answer.
           If tools are disabled, it directly returns the answer from LLM.
        5. Store assistant’s response in Redis.
        6. Return response to frontend (via SSE or normal HTTP).
        """
        logging.info("ChatService:process_query: User ID: %s, Session ID: %s, Query: %s, Tools Enabled: %s", user_id, session_id, query, is_tools_enabled)
        session = self.chat_storage.get_session(user_id, session_id)
        if session is None:
            session = self.chat_storage.create_session(
                user_id, session_id, title="New Chat"
            )

        messages = list(map(lambda msg: { "role": msg["role"], "content": msg["content"] }, session["messages"]))
        logging.info("ChatSevice:process_query: History messages: %s", messages)

        # Create tools description for system message
        tools_description = "\n".join(
            f"[{tool['function']['name']}]: {tool['function']['description']}"
            for tool in tools
        ) if is_tools_enabled else ""

        messages = ensure_system_prompt(
            messages, 
            SYSTEM_MESSAGE_WITH_TOOLS_AND_FORMATTING.format(tools_description=tools_description)
            if is_tools_enabled else SYSTEM_MESSAGE_NO_TOOLS_WITH_FORMATTING
        )
        messages.append({"role": "user", "content": query})
        self.add_user_message(user_id, session_id, query)

        logging.info("ChatService:process_query: Calling 1st LLM with messages: %s", messages)
        # Round 1: Ask LLM with tool list
        response = await self.client.create_completion(
            messages=messages,
            tools=tools if is_tools_enabled else None,
            tool_choice="auto" if tools else None,
            temperature=0.7,
            stream=False
        )

        msg = response.choices[0].message
        # Fix the assistant message format
        assistant_message = {"role": msg.role, "content": msg.content}
        if hasattr(msg, "tool_calls") and msg.tool_calls:
            assistant_message["type"] = "function"
            assistant_message["tool_calls"] = [
                {
                    "id": tool_call.id,
                    "type": "function",
                    "function": {
                        "name": tool_call.function.name,
                        "arguments": tool_call.function.arguments,
                    },
                }
                for tool_call in msg.tool_calls
            ]
        messages.append(assistant_message)

        logger.info("LLM response: %s", response)

        tools = []
        # If tools were called, execute them
        if hasattr(msg, "tool_calls") and msg.tool_calls:
            tool_results = []
            for tool_call in msg.tool_calls:
                tools.append(tool_call.function.name)
                tool_name = tool_call.function.name
                # Use safe JSON parsing instead of eval()
                args = safe_json_loads(tool_call.function.arguments)
                result = await self.execute_tool(tool_name, args, user_id)
                tool_results.append(
                    {
                        "tool_call_id": tool_call.id,
                        "role": "tool",
                        "name": tool_name,
                        "content": safe_str_conversion(
                            result
                        ),  # Safe string conversion
                        "type": "function",
                    }
                )

            # Append tool execution results
            for tool_result in tool_results:
                messages.append(tool_result)

            logging.info("ChatSevice:process_query: Calling 2nd LLM with messages: %s", messages)
            # Round 2: Final answer
            response = await self.client.create_completion(
                messages=messages,
                stream=False
            )
            final_msg = response.choices[0].message
            messages.append({"role": final_msg.role, "content": final_msg.content})
        else:
            final_msg = msg

        # Extract final response, reasoning, and mermaid code
        reasoning, mermaid_code, final_response = extract_structured_output(final_msg.content)
        # Convert CompletionUsage to dict
        token_usage = {
            "completion_tokens": response.usage.completion_tokens,
            "prompt_tokens": response.usage.prompt_tokens,
            "total_tokens": response.usage.total_tokens
        }

        # Update session in Redis
        options = {
            "tools": tools,
            "think": think_text,
            "evaluate": evaluate_text,
            "refine": refine_text,
            "mermaid_code": mermaid_code,
            "token_usage": token_usage
        }
        message_id = self.add_assistant_message(user_id, session_id, final_response, options)
        self.chat_storage.update_title(user_id, session_id)

        return CompletionResult(
            id=message_id,
            final_response=final_response,
            tools=tools,
            think=think_text,
            evaluate=evaluate_text,
            refine=refine_text,
            mermaid_code=mermaid_code,
            citations=[],
            token_usage=token_usage
        )

    async def process_stream_query(self, user_id: str, session_id: str, query: str, is_tools_enabled: bool):
        """
        Processes a user query with or without tools using streaming.
        When the frontend sends { session_id, new_user_message }, the backend should:
        1. Load previous history from Redis using session_id.
        2. Append new user message to the history.
        3. Trim messages if needed (based on token limits).
        4. Send to OpenAI (or local LLM) for chat completion.
           If tools are enabled, it first asks LLM with tool list, 
           then executes the tools, and finally returns the final answer.
           If tools are disabled, it directly returns the answer from LLM.
        5. Store assistant’s response in Redis.
        6. Return response to frontend (via SSE or normal HTTP).
        """
        logging.info("ChatService:process_stream_query: User ID: %s, Session ID: %s, Query: %s, Tools Enabled: %s", user_id, session_id, query, is_tools_enabled)
        session = self.chat_storage.get_session(user_id, session_id)
        if session is None:
            session = self.chat_storage.create_session(user_id, session_id, title="New Chat")
            logging.info("ChatService:process_stream_query: New session created: %s", session)

        messages = list(map(lambda msg: { "role": msg["role"], "content": msg["content"] }, session["messages"]))
        logging.info("ChatSevice:process_stream_query: History messages: %s", messages)

        tools = await self.get_all_tools() if is_tools_enabled else None
        # Create tools description for system message
        tools_description = "\n".join(
            f"[{tool['function']['name']}]: {tool['function']['description']}"
            for tool in tools
        ) if is_tools_enabled else ""

        messages = ensure_system_prompt(
            messages, 
            SYSTEM_MESSAGE_WITH_TOOLS_AND_FORMATTING.format(tools_description=tools_description)
            if is_tools_enabled else SYSTEM_MESSAGE_NO_TOOLS_WITH_FORMATTING
        )
        messages.append({"role": "user", "content": query})
        self.add_user_message(user_id, session_id, query)

        logging.info("ChatSevice:process_stream_query: Calling 1st LLM with messages: %s", messages)
        # Round 1: Ask LLM with tool list
        stream = await self.client.create_completion(
            messages=messages,
            tools=tools if tools else None,
            tool_choice="auto" if tools else None,
            stream=True,
            provider_name=provider_name,
            model_name=model_name
        )

        full_content = ""
        full_message = {"role": "assistant", "content": "", "tool_calls": []}
        tool_calls_raw = {}

        # 7. Check if tool calls are needed, 
        # - If yes, execute the tools, get the tools results and then call LLM for 2nd round chat completion with tools results.
        async for chunk in stream:
            if first_token_time is None:
                first_token_time = time.monotonic()
                metrics["ttft1"] = first_token_time - start_time if first_token_time else None

            choice = chunk.choices[0]
            delta = choice.delta

            if delta.content:
                full_content += delta.content
                full_message["content"] += delta.content
                yield {"type": "content", "data": delta.content}

            if delta.tool_calls:
                for call in delta.tool_calls:
                    if call.index not in tool_calls_raw:
                        # Initialize new tool call with complete metadata
                        tool_calls_raw[call.index] = {
                            "id": call.id,
                            "type": "function",
                            "function": {
                                "name": call.function.name,
                                "arguments": call.function.arguments or "",
                            },
                        }
                    else:
                        # Append only arguments for subsequent chunks
                        if call.function and call.function.arguments:
                            tool_calls_raw[call.index]["function"][
                                "arguments"
                            ] += call.function.arguments

        logging.info("ChatService:process_stream_query: 1st LLM response: %s", full_content)
        logging.info("ChatService:process_stream_query: Tool calls raw: %s", tool_calls_raw)

        tools = []
        tool_outputs = []
        if tool_calls_raw:
            full_message["tool_calls"] = list(tool_calls_raw.values())
            messages.append(full_message)

            tool_call_start = time.monotonic()
            for call_data in tool_calls_raw.values():
                try:
                    tools.append(call_data["function"]["name"])
                    yield {
                        "type": "tool_call_start",
                        "id": call_data["id"],
                        "name": call_data["function"]["name"],
                        "arguments": call_data["function"]["arguments"],
                    }

                    result = await self.execute_tool(
                        call_data["function"]["name"],
                        safe_json_loads(
                            call_data["function"]["arguments"]
                        ),  # Use safe JSON parsing
                        user_id,  # Pass user_id
                    )
                    tool_outputs.append(
                        {
                            "tool_call_id": call_data["id"],
                            "role": "tool",
                            "name": call_data["function"]["name"],
                            "content": safe_str_conversion(
                                result
                            ),  # Safe string conversion
                            "type": "function",
                        }
                    )
                except json.JSONDecodeError as e:
                    logging.error(f"ChatService:process_stream_query: Failed to parse arguments for tool {call_data['function']['name']}: {e}")
                except Exception as e:
                    logging.error(f"ChatService:process_stream_query: Failed to execute tool {call_data['function']['name']}: {e}")

            messages.extend(tool_outputs)
            logger.info("Done getting tool outputs: size of tool_outputs: %s", len(tool_outputs))

            logging.info("ChatSevice:process_stream_query: Calling 2nd LLM with messages: %s", messages)
            # 2nd round: Final answer
            second_stream = await self.client.create_completion(
                messages=cleaned_messages,
                stream=True,
                provider_name=provider_name,
                model_name=model_name
            )

            full_content = ""
            async for chunk in second_stream:
                if first_token_time is None:
                    first_token_time = time.monotonic()
                    metrics["ttft2"] = first_token_time - start_time if first_token_time else None

                delta = chunk.choices[0].delta
                if delta.content:
                    full_content += delta.content
                    yield {"type": "content", "data": delta.content}

            metrics["ttfr2"] = time.monotonic() - start_time

            # Add final response to history
            if full_content.strip():  # Ensure content is not empty
                messages.append({"role": "assistant", "content": full_content})
        else:
            # If no tools were used, save the single response
            if full_content.strip():  # Ensure content is not empty
                messages.append({"role": "assistant", "content": full_content})

        logging.info("ChatService:process_stream_query: 2nd LLM response: %s", full_content)

        reasoning, mermaid_code, final_response = extract_structured_output(full_content)
        options = {
            "tools": tools,
            "rag": rag_context,
            "think": think_text,
            "evaluate": evaluate_text,
            "refine": refine_text,
            "mermaid_code": mermaid_code,
            "token_usage": {}
        }

        self.add_assistant_message(user_id, session_id, final_response, options=options)
        self.chat_storage.update_title(user_id, session_id)

        if think_text:
            yield {"type": "think", "data": think_text}
        if evaluate_text:
            yield {"type": "evaluate", "data": evaluate_text}
        if refine_text:
            yield {"type": "refine", "data": refine_text}
        if mermaid_code:
            yield {"type": "mermaid", "data": mermaid_code}
        if refs:
            yield {"type": "refs", "data": refs}
        if followups:
            yield {"type": "followups", "data": followups}

        yield {"type": "final", "data": final_response}