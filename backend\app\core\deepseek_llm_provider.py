import os
from openai import Async<PERSON>penAI
from dotenv import load_dotenv
from app.core.llm_provider_base import <PERSON><PERSON><PERSON><PERSON>, ChatEngineError, InvalidAPIKeyError, RateLimitError, TimeoutError
from app.utils.logger import ColoredLogger

logger = ColoredLogger("app.core.deepseek_llm_provider")

load_dotenv()

class DeepSeekLLMProvider(LLMProvider):
    def __init__(self, model):
        super().__init__(model)
        self.base_url = os.getenv("DEEPSEEK_API_BASE", "https://api.deepseek.com")
        self.api_key = os.getenv("DEEPSEEK_API_KEY", "Your API Key")
        self.client = AsyncOpenAI(base_url=self.base_url, api_key=self.api_key)
    
    async def create_completion(self, messages, tools=None, tool_choice=None, temperature=0.7, stream=False):
        try:
            return await self.client.chat.completions.create(
                model=self.model,
                messages=messages,
                tools=tools,
                tool_choice=tool_choice,
                temperature=temperature,
                stream=stream,
            )
        except InvalidAPIKeyError:
            logger.error("Invalid API key provided.")
            raise InvalidAPIKeyError("Invalid API key provided.")
        except TimeoutError:
            logger.error("Request timed out.")
            raise TimeoutError("Request timed out.")
        except RateLimitError:
            logger.error("Rate limit exceeded. Please check your plan and billing details.")
            raise RateLimitError("Rate limit exceeded. Please check your plan and billing details.")
        except Exception as e:
            logger.error(f"An unexpected error, {str(e)}.")
            raise ChatEngineError(f"An unexpected error occurred: {str(e)}")