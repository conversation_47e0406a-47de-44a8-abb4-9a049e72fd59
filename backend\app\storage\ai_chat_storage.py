# backend/app/storage/ai_chat_storage.py

# | Redis Key                       | Contains                               |
# | ------------------------------- | -------------------------------------- |
# | `chat_session:{id}:{session_id} | session metadata, messages             | 
#

import json
from datetime import datetime, timezone
from typing import List
import os
import redis
from app.utils.logger import ColoredLogger

logger = ColoredLogger("app.storage.ai_chat_storage")


class ChatStorage:
    def __init__(self):
        try:
            self.redis = redis.Redis.from_url(
                os.getenv("REDIS_URL", "redis://localhost:6379")
            )
            self.redis.ping()
            logger.info("Initialized Redis connection")
        except Exception as e:
            logger.error(f"Failed to initialize Redis connection: {str(e)}")
            raise e
        self.prefix = "chat_session"
        self.index_prefix = "chat_session_index"

    def _session_key(self, user_id: str, session_id: str) -> str:
        return f"{self.prefix}:{user_id}:{session_id}"

    def _index_key(self, user_id: str) -> str:
        return f"{self.index_prefix}:{user_id}"

    def create_session(self, user_id: str, session_id: str, title: str):
        data = {
            "session_id": session_id,
            "title": title,
            "created_at": datetime.now(timezone.utc).isoformat(),
            "messages": [],
        }
        # Save session data
        self.redis.set(self._session_key(user_id, session_id), json.dumps(data))
        # Add session_id to user's index
        self.redis.sadd(self._index_key(user_id), session_id)
        logger.info(f"Created new chat session for user {user_id}: {session_id}")
        return data

    def add_message(self, user_id: str, session_id: str, role: str, content: str, options: dict = {}) -> str:
        key = self._session_key(user_id, session_id)
        raw = self.redis.get(key)
        if not raw:
            raise ValueError("Session not found")

        session = json.loads(raw)
        message_id = str(len(session["messages"]) + 1)
        session["messages"].append({"id": message_id, "role": role, "content": content, "options": options, "timestamp": datetime.now(timezone.utc).isoformat()})
        self.redis.set(key, json.dumps(session))
        logger.info(f"Added message {message_id} to chat session {session_id} for user {user_id}")
        return message_id

    def update_message_feedback(self, user_id: str, session_id: str, message_id: str, feedback: str):
        key = self._session_key(user_id, session_id)
        try:
            raw = self.redis.get(key)
            if not raw:
                logger.error(f"Session {session_id} not found for user {user_id}")
                return False
            session = json.loads(raw)
            for msg in session.get("messages", []):
                if str(msg.get("id")) == str(message_id) and msg.get("role") == "assistant" and msg.get("options"):
                    msg["options"]["feedback"] = feedback
                    self.redis.set(key, json.dumps(session))
                    logger.info(f"Updated feedback '{feedback}' for message {message_id} in chat session {session_id} for user {user_id}")
                    return True
            logger.warning(f"Message {message_id} not found or does not meet update criteria in session {session_id}")
            return False
        except Exception as e:
            logger.error(f"Unexpected error retrieving session {session_id}: {e}")
            return False

    def get_message_feedback(self, user_id: str, session_id: str, message_id: str) -> str | None:
        key = self._session_key(user_id, session_id)
        try:
            raw = self.redis.get(key)
            if not raw:
                raise ValueError("Session not found")

            session = json.loads(raw)
            for msg in session["messages"]:
                if msg["id"] == message_id and msg["role"] == "assistant" and msg["options"] and msg["options"].get("feedback"):
                    logger.info(f"Retrieved feedback for message {message_id} in chat session {session_id} for user {user_id}")
                    return msg["options"]["feedback"]
            logger.info(f"Message {message_id} in chat session {session_id} for user {user_id} does not have feedback")
            return None
        except Exception as e:
            logger.error(f"Unexpected error retrieving session {session_id}: {e}")
            return None

    def get_session(self, user_id: str, session_id: str) -> dict | None:
        raw = self.redis.get(self._session_key(user_id, session_id))
        logger.info(f"Retrieved chat session {session_id} for user {user_id}")
        if raw:
            return json.loads(raw)
        return None

    def delete_session(self, user_id: str, session_id: str):
        # Delete session data
        self.redis.delete(self._session_key(user_id, session_id))
        # Remove session_id from user's index
        self.redis.srem(self._index_key(user_id), session_id)

    def get_new_message_id(self, user_id: str, session_id: str) -> str:
        key = self._session_key(user_id, session_id)
        raw = self.redis.get(key)
        if not raw:
            raise ValueError("Session not found")

        session = json.loads(raw)
        return str(len(session["messages"]) + 1)

    def delete_all_sessions(self, user_id: str):
        """Delete all chat sessions and their data for a given user."""
        session_ids = self.redis.smembers(self._index_key(user_id))
        for session_id_bytes in session_ids:
            session_id = (
                session_id_bytes.decode()
                if isinstance(session_id_bytes, bytes)
                else session_id_bytes
            )
            self.redis.delete(self._session_key(user_id, session_id))
        self.redis.delete(self._index_key(user_id))
        logger.info(f"Deleted all chat sessions for user {user_id}")

    def list_sessions(self, user_id: str) -> List[dict]:
        # Fetch all session_ids from index
        session_ids = self.redis.smembers(self._index_key(user_id))
        sessions = []
        for session_id_bytes in session_ids:
            session_id = (
                session_id_bytes.decode()
                if isinstance(session_id_bytes, bytes)
                else session_id_bytes
            )
            raw = self.redis.get(self._session_key(user_id, session_id))
            if raw:
                session = json.loads(raw)
                sessions.append(
                    {
                        "session_id": session["session_id"],
                        "title": session["title"],
                        "created_at": session["created_at"],
                    }
                )
        # Optional: Sort by created_at
        sessions.sort(key=lambda x: x["created_at"], reverse=True)
        return sessions

    def set_ttl(self, user_id: str, session_id: str, seconds: int):
        self.redis.expire(self._session_key(user_id, session_id), seconds)

    def update_title(self, user_id: str, session_id: str):
        key = self._session_key(user_id, session_id)
        raw = self.redis.get(key)
        if not raw:
            raise ValueError("Session not found")
        session = json.loads(raw)
        # Find the first message with role 'user'
        for msg in session.get("messages", []):
            if msg.get("role") == "user" and msg.get("content"):
                first_line = msg["content"].split("\n", 1)[0]
                session["title"] = first_line
                self.redis.set(key, json.dumps(session))
                logger.info(f"Updated title for session {session_id} for user {user_id}")
                return session["title"]
        raise ValueError("No user message found to set as title")
