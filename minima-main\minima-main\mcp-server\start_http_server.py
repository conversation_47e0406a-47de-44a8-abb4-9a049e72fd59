#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
啟動 MCP HTTP 服務器
"""

import asyncio
import sys
import os

# 添加 src 目錄到 Python 路徑
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "src"))

from minima.http_server import run_server

if __name__ == "__main__":
    print("🚀 Starting MCP HTTP Server...")
    print("📍 Server will be available at: http://localhost:8002")
    print("🔧 Health check: http://localhost:8002/health")
    print("📋 Tools list: http://localhost:8002/tools")
    print("⚡ Execute endpoint: http://localhost:8002/execute")
    print()

    try:
        asyncio.run(run_server(host="localhost", port=8002))
    except KeyboardInterrupt:
        print("\n👋 Server stopped by user")
    except Exception as e:
        print(f"❌ Server error: {e}")
        sys.exit(1)
