<script setup>
import { ref, computed, onMounted, onBeforeUnmount, watch } from 'vue';
import { useI18n } from 'vue-i18n';
import { useTheme } from 'vuetify';
import * as THREE from 'three';
import { GLTFLoader } from 'three/addons/loaders/GLTFLoader.js';
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls';
import { parseAreaFromId } from '@/utils/svgParser'
import { getPhoto } from '@/utils/photo'
import { storeToRefs } from 'pinia';
import { useFilterStore } from '@/stores/filterStore';
import { getAreas, getBuildings } from '@/services/floorPlanService';
import { GoogleMap, AdvancedMarker } from 'vue3-google-map'

const GOOGLE_MAPS_API_KEY = import.meta.env.VITE_GOOGLE_MAPS_API_KEY || ''

const { t, locale } = useI18n();
const theme = useTheme();
const isDarkMode = computed(() => theme.global.current.value.dark);

const filterStore = useFilterStore();
const { filterValue } = storeToRefs(filterStore);

const currentMode = ref('');

const currentBuilding = ref('');
const setCurrentBuilding = (building) => {
  currentBuilding.value = building;
  switchMode('floors');
};

const currentFloor = ref('');
const setCurrentFloor = (floor) => {
  currentFloor.value = floor;
  switchMode('areas');
}
const buildingsData = ref([]);
const currentBuildingData = computed(() => {
  return buildingsData.value.find(building => building.id === currentBuilding.value);
});
const groupedBuildings = computed(() => {
  return buildingsData.value.reduce((groups, building) => {
    const country = building.country[locale.value];
    if (!groups[country]) {
      groups[country] = [];
    }
    groups[country].push(building);
    return groups;
  }, {});
});
const expanded = ref({});
const areasData = ref([]);
const areaBoxes = [];
const selectedArea = ref(null); // New ref for selected area

const mouse = new THREE.Vector2();

const containerRef = ref(null);
let scene, camera, renderer, controls, pathfinder, pathfinderHelper, navmesh;
let previousSelectedAreas = []; 

const initBuildingScene = async () => {
  const response = await getBuildings();
  buildingsData.value = response.data;
  // console.log(buildingsData.value);
}

const exitBuildingScene = () => {
}

const initFloorScene = async () => {
  if (buildingsData.value.length === 0) {
    await initBuildingScene();
  }
}

const exitFloorScene = () => {
}

const initAreaScene = async (buildingValue, floorValue) => {
  const response = await getAreas(buildingValue, floorValue);
  if (!response.success) {
    switchMode('buildings');
    return;
  }
  areasData.value = response.data;
  // Create scene
  scene = new THREE.Scene();
  scene.background = new THREE.Color(theme.global.current.value.colors['background']);
  watch(() => theme.global.current.value.colors['background'], (newColor) => {
    if (scene) {
      scene.background.set(newColor);
    }
  }, { immediate: true });

  // Create camera
  camera = new THREE.PerspectiveCamera(
    75,
    1,
    // window.innerWidth / window.innerHeight,
    0.1,
    1000
  );
  camera.position.set(0, 80, 0);
  camera.lookAt(0, 0, 0);

  // Create renderer
  renderer = new THREE.WebGLRenderer({ antialias: true });
  renderer.setSize(containerRef.value.clientWidth, containerRef.value.clientHeight);
  renderer.shadowMap.enabled = true;
  renderer.shadowMap.type = THREE.PCFSoftShadowMap;
  containerRef.value.appendChild(renderer.domElement);

  // Add orbit controls
  controls = new OrbitControls(camera, renderer.domElement);
  controls.enableDamping = true;
  controls.dampingFactor = 0.05;
  controls.screenSpacePanning = false;
  controls.minDistance = 10;
  controls.maxDistance = 250;
  controls.maxPolarAngle = Math.PI / 2 - 0.05;
  controls.target.set(0, 1.6, 0);
  controls.update();

  // Add lighting
  const ambientLight = new THREE.AmbientLight(0xffffff, 0.7);
  scene.add(ambientLight);

  const directionalLight = new THREE.DirectionalLight(0xffffff, 0.9);
  directionalLight.position.set(5, 10, 5);
  directionalLight.castShadow = true;
  directionalLight.shadow.mapSize.width = 2048;
  directionalLight.shadow.mapSize.height = 2048;
  const shadowCamSize = Math.max(80, 80) * 0.5 * 0.6;
  directionalLight.shadow.camera.near = 10;
  directionalLight.shadow.camera.far = 250;
  directionalLight.shadow.camera.left = -shadowCamSize;
  directionalLight.shadow.camera.right = shadowCamSize;
  directionalLight.shadow.camera.top = shadowCamSize;
  directionalLight.shadow.camera.bottom = -shadowCamSize;
  scene.add(directionalLight);

  // Add grid helper
  const gridHelper = new THREE.GridHelper(20, 20);
  scene.add(gridHelper);

  // Add floorplan
  const gltfLoader = new GLTFLoader();
  gltfLoader.load(`/floorplan/${buildingValue}_${floorValue}.glb`, (gltf) => {
    scene.add(gltf.scene);
    // Traverse to find area boxes
    gltf.scene.traverse((child) => {
      const areaInfo = parseAreaFromId(child.name, '_');
      if (child.isMesh && areaInfo) {
        child.userData = {
          id: areaInfo.id,
          name: areasData.value && areasData.value[child.name] ? areasData.value[child.name].name[locale.value] : 
            areaInfo.type.replace(/-/g, " ") + " " + areaInfo.number,
          building: areaInfo.building,
          floor: areaInfo.floor,
          type: areaInfo.type.replace(/-/g, " "),
          number: areaInfo.number,
          employee: areasData.value && areasData.value[child.name] ? areasData.value[child.name].employee : null,
        };
        child.material = new THREE.MeshBasicMaterial({
          color: new THREE.Color(theme.global.current.value.colors['secondary']),
          transparent: true,
          opacity: 0.0,
        });
        areaBoxes.push(child);
      }
    });
  },
  undefined,  // onProgress handler (optional)
  (error) => {
    console.error("Error loading GLB file:", error);
    switchMode('buildings');
  }
 );
  animate();
  window.addEventListener('resize', handleResize);
  containerRef.value.addEventListener('mousemove', onMouseMove);
};

const exitAreaScene = () => {
  window.removeEventListener('resize', handleResize);
  containerRef.value.removeEventListener('mousemove', onMouseMove);
  areasData.value = [];
  if (renderer) {
    renderer.dispose();
  }
  if (controls) {
    controls.dispose();
  }
};

const animate = () => {
  requestAnimationFrame(animate);
  controls.update();

  renderer.render(scene, camera);
};

const handleResize = () => {
  if (!containerRef.value || !camera || !renderer) return;
  
  camera.aspect = containerRef.value.clientWidth / containerRef.value.clientHeight;
  camera.updateProjectionMatrix();
  renderer.setSize(containerRef.value.clientWidth, containerRef.value.clientHeight);
};

let tooltipSprites = [];
function createTooltipSprite(text) {
  const canvas = document.createElement('canvas');
  const ctx = canvas.getContext('2d');
  ctx.font = '20px Arial';
  // const textWidth = ctx.measureText(text).width;
  canvas.width = 256;
  canvas.height = 64;
  ctx.fillStyle = 'rgba(0,0,0,0.7)';
  ctx.fillRect(0, 0, canvas.width, canvas.height);
  ctx.fillStyle = '#fff';
  ctx.fillText(text, 10, 40);
  const texture = new THREE.CanvasTexture(canvas);
  const material = new THREE.SpriteMaterial({ map: texture, transparent: true });
  const sprite = new THREE.Sprite(material);
  sprite.scale.set(10, 2.5, 1); // Adjust size as needed
  return sprite;
}

function createTooltipSprite2(text) {
  const maxWidth = 280; // Maximum width for text before wrapping
  const font = '20px Arial';
  // Create a temporary canvas to measure text
  const tempCanvas = document.createElement('canvas');
  const tempCtx = tempCanvas.getContext('2d');
  tempCtx.font = font;
  let lines = [];
  let currentLine = '';
  for (let word of text.split(' ')) {
    let testLine = currentLine ? currentLine + ' ' + word : word;
    let { width } = tempCtx.measureText(testLine);
    if (width > maxWidth && currentLine) {
      lines.push(currentLine);
      currentLine = word;
    } else {
      currentLine = testLine;
    }
  }
  if (currentLine) lines.push(currentLine);
  const canvasWidth = 300;
  const canvasHeight = 40 + 30 * lines.length;
  const canvas = document.createElement('canvas');
  canvas.width = canvasWidth;
  canvas.height = canvasHeight;
  const ctx = canvas.getContext('2d');
  ctx.fillStyle = 'rgba(0,0,0,0.7)';
  ctx.fillRect(0, 0, canvas.width, canvas.height);
  ctx.font = font;
  ctx.fillStyle = '#fff';
  lines.forEach((line, i) => {
    ctx.fillText(line, 10, 40 + i * 30);
  });
  const texture = new THREE.CanvasTexture(canvas);
  const material = new THREE.SpriteMaterial({ map: texture, transparent: true });
  const sprite = new THREE.Sprite(material);
  sprite.scale.set(10, 2.5 * lines.length, 1); // Adjust height based on lines
  return sprite;
}

const onMouseMove = (event) => {
  if (filterValue.value) return;
  // Update actual mouse pixel coordinates for the info-card  
  const rect = containerRef.value.getBoundingClientRect();
  mouse.x = ((event.clientX - rect.left) / rect.width) * 2 - 1;
  mouse.y = -((event.clientY - rect.top) / rect.height) * 2 + 1;

  const raycaster = new THREE.Raycaster();
  // Update the raycaster with the camera and mouse position
  raycaster.setFromCamera(mouse, camera);

  // Calculate objects intersecting the ray
  const intersects = raycaster.intersectObjects(areaBoxes);
  if (intersects.length > 0) {
    selectedArea.value = intersects[0].object;
    // Reset the color of the previously clicked box (if it exists)
    previousSelectedAreas.forEach(area => {
      area.material.opacity = 0.0;
    });
    // Update the color of the currently clicked box
    selectedArea.value.material.color = new THREE.Color(theme.global.current.value.colors['secondary']);
    selectedArea.value.material.opacity = 0.5;
    // Store the current clicked box as the previous box
    previousSelectedAreas[0] = selectedArea.value;
  }
  else {
    if (previousSelectedAreas && previousSelectedAreas[0]) {
      previousSelectedAreas[0].material.opacity = 0.0;
      previousSelectedAreas.length = 0;
    }
  }
};

function filterFloorplan() {
  if (!filterValue.value) {
    resetFloorplan();
    return;
  }
  filterStore.setFilter(filterValue.value); // Update the filter in Pinia
  // Reset the color of the previously clicked box (if it exists)
  if (previousSelectedAreas) {
    previousSelectedAreas.forEach(area => {
      area.material.opacity = 0.0;
    });
  }
  previousSelectedAreas = [];

  // Remove old tooltips
  tooltipSprites.forEach(sprite => scene.remove(sprite));
  tooltipSprites = [];

  // Filter the area boxes based on the filter value
  const filteredAreas = areaBoxes.filter(area =>
    (area.userData.name && area.userData.name.toLowerCase().includes(filterValue.value.toLowerCase())) ||
    (area.userData.employee && area.userData.employee.displayName.toLowerCase().includes(filterValue.value.toLowerCase()))
  );
  // Update the color of the filtered areas
  filteredAreas.forEach(area => {
    area.material.color = new THREE.Color(theme.global.current.value.colors['secondary']);
    area.material.opacity = 0.5;
    previousSelectedAreas.push(area);

    // Create and position tooltip
    const info = area.userData.employee ? area.userData.employee.displayName : area.userData.name;
    const tooltip = createTooltipSprite2(info);
    tooltip.position.copy(area.position).add(new THREE.Vector3(0, 6, 0)); // Offset above area
    scene.add(tooltip);
    tooltipSprites.push(tooltip);    
  });
}

function resetFloorplan() {
  filterStore.resetFilter(); // Reset the filter in Pinia
  // Reset the color of the previously clicked box (if it exists)
  if (previousSelectedAreas) {
    previousSelectedAreas.forEach(area => {
      area.material.opacity = 0.0;
    });
  }
  // Remove old tooltips
  tooltipSprites.forEach(sprite => scene.remove(sprite));
  tooltipSprites = [];
}

const switchMode = async (mode, exit=false) => {
  if (currentMode.value) {
    switch (currentMode.value) {
      case 'buildings':
        exitBuildingScene();
        break;
      case 'floors':
        exitFloorScene();
        break;
      case 'areas':
        exitAreaScene();
        break;
      case 'departments':
        break;
    }
  }

  if (exit) return;
  switch (mode) {
    case 'buildings':
      currentMode.value = mode;
      await initBuildingScene();
      break;
    case 'floors':
      currentMode.value = mode;
      await initFloorScene(currentBuilding.value);
      break;
    case 'areas':
      currentMode.value = mode;
      await initAreaScene(currentBuilding.value, currentFloor.value);
      break;
    case 'departments':
      currentMode.value = mode;
      break;
  }
};

onMounted(async () => {
  await switchMode('buildings');
});

onBeforeUnmount(() => {
  switchMode(currentMode, true);
});
</script>

<template>
  <v-container fluid>
    <div class="office-simulation-container flex-grow-1">
      <div v-if="currentMode === 'areas'" ref="containerRef" class="canvas-container"></div>
      <div v-if="currentMode === 'buildings' && GOOGLE_MAPS_API_KEY != ''">
        <GoogleMap
          :key="isDarkMode"
          :api-key="GOOGLE_MAPS_API_KEY"
          mapId="a32f8b06945a5d44670e982b"
          style="width: 100%; height: 100vh"
          :zoom="1"
          :language="locale"
        >
          <AdvancedMarker
            v-for="(building, index) in buildingsData"
            :key="index"
            :options="{ position: { lat: building.location.lat, lng: building.location.lng }, title: building.name[locale] }"
          ></AdvancedMarker>
        </GoogleMap>
      </div>
      <div v-if="currentMode === 'floors' && GOOGLE_MAPS_API_KEY != ''">
        <GoogleMap
          :key="isDarkMode"
          :api-key="GOOGLE_MAPS_API_KEY"
          mapId="a32f8b06945a5d44670e982b"
          style="width: 100%; height: 100vh"
          :center="{ lat: currentBuildingData.location.lat, lng: currentBuildingData.location.lng }"
          :zoom="15"
          :language="locale"
        >
          <AdvancedMarker
            :options="{ position: { lat: currentBuildingData.location.lat, lng: currentBuildingData.location.lng }, title: currentBuildingData.name[locale] }"
          ></AdvancedMarker>
        </GoogleMap>
      </div>
    </div>
    <!-- Add search bar -->
    <v-card v-if="currentMode === 'areas'" class="search-bar" elevation="10">
      <v-text-field
        v-model="filterValue"
        type="text"
        density="compact"
        variant="outlined"
        :label="t('common.search')"
        prepend-inner-icon="mdi-magnify"
        single-line
        hide-details
        clearable
        @keyup.enter="filterFloorplan"
        @click:clear="resetFloorplan"
      ></v-text-field>
    </v-card>
    <v-navigation-drawer permanent rail>
      <v-list>
        <v-list-item
          prepend-icon="mdi-office-building-marker"
          value="buildings"
          :disabled="currentMode === 'buildings'"
          @click="switchMode('buildings')"
        ></v-list-item>
        <v-list-item
          prepend-icon="mdi-office-building"
          value="floors"
          :disabled="currentMode === 'floors'"
          @click="switchMode('floors')"
        ></v-list-item>
        <v-list-item
          prepend-icon="mdi-floor-plan"
          value="areas"
          :disabled="currentMode === 'areas' && currentBuilding != '' && currentFloor != ''"
          @click="switchMode('areas')"
        ></v-list-item>
        <v-list-item
          prepend-icon="mdi-account-group-outline"
          value="departments"
          :disabled="currentMode === 'departments'"
        ></v-list-item>
      </v-list>
    </v-navigation-drawer>
    <v-navigation-drawer :width="300">
      <v-card v-if="currentMode === 'buildings'" class="mx-auto my-8" variant="flat">
        <v-card-title>
          {{ t('officeFloorplan.buildings')}}
        </v-card-title>
        <v-card-item>
          <v-list v-for="building in buildingsData">
            <v-list-item class="cursor-pointer">
              <v-img
                class="mb-2"
                height="300px"
                :src="building.image_url"
                cover
                @click="setCurrentBuilding(building.id)"
              />
              <v-list-item-title>{{ building.name[locale] }}</v-list-item-title>
              <v-list-item-subtitle class="mb-4">{{ building.city[locale] }}, {{ building.country[locale] }}</v-list-item-subtitle>
            </v-list-item>
          </v-list>
        </v-card-item>
      </v-card>
      <v-card v-if="currentMode === 'floors'" class="mx-auto my-8" variant="flat">
        <v-card-title>
          <v-btn icon="mdi-keyboard-return" @click="switchMode('buildings')"></v-btn>
        </v-card-title>
        <v-card-title>
          {{ t('officeFloorplan.floors')}}
        </v-card-title>
        <v-card-item>
          <v-list>
            <v-list-item>
              <v-img
                class="mb-2"
                height="300px"
                :src="currentBuildingData.image_url"
                cover
              />
              <v-list-item-title>{{ currentBuildingData.name[locale] }}</v-list-item-title>
              <v-list-item-subtitle class="mb-4">{{ currentBuildingData.city[locale] }}, {{ currentBuildingData.country[locale] }}</v-list-item-subtitle>
              <v-list-item-subtitle class="mb-4">
                <v-icon icon="mdi-map-marker"></v-icon>
                {{ currentBuildingData.address[locale] }}
              </v-list-item-subtitle>
            </v-list-item>
            <v-list-item
              v-for="floor in currentBuildingData.floors" 
              :key="floor" 
            >
              <v-list-item @click="setCurrentFloor(floor.id)">{{ floor.id }}>
                <template v-slot:prepend>
                  <v-avatar>
                    <v-icon color="primary" icon="mdi-floor-plan"></v-icon>
                  </v-avatar>
                </template>
                <v-list-item-title></v-list-item-title>
              </v-list-item>
              <v-list-item v-for="department in floor.departments" :key="floor.id">
                <template v-slot:prepend>
                  <v-avatar>
                    <v-icon></v-icon>
                  </v-avatar>
                </template>
                <v-list-item-title>{{ department.name[locale] }}</v-list-item-title>
              </v-list-item>
            </v-list-item>
          </v-list>
        </v-card-item>
      </v-card>
      <v-card v-if="currentMode === 'areas'" class="mx-auto my-8" variant="flat">
        <v-card-title>
          <v-btn icon="mdi-keyboard-return" @click="switchMode('floors')"></v-btn>
        </v-card-title>
        <v-card-title>
          {{ t('officeFloorplan.areaInformation')}}
        </v-card-title>
        <v-card-item>
          <v-list density="compact" v-if="selectedArea && filterValue === ''">
            <v-list-item prepend-icon="mdi-office-building">{{ selectedArea.userData.building }}</v-list-item>
            <v-list-item prepend-icon="mdi-floor-plan">{{ selectedArea.userData.floor }}</v-list-item>
            <v-list-item prepend-icon="mdi-selection">{{ selectedArea.userData.name }}</v-list-item>
            <v-list-item class="mt-10">
              <v-card v-if="selectedArea.userData.employee" class="text-center pa-3">
                <v-avatar
                  :image=" getPhoto(selectedArea.userData.employee) "
                  size="80"
                ></v-avatar>
                <v-card-item>
                  <v-card-title>{{ selectedArea.userData.employee.displayName }}</v-card-title>
                  <v-card-subtitle>{{ selectedArea.userData.employee.jobTitle }}</v-card-subtitle>
                  <v-card-subtitle>{{ selectedArea.userData.employee.department }}</v-card-subtitle>
                  <v-card-subtitle>
                    <v-icon color="primary" icon="mdi-email-outline"></v-icon>
                    {{ selectedArea.userData.employee.mail }}
                  </v-card-subtitle>
                </v-card-item>
              </v-card>
            </v-list-item>
          </v-list>
        </v-card-item>
      </v-card>
    </v-navigation-drawer>
  </v-container>
</template>

<style scoped>
.office-simulation-container {
  width: 100%;
  height: 90vh;
  background-color: rgb(var(--v-theme-background));
  position: relative;
}

.canvas-container {
  width: 100%;
  height: 100%;
}

.floating-card {
  position: absolute;
  top: 100px;
  left: 50px;
  padding: 20px;
  opacity: 0.75;
}

.search-bar {
  position: absolute;
  top: 100px;
  right: 20px;
  width: 350px;
  padding: 20px;
  opacity: 0.75;
  backdrop-filter: blur(5px);
  background-color: rgb(var(--v-theme-surface-lighten-1));
  color: rgb(var(--v-theme-on-surface));
}
</style>
