<template>
    <v-menu>
      <template v-slot:activator="{ props }">
        <v-btn v-bind="props" variant="plain" class="ml-4">
          {{ providerName }} - {{ modelName }}
        </v-btn>
      </template>
      <v-list>
        <template v-for="(provider, providerKey) in providers" :key="providerKey">
          <!-- Collapsible Provider Group -->
          <v-list-group>
            <template v-slot:activator="{ props }">
              <v-list-item 
                v-bind="props" 
                variant="tonal" 
                :class="{ 'selected-provider': providerKey === providerName }"
              >
                <v-list-item-title>
                  {{ providerKey }}
                </v-list-item-title>
              </v-list-item>
            </template>
            <!-- Model Selection Items -->
            <template v-for="model in provider.models" :key="model.name">
              <v-list-item 
                variant="tonal" 
                @click="selectModel(providerKey, model.name)" 
                :class="{ 'selected-model': model.name === modelName }"
              >
                <v-list-item-title>{{ model.name }}</v-list-item-title>
                <v-list-item-subtitle>
                  {{ model.description }} (Released: {{ model.release_date }})
                </v-list-item-subtitle>
              </v-list-item>
            </template>
          </v-list-group>
        </template>
      </v-list>
    </v-menu>
  </template>
  
  <script setup>  
  const props = defineProps({
    providerName: String,
    modelName: String,
    providers: Object,
  })
  
  const emit = defineEmits(['selectModel'])
  
  const selectModel = (provider, model) => {
    emit('selectModel', { provider, model })
  }
  </script>
  <style scoped>
  .selected-provider {
    background-color: rgb(var(--v-theme-primary)) !important; /* Highlight provider */
    color: rgb(var(--v-theme-on-primary)) !important;
  }
  
  .selected-model {
    background-color: rgb(var(--v-theme-primary)) !important; /* Highlight model */
    color: rgb(var(--v-theme-on-primary)) !important;
  }
  </style>