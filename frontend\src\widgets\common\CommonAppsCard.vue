<script setup>
import { ref } from 'vue'
import { useI18n } from 'vue-i18n'

const { t, locale } = useI18n()

const links = ref([
  { title: 'Foodcourt', icon: 'mdi-account-group', color: 'blue', url: 'https://foodcourt.moxa.com/foodCourt/' },
  { title: 'BPM', icon: 'mdi-leaf', color: 'green', url: 'https://bpm.moxa.com/' },
  { title: 'Moxa College', icon: 'mdi-cash', color: 'green-darken-2', url: 'https://moxa.learnupon.com/learner-dashboard' },
  { title: 'Moxa Connect', icon: 'mdi-gavel', color: 'deep-purple', url: 'https://moxaconnect.moxa.com/' },
  { title: 'Moxa Bravo', icon: 'mdi-domain', color: 'blue-grey', url: 'https://moxa.achievers.com/' },
  { title: 'E-HR', icon: 'mdi-check-decagram', color: 'teal', url: 'https://ehr.moxa.com/' },
  { title: 'Attendance & Payroll', icon: 'mdi-laptop', color: 'indigo', url: 'https://apollo.mayohr.com/tube' },
  { title: 'GeDCC', icon: 'mdi-factory', color: 'brown', url: 'http://global-gedcc.moxa.com/' }
])

</script>

<template>
  <v-card height="100%" class="useful-links-card">
    <v-card-title class="d-flex align-center">
      <v-icon icon="mdi-link" color="primary" class="mr-2"></v-icon>
      {{ t('usefulApps.common') }}
    </v-card-title>
    <v-card-text>
      <div class="d-flex flex-column align-start">
        <v-btn
          v-for="link in links"
          :key="link.title"
          :color="link.color"
          :prepend-icon="link.icon"
          class="ma-1 w-100"
          variant="tonal"
          :href="link.url"
          target="_blank"
        >
          {{ link.title }}
        </v-btn>
      </div>
    </v-card-text>
  </v-card>
</template>