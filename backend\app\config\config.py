from pydantic_settings import BaseSettings

class Settings(BaseSettings):
    # ... other settings ...

    # For Redis
    CELERY_BROKER_URL: str = "redis://localhost:6379/0"
    CELERY_RESULT_BACKEND: str = "redis://localhost:6379/1"

    # Or for RabbitMQ
    # CELERY_BROKER_URL: str = "amqp://guest:guest@localhost:5672//"
    # CELERY_RESULT_BACKEND: str = "rpc://"

    class Config:
        env_file = ".env"

settings = Settings()