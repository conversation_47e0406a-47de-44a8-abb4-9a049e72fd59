Stack trace:
Frame         Function      Args
0007FFFFAC00  00021005FE8E (000210285F68, 00021026AB6E, 000000000000, 0007FFFF9B00) msys-2.0.dll+0x1FE8E
0007FFFFAC00  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFAED8) msys-2.0.dll+0x67F9
0007FFFFAC00  000210046832 (000210286019, 0007FFFFAAB8, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFFAC00  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFFAC00  000210068E24 (0007FFFFAC10, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFFAEE0  00021006A225 (0007FFFFAC10, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFB4F610000 ntdll.dll
7FFB4D920000 KERNEL32.DLL
7FFB4CDB0000 KERNELBASE.dll
7FFB4D740000 USER32.dll
7FFB4D1E0000 win32u.dll
7FFB4D8F0000 GDI32.dll
7FFB4CC90000 gdi32full.dll
7FFB4C930000 msvcp_win.dll
7FFB4CB70000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFB4E890000 advapi32.dll
7FFB4DEB0000 msvcrt.dll
7FFB4D4C0000 sechost.dll
7FFB4C9D0000 bcrypt.dll
7FFB4E970000 RPCRT4.dll
7FFB4C1A0000 CRYPTBASE.DLL
7FFB4D160000 bcryptPrimitives.dll
7FFB4D430000 IMM32.DLL
