import axios from 'axios'

const API_URL = import.meta.env.VITE_BACKEND_API_URL || 'http://localhost:8000'

const userClient = axios.create({
  baseURL: API_URL,
  headers: {
    'Content-Type': 'application/json'
  }
})


export const getUserProfile = async (userId) => {
  try {
    const response = await fetch(`${API_URL}/user/profile`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'user_id': userId,
      }}
    )
    const data = await response.json()
    return data
  } catch (error) {
      console.error('Error fetching user profile:', error)
      return {
        success: false,
        error: error.response?.data?.error || 'Failed to get user profile',
        timestamp: new Date().toLocaleTimeString()
      }
  }
}

export const getEmployeeProfile = async (userId) => {
  try {
    const response = await fetch(`${API_URL}/users/${userId}/employee_profile`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      }}
    )
    const data = await response.json()
    // console.log("getEmployeeProfile: ", data)
    return data
  } catch (error) {
      console.error('Error fetching employee profile:', error)
      return {
        success: false,
        error: error.response?.data?.error || 'Failed to get employee profile',
        timestamp: new Date().toLocaleTimeString()
      }
  }
}

export const getOrgChart = async () => {
  try {
    const response = await fetch(`${API_URL}/orgchart`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      }
    });
    const data = await response.json();
    // console.log("getOrgChart: ", data);
    return data;
  } catch (error) {
    console.error('Error fetching orgchart:', error);
    return {
      success: false,
      error: error.message || 'Failed to get orgchart',
      timestamp: new Date().toLocaleTimeString()
    };
  }
}

export const getUserPhoto = async (userId) => {
  try {
    const response = await fetch(`${API_URL}/users/${userId}/photo`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      }
    });
    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Error fetching user photo:', error);
    return {
      success: false,
      error: error.message || 'Failed to get user photo',
      timestamp: new Date().toLocaleTimeString()
    };
  }
}


export const getUserSettings = async (userId) => {
  try {
    const response = await fetch(`${API_URL}/users/${userId}/settings`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      }
    });
    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Error fetching user settings:', error);
    return {
      success: false,
      error: error.message || 'Failed to get user settings',
      timestamp: new Date().toLocaleTimeString()
    };
  }
}

export const setUserSettings = async (userId, settings) => {
  try {
    const response = await fetch(`${API_URL}/users/${userId}/settings`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(settings)
    });
    const data = await response.json()
    return data
  } catch (error) {
      console.error('Error setting user settings:', error)
      return {
        success: false,
        error: error.response?.data?.error || 'Failed to set user settings',
        timestamp: new Date().toLocaleTimeString()
      }
  }
}


export const getUserPreferences = async (userId) => {
  try {
    const response = await fetch(`${API_URL}/users/${userId}/preferences`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      }
    });
    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Error fetching user preferences:', error);
    return {
      success: false,
      error: error.message || 'Failed to get user preferences',
      timestamp: new Date().toLocaleTimeString()
    };
  }
}

export const setUserPreferences = async (userId, preferences) => {
  try {
    const response = await fetch(`${API_URL}/users/${userId}/preferences`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(preferences)
    });
    const data = await response.json()
    return data
  } catch (error) {
      console.error('Error setting user preferences:', error)
      return {
        success: false,
        error: error.response?.data?.error || 'Failed to set user perferences',
        timestamp: new Date().toLocaleTimeString()
      }
  }
}
