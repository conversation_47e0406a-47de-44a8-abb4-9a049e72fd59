<script setup>
import { ref } from 'vue'
import { useI18n } from 'vue-i18n';
import { useUserStore } from '@/stores/userStore';

const { t, locale } = useI18n();

const userStore = useUserStore()
const benefitPoints = userStore.getBenefitPoints
</script>

<template>
  <v-card height="100%" class="benefit-points-card">
    <v-card-title class="d-flex align-center">
      <v-icon icon="mdi-gift" color="primary" class="mr-2"></v-icon>
      {{ t('benefitPoints.title') }}
    </v-card-title>
    <v-card-text>
      <div class="d-flex justify-space-between align-center mb-2">
        <div>
          <div class="text-subtitle-2">{{ t('benefitPoints.remaining') }} / {{ t('benefitPoints.granted') }}</div>
          <div class="text-h5 font-weight-bold primary--text">{{ benefitPoints.remaining }} / {{ benefitPoints.granted }}</div>
        </div>
      </div>
      <v-progress-linear
        :model-value="(benefitPoints.remaining / benefitPoints.granted) * 100"
        color="success"
        height="10"
        rounded
      ></v-progress-linear>
    </v-card-text>
  </v-card>
</template>