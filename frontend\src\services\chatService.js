import axios from 'axios'

const API_URL = import.meta.env.VITE_BACKEND_API_URL || 'http://localhost:8000'
const RAG_API_URL = import.meta.env.VITE_RAG_API_URL || 'http://localhost:9621'

const chatClient = axios.create({
  baseURL: API_URL,
  headers: {
    'Content-Type': 'application/json'
  }
})

export const getAvailableTools = async () => {
  try {
    const response = await chatClient.get('/chat/tools', {})
    return response.data
  } catch (error) {
    // console.error('Error calling /chat/tools API:', error)
    return {
      success: false,
      error: error.response?.data?.error || 'Failed to get available tools',
      timestamp: new Date().toLocaleTimeString()
    }
  }
}

export const startNewChat = async (userId, title) => {
  try {
    const response = await chatClient.post(`${API_URL}/chat/start`, {
      title,
    },
    {
      headers: {
        'user_id': userId,
      }
    })
    return response.data
  } catch (error) {
    console.error('Error fetching sessions:', error)
    return {
      success: false,
      error: error.response?.data?.error || 'Failed to start a new chat',
      timestamp: new Date().toLocaleTimeString()
    }
  }
}

export const getSession = async (userId, sessionId) => {
  try {
    const response = await chatClient.get(`/chat/sessions/${sessionId}`, {
      headers: {
        'user_id': userId,
        'Content-Type': 'application/json'
      }
    })
    return {
      success: true,
      data: response.data,
      timestamp: new Date().toLocaleTimeString()
    }
  } catch (error) {
    // console.error('Error fetching session:', error)
    return {
      success: false,
      error: error.response?.data?.error || 'Failed to get session',
      timestamp: new Date().toLocaleTimeString()
    }
  }
}

export const updateMessageFeedback = async (userId, sessionId, messageId, feedback) => {
  try {
    const response = await chatClient.post(`/chat/feedback/${sessionId}/${messageId}`, {
        feedback: feedback
      },
      {
        headers: {
          'user_id': userId,
          'Content-Type': 'application/json'
        }
    })
    return {
      success: true,
      data: response.data,
      timestamp: new Date().toLocaleTimeString()
    }
  }
  catch (error) {
    console.error('Error fetching sessions:', error)
    return {
      success: false,
      error: error.response?.data?.error || 'Failed to update message feedback',
      timestamp: new Date().toLocaleTimeString()
    }
  }
}

export const getMessageFeedback = async (userId, sessionId, messageId) => {
  try {
    const response = await chatClient.get(`/chat/feedback/${sessionId}/${messageId}`, {
      headers: {
        'user_id': userId,
        'Content-Type': 'application/json'
      }
    })
    return {
      success: true,
      data: response.data,
      timestamp: new Date().toLocaleTimeString()
    }
  }
  catch (error) {
    console.error('Error fetching sessions:', error)
    return {
      success: false,
      error: error.response?.data?.error || 'Failed to get message feedback',
      timestamp: new Date().toLocaleTimeString()
    }
  }
}


export const getSessions = async (userId) => {
  try {
    const response = await chatClient.get(`/chat/sessions`,
      {
        headers: {
          'user_id': userId,
          'Content-Type': 'application/json'
        }
      }
    )
    return {
      success: true,
      data: response.data,
      timestamp: new Date().toLocaleTimeString()
    }
  } catch (error) {
    // console.error('Error fetching sessions:', error)
    return {
      success: false,
      error: error.response?.data?.error || 'Failed to get sessions',
      timestamp: new Date().toLocaleTimeString()
    }
  }
}


export const deleteSession = async (userId, sessionId) => {
  try {
    const response = await chatClient.delete(`/chat/sessions/${sessionId}`,
      {
        headers: {
          'user_id': userId,
          'Content-Type': 'application/json'
        }
      }
    )
    return {
      success: true,
      data: response.data,
      timestamp: new Date().toLocaleTimeString()
    }
  }
  catch (error) {
    console.error('Error fetching sessions:', error)
    return {
      success: false,
      error: error.response?.data?.error || 'Failed to delete session',
      timestamp: new Date().toLocaleTimeString()
    }
  }
}

export const deleteAllSessions = async (userId) => {
  try {
    const response = await chatClient.delete(`/chat/sessions`,
      {
        headers: {
          'user_id': userId,
          'Content-Type': 'application/json'
        }
      }
    )
    return {
      success: true,
      data: response.data,
      timestamp: new Date().toLocaleTimeString()
    }
  }
  catch (error) {
    console.error('Error fetching sessions:', error)
    return {
      success: false,
      error: error.response?.data?.error || 'Failed to delete all sessions',
      timestamp: new Date().toLocaleTimeString()
    }
  }
}


export const sendChatMsg = async (user_id, session_id, query, enabledTools, mode, providerName, modelName) => {
  try {
    const response = await chatClient.post(`${API_URL}/chat/query`, {
        session_id,
        query,
        enabled_tools: enabledTools,
        mode: mode,
        provider_name: providerName,
        model_name: modelName
      },
      {
        headers: {
          'user_id': user_id,
        }
      }
    )
    console.log('Response from sendChatMsg:', response.data)
    return response.data
  } catch (error) {
    console.error('Error calling Chat API:', error)
    return {
      success: false,
      error: error.response?.data?.error || 'Failed to get response from AI',
      timestamp: new Date().toLocaleTimeString()
    }
  }
}

export const sendStreamingChatMsg = async (user_id, session_id, query, enabledTools, mode, providerName, modelName, onChunk) => {
  try {
    const response = await fetch(`${API_URL}/chat/query/stream`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'user_id': user_id,
        },
        body: JSON.stringify({
          session_id,
          query,
          enabled_tools: enabledTools,
          mode: mode,
          provider_name: providerName,
          model_name: modelName
        })
      }
    )

    const reader = response.body.getReader()
    const decoder = new TextDecoder()

    while (true) {
      const { value, done } = await reader.read()
      if (done) break

      const chunk = decoder.decode(value)
      const lines = chunk.split('\n')

      for (const line of lines) {
        if (line.startsWith('data: ')) {
          try {
            const event = JSON.parse(line.slice(6))
            onChunk(event) // Pass the full event object
          } catch (e) {
            console.error('Error parsing streaming response:', e)
          }
        }
      }
    }
  } catch (error) {
    console.error('Error in streaming chat:', error)
    const errorMessage = error?.response?.data?.error || error?.message || 'Failed to get streaming response from AI'
    onChunk({
      type: "error",
      data: errorMessage,
      timestamp: new Date().toLocaleTimeString()
    })
  }
}

export const getTTS = async (text) => {
  try {
    const response = await chatClient.get(`/chat/query/tts/${text}`,

      {
        headers: {
          'Content-Type': 'application/json'
        }
      }
    )
    console.log('Response from getTTS:', response.data)
    return {
      success: true,
      data: response.data,
      timestamp: new Date().toLocaleTimeString()
    }
  } catch (error) {
    return {
      success: false,
      error: error.response?.data?.error || 'Failed to get TTS audio',
      timestamp: new Date().toLocaleTimeString()
    }
  }
}


export const getProviders = async () => {
  try {
    const response = await chatClient.get(`/chat/providers`,
      {
        headers: {
          'Content-Type': 'application/json'
        }
      }
    )
    return {
      success: true,
      data: response.data,
      timestamp: new Date().toLocaleTimeString()
    }
  } catch (error) {
    // console.error('Error fetching providers:', error)
    return {
      success: false,
      error: error.response?.data?.error || 'Failed to get providers',
      timestamp: new Date().toLocaleTimeString()
    }
  }
}
