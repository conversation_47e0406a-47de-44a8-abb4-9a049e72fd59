import { ref } from 'vue'

// Store for managing office areas and employee assignments
export const areas = ref([])
export const employees = ref(
  [
    {id: "DAVIDWH_CHEN", displayName:"<PERSON>", chineseName: "陳偉弘", areaId: 'i-Tower_22F_seat_001',picture:{"large":"https://randomuser.me/api/portraits/men/33.jpg",medium:"https://randomuser.me/api/portraits/med/men/33.jpg",thumbnail:"https://randomuser.me/api/portraits/thumb/men/33.jpg"}},
    {id: "RICKY_CHANG", displayName:"<PERSON>", chineseName: "張瑞文", areaId: 'i-Tower_22F_seat_002',picture:{"large":"https://randomuser.me/api/portraits/men/32.jpg",medium:"https://randomuser.me/api/portraits/med/men/32.jpg",thumbnail:"https://randomuser.me/api/portraits/thumb/men/32.jpg"}},
    {id: "NICOLESR_CHEN", displayName:"<PERSON>", chineseName: "陳欣蓉", areaId:  'i-Tower_22F_seat_066',picture:{"large":"https://randomuser.me/api/portraits/women/89.jpg",medium:"https://randomuser.me/api/portraits/med/women/89.jpg",thumbnail:"https://randomuser.me/api/portraits/thumb/women/89.jpg"}},
    {id: "ERIC_CHEN", displayName:"Eric Chen", chineseName: "陳威全", areaId:  null,picture:{"large":"https://randomuser.me/api/portraits/men/53.jpg",medium:"https://randomuser.me/api/portraits/med/men/53.jpg",thumbnail:"https://randomuser.me/api/portraits/thumb/men/53.jpg"}},
    {id: "SHELLY_LEE", displayName:"Shelly Lee", chineseName: "李修懿", areaId:  null,picture:{"large":"https://randomuser.me/api/portraits/women/94.jpg",medium:"https://randomuser.me/api/portraits/med/women/94.jpg",thumbnail:"https://randomuser.me/api/portraits/thumb/women/94.jpg"}},
    {id: "ROBINSON_CHEN", displayName:"Robinson Chen", chineseName: "陳克昌", areaId:  'i-Tower_22F_seat_054',picture:{"large":"https://randomuser.me/api/portraits/men/71.jpg",medium:"https://randomuser.me/api/portraits/med/men/71.jpg",thumbnail:"https://randomuser.me/api/portraits/thumb/men/71.jpg"}},
    {id: "RAYMOND_YANG", displayName:"Raymond Yang", chineseName: "楊奕駿", areaId:  'i-Tower_22F_seat_058',picture:{"large":"https://randomuser.me/api/portraits/women/49.jpg",medium:"https://randomuser.me/api/portraits/med/women/49.jpg",thumbnail:"https://randomuser.me/api/portraits/thumb/women/49.jpg"}},
    {id: "KARENWW_YI", displayName:"Karen WW Yi", chineseName: "易文薇", areaId:  'i-Tower_22F_seat_055',picture:{"large":"https://randomuser.me/api/portraits/women/57.jpg",medium:"https://randomuser.me/api/portraits/med/women/57.jpg",thumbnail:"https://randomuser.me/api/portraits/thumb/women/57.jpg"}},
    {id: "BECKYBE_LAI", displayName:"Becky BE Lai", chineseName: "賴貝兒", areaId:  'i-Tower_22F_seat_057',picture:{"large":"https://randomuser.me/api/portraits/women/96.jpg",medium:"https://randomuser.me/api/portraits/med/women/96.jpg",thumbnail:"https://randomuser.me/api/portraits/thumb/women/96.jpg"}},
    {id: "STEVENHX_HU", displayName:"Steven HX Hu", chineseName: "胡竑璽", areaId: 'i-Tower_22F_seat_060',picture:{"large":"https://randomuser.me/api/portraits/women/48.jpg",medium:"https://randomuser.me/api/portraits/med/women/48.jpg",thumbnail:"https://randomuser.me/api/portraits/thumb/women/48.jpg"}},
    {id: "WENDYYH_CHEN", displayName:"Wendy YH Chen", chineseName: "陳又暄", areaId:  'i-Tower_22F_seat_056',picture:{"large":"https://randomuser.me/api/portraits/women/91.jpg",medium:"https://randomuser.me/api/portraits/med/women/91.jpg",thumbnail:"https://randomuser.me/api/portraits/thumb/women/91.jpg"}},
    {id: "JEFFJF_LUO", displayName:"JEFF JF LUO", chineseName: "羅志鋒", areaId: 'i-Tower_22F_seat_059',picture:{"large":"https://randomuser.me/api/portraits/men/41.jpg",medium:"https://randomuser.me/api/portraits/med/men/41.jpg",thumbnail:"https://randomuser.me/api/portraits/thumb/men/41.jpg"}},
    {id: "ANDYJC_HUANG", displayName:"Andy JC Huang", chineseName: "黃瑞欽", areaId: 'i-Tower_22F_seat_012',picture:{"large":"https://randomuser.me/api/portraits/women/74.jpg",medium:"https://randomuser.me/api/portraits/med/women/74.jpg",thumbnail:"https://randomuser.me/api/portraits/thumb/women/74.jpg"}},
    {id: "CATLYNWL_CHANG", displayName:"Catlyn WL Chang", chineseName: "張琬琳", areaId: 'i-Tower_22F_seat_063',picture:{"large":"https://randomuser.me/api/portraits/women/54.jpg",medium:"https://randomuser.me/api/portraits/med/women/54.jpg",thumbnail:"https://randomuser.me/api/portraits/thumb/women/54.jpg"}},
    {id: "LESLIEPC_CHANG", displayName:"Leslie PC Chang", chineseName: "張沛晴", areaId: 'i-Tower_22F_seat_061',picture:{"large":"https://randomuser.me/api/portraits/women/41.jpg",medium:"https://randomuser.me/api/portraits/med/women/41.jpg",thumbnail:"https://randomuser.me/api/portraits/thumb/women/41.jpg"}},
    {id: "DIANAPH_CHEN", displayName:"Diana PH Chen", chineseName: "陳姵秀", areaId: 'i-Tower_22F_seat_052',picture:{"large":"https://randomuser.me/api/portraits/men/33.jpg",medium:"https://randomuser.me/api/portraits/med/men/33.jpg",thumbnail:"https://randomuser.me/api/portraits/thumb/men/33.jpg"}},
    {id: "GRAHAM_LIN", displayName:"Graham Lin", chineseName: "林國全", areaId: 'i-Tower_22F_seat_016',picture:{"large":"https://randomuser.me/api/portraits/women/45.jpg",medium:"https://randomuser.me/api/portraits/med/women/45.jpg",thumbnail:"https://randomuser.me/api/portraits/thumb/women/45.jpg"}},
    {id: "JACKYY_SHIH", displayName:"Jack YY Shih", chineseName: "施昱宇", areaId: 'i-Tower_22F_seat_017',picture:{"large":"https://randomuser.me/api/portraits/women/92.jpg",medium:"https://randomuser.me/api/portraits/med/women/92.jpg",thumbnail:"https://randomuser.me/api/portraits/thumb/women/92.jpg"}},
    {id: "ROXWC_CHUANG", displayName:"Rox WC Chuang", chineseName: "莊萬慶", areaId: 'i-Tower_22F_seat_018',picture:{"large":"https://randomuser.me/api/portraits/men/64.jpg",medium:"https://randomuser.me/api/portraits/med/men/64.jpg",thumbnail:"https://randomuser.me/api/portraits/thumb/men/64.jpg"}},
    {id: "VINCENT_CHEN", displayName:"Vincent Chen", chineseName: "陳玟昇", areaId: 'i-Tower_22F_seat_028',picture:{"large":"https://randomuser.me/api/portraits/men/47.jpg",medium:"https://randomuser.me/api/portraits/med/men/47.jpg",thumbnail:"https://randomuser.me/api/portraits/thumb/men/47.jpg"}},
    {id: "SAMPH_WU", displayName:"Sam PH Wu", chineseName: "吳柏欣", areaId: 'i-Tower_22F_seat_027',picture:{"large":"https://randomuser.me/api/portraits/men/53.jpg",medium:"https://randomuser.me/api/portraits/med/men/53.jpg",thumbnail:"https://randomuser.me/api/portraits/thumb/men/53.jpg"}},
    {id: "CHESTERCS_LIU", displayName:"Chester CS Liu", chineseName: "劉宸亘", areaId: 'i-Tower_22F_seat_025',picture:{"large":"https://randomuser.me/api/portraits/women/47.jpg",medium:"https://randomuser.me/api/portraits/med/women/47.jpg",thumbnail:"https://randomuser.me/api/portraits/thumb/women/47.jpg"}},
    {id: "ELININ_YEH", displayName:"Elin IN Yeh", chineseName: "葉怡寧", areaId: 'i-Tower_22F_seat_026',picture:{"large":"https://randomuser.me/api/portraits/women/86.jpg",medium:"https://randomuser.me/api/portraits/med/women/86.jpg",thumbnail:"https://randomuser.me/api/portraits/thumb/women/86.jpg"}},
    {id: "ZIVSC_CHO", displayName:"Ziv SC Cho", chineseName: "卓劭昌", areaId: 'i-Tower_22F_seat_023',picture:{"large":"https://randomuser.me/api/portraits/men/25.jpg",medium:"https://randomuser.me/api/portraits/med/men/25.jpg",thumbnail:"https://randomuser.me/api/portraits/thumb/men/25.jpg"}},
    {id: "SEANHH_LIANG", displayName:"Sean HH Liang", chineseName: "梁賀翔", areaId: 'i-Tower_22F_seat_078',picture:{"large":"https://randomuser.me/api/portraits/men/87.jpg",medium:"https://randomuser.me/api/portraits/med/men/87.jpg",thumbnail:"https://randomuser.me/api/portraits/thumb/men/87.jpg"}},
    {id: "GAVIN_LAI", displayName:"Gavin Lai", chineseName: "賴正宗", areaId: 'i-Tower_22F_seat_068',picture:{"large":"https://randomuser.me/api/portraits/men/0.jpg",medium:"https://randomuser.me/api/portraits/med/men/0.jpg",thumbnail:"https://randomuser.me/api/portraits/thumb/men/0.jpg"}},
    {id: "ALLEN_WANG", displayName:"Allen Wang", chineseName: "王永裕", areaId: 'i-Tower_22F_seat_074',picture:{"large":"https://randomuser.me/api/portraits/men/29.jpg",medium:"https://randomuser.me/api/portraits/med/men/29.jpg",thumbnail:"https://randomuser.me/api/portraits/thumb/men/29.jpg"}},
    {id: "WINSTONYJ_WANG", displayName:"Winston YJ Wang", chineseName: "王翊家", areaId: 'i-Tower_22F_seat_071',picture:{"large":"https://randomuser.me/api/portraits/women/1.jpg",medium:"https://randomuser.me/api/portraits/med/women/1.jpg",thumbnail:"https://randomuser.me/api/portraits/thumb/women/1.jpg"}},
    {id: "JEIMMYCM_HUANG", displayName:"Jeimmy CM Huang", chineseName: "黃晴敏", areaId: 'i-Tower_22F_seat_069',picture:{"large":"https://randomuser.me/api/portraits/women/18.jpg",medium:"https://randomuser.me/api/portraits/med/women/18.jpg",thumbnail:"https://randomuser.me/api/portraits/thumb/women/18.jpg"}},
    {id: "POLLYHY_CHEN", displayName:"Polly HY Chen", chineseName: "陳慧儀", areaId: 'i-Tower_22F_seat_065',picture:{"large":"https://randomuser.me/api/portraits/women/74.jpg",medium:"https://randomuser.me/api/portraits/med/women/74.jpg",thumbnail:"https://randomuser.me/api/portraits/thumb/women/74.jpg"}},
    {id: "DEREKHJ_SUN", displayName:"Derek HJ Sun", chineseName: "孫浩然", areaId: 'i-Tower_22F_seat_073',picture:{"large":"https://randomuser.me/api/portraits/women/2.jpg",medium:"https://randomuser.me/api/portraits/med/women/2.jpg",thumbnail:"https://randomuser.me/api/portraits/thumb/women/2.jpg"}},
    {id: "LEONLY_CHANG", displayName:"Leon LY Chang", chineseName: "張立佑", areaId: 'i-Tower_22F_seat_062',picture:{"large":"https://randomuser.me/api/portraits/women/10.jpg",medium:"https://randomuser.me/api/portraits/med/women/10.jpg",thumbnail:"https://randomuser.me/api/portraits/thumb/women/10.jpg"}},
    {id: "ANNIEAT_CHIEN", displayName:"Annie AT Chien", chineseName: "簡安婷", areaId: 'i-Tower_22F_seat_070',picture:{"large":"https://randomuser.me/api/portraits/women/13.jpg",medium:"https://randomuser.me/api/portraits/med/women/13.jpg",thumbnail:"https://randomuser.me/api/portraits/thumb/women/13.jpg"}},
    {id: "DANNYKJ_CHIU", displayName:"Danny KJ Chiu", chineseName: "邱冠融", areaId: 'i-Tower_22F_seat_077',picture:{"large":"https://randomuser.me/api/portraits/women/16.jpg",medium:"https://randomuser.me/api/portraits/med/women/16.jpg",thumbnail:"https://randomuser.me/api/portraits/thumb/women/16.jpg"}},
    {id: "DONCT_YANG", displayName:"Don CT Yang", chineseName: "楊振東", areaId: 'i-Tower_22F_seat_080',picture:{"large":"https://randomuser.me/api/portraits/women/44.jpg",medium:"https://randomuser.me/api/portraits/med/women/44.jpg",thumbnail:"https://randomuser.me/api/portraits/thumb/women/44.jpg"}},
    {id: "KALENKL_YANG", displayName:"Kalen KL Yang", chineseName: "楊凱倫", areaId: 'i-Tower_22F_seat_075',picture:{"large":"https://randomuser.me/api/portraits/men/16.jpg",medium:"https://randomuser.me/api/portraits/med/men/16.jpg",thumbnail:"https://randomuser.me/api/portraits/thumb/men/16.jpg"}},
    {id: "ENZOYC_SU", displayName:"Enzo YC Su", chineseName: "蘇揚致", areaId: 'i-Tower_22F_seat_072',picture:{"large":"https://randomuser.me/api/portraits/women/48.jpg",medium:"https://randomuser.me/api/portraits/med/women/48.jpg",thumbnail:"https://randomuser.me/api/portraits/thumb/women/48.jpg"}},
    {id: "EMMAHH_HUNG", displayName:"Emma HH Hung", chineseName: "洪湘涵", areaId: 'i-Tower_22F_seat_079',picture:{"large":"https://randomuser.me/api/portraits/men/26.jpg",medium:"https://randomuser.me/api/portraits/med/men/26.jpg",thumbnail:"https://randomuser.me/api/portraits/thumb/men/26.jpg"}},
    {id: "RICHARDJC_WU", displayName:"Richard JC Wu", chineseName: "吳睿哲", areaId: 'i-Tower_22F_seat_077',picture:{"large":"https://randomuser.me/api/portraits/women/95.jpg",medium:"https://randomuser.me/api/portraits/med/women/95.jpg",thumbnail:"https://randomuser.me/api/portraits/thumb/women/95.jpg"}},
    {id: "SHANIA_LEE", displayName:"Shania Lee", chineseName: "李雅玲", areaId: 'i-Tower_22F_seat_030',picture:{"large":"https://randomuser.me/api/portraits/women/68.jpg",medium:"https://randomuser.me/api/portraits/med/women/68.jpg",thumbnail:"https://randomuser.me/api/portraits/thumb/women/68.jpg"}},
    {id: "CLAIREYC_HUANG", displayName:"Claire YC Huang", chineseName: "黃郁靜", areaId: 'i-Tower_22F_seat_011',picture:{"large":"https://randomuser.me/api/portraits/men/78.jpg",medium:"https://randomuser.me/api/portraits/med/men/78.jpg",thumbnail:"https://randomuser.me/api/portraits/thumb/men/78.jpg"}},
    {id: "MATTCM_LIN", displayName:"Matt CM Lin", chineseName: "林見明", areaId: 'i-Tower_22F_seat_008',picture:{"large":"https://randomuser.me/api/portraits/men/85.jpg",medium:"https://randomuser.me/api/portraits/med/men/85.jpg",thumbnail:"https://randomuser.me/api/portraits/thumb/men/85.jpg"}},
    {id: "ALICESY_PENG", displayName:"Alice SY Peng", chineseName: "彭思瑀", areaId: 'i-Tower_22F_seat_006',picture:{"large":"https://randomuser.me/api/portraits/women/56.jpg",medium:"https://randomuser.me/api/portraits/med/women/56.jpg",thumbnail:"https://randomuser.me/api/portraits/thumb/women/56.jpg"}},
    {id: "ZERAYH_HUANG", displayName:"Zera YH Huang", chineseName: "黃雅歆", areaId: 'i-Tower_22F_seat_007',picture:{"large":"https://randomuser.me/api/portraits/women/30.jpg",medium:"https://randomuser.me/api/portraits/med/women/30.jpg",thumbnail:"https://randomuser.me/api/portraits/thumb/women/30.jpg"}},
    {id: "VETERWC_HSU", displayName:"Veter WC Hsu", chineseName: "許維城", areaId: 'i-Tower_22F_seat_005',picture:{"large":"https://randomuser.me/api/portraits/men/63.jpg",medium:"https://randomuser.me/api/portraits/med/men/63.jpg",thumbnail:"https://randomuser.me/api/portraits/thumb/men/63.jpg"}},
    {id: "MISSI_CHEN", displayName:"Missi Chen", chineseName: "陳慈惠", areaId: 'i-Tower_22F_seat_019',picture:{"large":"https://randomuser.me/api/portraits/women/52.jpg",medium:"https://randomuser.me/api/portraits/med/women/52.jpg",thumbnail:"https://randomuser.me/api/portraits/thumb/women/52.jpg"}},
    {id: "SHEPHERDCW_FAN", displayName:"Shepherd CW Fan", chineseName: "范哲偉", areaId: 'i-Tower_22F_seat_017',picture:{"large":"https://randomuser.me/api/portraits/women/65.jpg",medium:"https://randomuser.me/api/portraits/med/women/65.jpg",thumbnail:"https://randomuser.me/api/portraits/thumb/women/65.jpg"}},
    {id: "REDYS_HUNG", displayName:"Red YS Hung", chineseName: "洪佑昇", areaId: 'i-Tower_22F_seat_020',picture:{"large":"https://randomuser.me/api/portraits/men/40.jpg",medium:"https://randomuser.me/api/portraits/med/men/40.jpg",thumbnail:"https://randomuser.me/api/portraits/thumb/men/40.jpg"}},
    {id: "ANGELAYC_KUO", displayName:"Angela YC Kuo", chineseName: "郭鈺淇", areaId: 'i-Tower_22F_seat_021',picture:{"large":"https://randomuser.me/api/portraits/women/29.jpg",medium:"https://randomuser.me/api/portraits/med/women/29.jpg",thumbnail:"https://randomuser.me/api/portraits/thumb/women/29.jpg"}},
    {id: "BRUCETN_TING", displayName:"Bruce TN Ting", chineseName: "丁寧", areaId: 'i-Tower_22F_seat_014',picture:{"large":"https://randomuser.me/api/portraits/men/68.jpg",medium:"https://randomuser.me/api/portraits/med/men/68.jpg",thumbnail:"https://randomuser.me/api/portraits/thumb/men/68.jpg"}},
    {id: "WILLWT_LIN", displayName:"Will WT Lin", chineseName: "林威廷", areaId: 'i-Tower_22F_seat_015',picture:{"large":"https://randomuser.me/api/portraits/women/43.jpg",medium:"https://randomuser.me/api/portraits/med/women/43.jpg",thumbnail:"https://randomuser.me/api/portraits/thumb/women/43.jpg"}},
    {id: "JIMYS_TSAI", displayName:"Jim YS Tsai", chineseName: "蔡允舜", areaId: 'i-Tower_22F_seat_013',picture:{"large":"https://randomuser.me/api/portraits/men/15.jpg",medium:"https://randomuser.me/api/portraits/med/men/15.jpg",thumbnail:"https://randomuser.me/api/portraits/thumb/men/15.jpg"}},
    {id: "BLAKECS_HUANG", displayName:"Blake CS Huang", chineseName: "黃金松", areaId: 'i-Tower_22F_seat_018',picture:{"large":"https://randomuser.me/api/portraits/women/72.jpg",medium:"https://randomuser.me/api/portraits/med/women/72.jpg",thumbnail:"https://randomuser.me/api/portraits/thumb/women/72.jpg"}},
    {id: "ZANDERCC_CHUANG", displayName:"Zander CC Chuang", chineseName: "莊承展", areaId: 'i-Tower_22F_seat_022',picture:{"large":"https://randomuser.me/api/portraits/women/28.jpg",medium:"https://randomuser.me/api/portraits/med/women/28.jpg",thumbnail:"https://randomuser.me/api/portraits/thumb/women/28.jpg"}},
    {id: "TIMST_LIN", displayName:"Tim ST Lin", chineseName: "林書霆", areaId: 'i-Tower_22F_seat_010',picture:{"large":"https://randomuser.me/api/portraits/men/98.jpg",medium:"https://randomuser.me/api/portraits/med/men/98.jpg",thumbnail:"https://randomuser.me/api/portraits/thumb/men/98.jpg"}},
    {id: "PETERYH_YU", displayName:"Peter YH Yu", chineseName: "余益旭", areaId: 'i-Tower_22F_seat_004',picture:{"large":"https://randomuser.me/api/portraits/men/21.jpg",medium:"https://randomuser.me/api/portraits/med/men/21.jpg",thumbnail:"https://randomuser.me/api/portraits/thumb/men/21.jpg"}},
    {id: "TOM_WANG", displayName:"Tom Wang", chineseName: "汪桂林", areaId: 'i-Tower_22F_seat_180',picture:{"large":"https://randomuser.me/api/portraits/men/77.jpg",medium:"https://randomuser.me/api/portraits/med/men/77.jpg",thumbnail:"https://randomuser.me/api/portraits/thumb/men/77.jpg"}},
    {id: "LIZFH_YEH", displayName:"Liz FH Yeh", chineseName: "葉馥華", areaId: 'i-Tower_22F_seat_177',picture:{"large":"https://randomuser.me/api/portraits/men/34.jpg",medium:"https://randomuser.me/api/portraits/med/men/34.jpg",thumbnail:"https://randomuser.me/api/portraits/thumb/men/34.jpg"}},
    {id: "KEVINPW_YANG", displayName:"Kevin PW Yang", chineseName: "楊博文", areaId: 'i-Tower_22F_seat_179',picture:{"large":"https://randomuser.me/api/portraits/men/87.jpg",medium:"https://randomuser.me/api/portraits/med/men/87.jpg",thumbnail:"https://randomuser.me/api/portraits/thumb/men/87.jpg"}},
    {id: "SINSC_HO", displayName:"Sin SC Ho", chineseName: "何昕宸", areaId: null,picture:{"large":"https://randomuser.me/api/portraits/women/52.jpg",medium:"https://randomuser.me/api/portraits/med/women/52.jpg",thumbnail:"https://randomuser.me/api/portraits/thumb/women/52.jpg"}},
    {id: "STEWARD_CHANG", displayName:"Steward Chang", chineseName: "張千村", areaId: 'i-Tower_22F_seat_090',picture:{"large":"https://randomuser.me/api/portraits/women/86.jpg",medium:"https://randomuser.me/api/portraits/med/women/86.jpg",thumbnail:"https://randomuser.me/api/portraits/thumb/women/86.jpg"}},
    {id: "JELEN_HUANG", displayName:"Jelen Huang", chineseName: "黃健隆", areaId: null,picture:{"large":"https://randomuser.me/api/portraits/women/22.jpg",medium:"https://randomuser.me/api/portraits/med/women/22.jpg",thumbnail:"https://randomuser.me/api/portraits/thumb/women/22.jpg"}},
    {id: "DYLANLY_WUNG", displayName:"Dylan LY Wung", chineseName: "翁立遠", areaId: 'i-Tower_22F_seat_086',picture:{"large":"https://randomuser.me/api/portraits/women/89.jpg",medium:"https://randomuser.me/api/portraits/med/women/89.jpg",thumbnail:"https://randomuser.me/api/portraits/thumb/women/89.jpg"}},
    {id: "ALLENC_YANG", displayName:"Allen C Yang", chineseName: "楊騏", areaId: 'i-Tower_22F_seat_176',picture:{"large":"https://randomuser.me/api/portraits/women/7.jpg",medium:"https://randomuser.me/api/portraits/med/women/7.jpg",thumbnail:"https://randomuser.me/api/portraits/thumb/women/7.jpg"}},
    {id: "MARKMY_HSIEH", displayName:"Mark MY Hsieh", chineseName: "謝孟陽", areaId: 'i-Tower_22F_seat_181',picture:{"large":"https://randomuser.me/api/portraits/women/20.jpg",medium:"https://randomuser.me/api/portraits/med/women/20.jpg",thumbnail:"https://randomuser.me/api/portraits/thumb/women/20.jpg"}},
    {id: "CILENTC_LIN", displayName:"Cilen TC Lin", chineseName: "林澤成", areaId: 'i-Tower_22F_seat_003',picture:{"large":"https://randomuser.me/api/portraits/women/25.jpg",medium:"https://randomuser.me/api/portraits/med/women/25.jpg",thumbnail:"https://randomuser.me/api/portraits/thumb/women/25.jpg"}},
    {id: "KAREN_CHEN", displayName:"Karen Chen", chineseName: "陳之蓓", areaId: 'i-Tower_22F_seat_039',picture:{"large":"https://randomuser.me/api/portraits/men/48.jpg",medium:"https://randomuser.me/api/portraits/med/men/48.jpg",thumbnail:"https://randomuser.me/api/portraits/thumb/men/48.jpg"}},
    {id: "GINAYJ_TSAI", displayName:"Gina YJ Tsai", chineseName: "蔡宜瑾", areaId: 'i-Tower_22F_seat_038',picture:{"large":"https://randomuser.me/api/portraits/women/34.jpg",medium:"https://randomuser.me/api/portraits/med/women/34.jpg",thumbnail:"https://randomuser.me/api/portraits/thumb/women/34.jpg"}},
    {id: "JERRYYT_LAI", displayName:"Jerry YT Lai", chineseName: "賴奕廷", areaId: 'i-Tower_22F_seat_033',picture:{"large":"https://randomuser.me/api/portraits/women/94.jpg",medium:"https://randomuser.me/api/portraits/med/women/94.jpg",thumbnail:"https://randomuser.me/api/portraits/thumb/women/94.jpg"}},
    {id: "JAMESCW_CHANG", displayName:"James CW Chang", chineseName: "張哲維", areaId: 'i-Tower_22F_seat_034',picture:{"large":"https://randomuser.me/api/portraits/women/17.jpg",medium:"https://randomuser.me/api/portraits/med/women/17.jpg",thumbnail:"https://randomuser.me/api/portraits/thumb/women/17.jpg"}},
    {id: "STEVENYC_CHIU", displayName:"Steven YC Chiu", chineseName: "邱乙城", areaId: 'i-Tower_22F_seat_043',picture:{"large":"https://randomuser.me/api/portraits/men/87.jpg",medium:"https://randomuser.me/api/portraits/med/men/87.jpg",thumbnail:"https://randomuser.me/api/portraits/thumb/men/87.jpg"}},
    {id: "TIMCC_WU", displayName:"Tim CC Wu", chineseName: "吳昭慶", areaId: 'i-Tower_22F_seat_036',picture:{"large":"https://randomuser.me/api/portraits/men/96.jpg",medium:"https://randomuser.me/api/portraits/med/men/96.jpg",thumbnail:"https://randomuser.me/api/portraits/thumb/men/96.jpg"}},
    {id: "LILLIANIC_WANG", displayName:"Lillian IC Wang", chineseName: "王依晴", areaId: 'i-Tower_22F_seat_040',picture:{"large":"https://randomuser.me/api/portraits/men/65.jpg",medium:"https://randomuser.me/api/portraits/med/men/65.jpg",thumbnail:"https://randomuser.me/api/portraits/thumb/men/65.jpg"}},
    {id: "ANDREWYC_LEE", displayName:"Andrew YC Lee", chineseName: "李煜中", areaId: 'i-Tower_22F_seat_037',picture:{"large":"https://randomuser.me/api/portraits/men/36.jpg",medium:"https://randomuser.me/api/portraits/med/men/36.jpg",thumbnail:"https://randomuser.me/api/portraits/thumb/men/36.jpg"}},
    {id: "HUGHHC_LIU", displayName:"Hugh HC Liu", chineseName: "劉信晨", areaId: null,picture:{"large":"https://randomuser.me/api/portraits/men/87.jpg",medium:"https://randomuser.me/api/portraits/med/men/87.jpg",thumbnail:"https://randomuser.me/api/portraits/thumb/men/87.jpg"}},
    {id: "DERRICKCY_CHEN", displayName:"Derrick CY Chen", chineseName: "陳俊宇", areaId: 'i-Tower_22F_seat_047',picture:{"large":"https://randomuser.me/api/portraits/women/95.jpg",medium:"https://randomuser.me/api/portraits/med/women/95.jpg",thumbnail:"https://randomuser.me/api/portraits/thumb/women/95.jpg"}},
    {id: "KUEICHINKC_HUANG", displayName:"Kueichin KC Huang", chineseName: "黃奎瑾", areaId: 'i-Tower_22F_seat_046',picture:{"large":"https://randomuser.me/api/portraits/men/89.jpg",medium:"https://randomuser.me/api/portraits/med/men/89.jpg",thumbnail:"https://randomuser.me/api/portraits/thumb/men/89.jpg"}},
    {id: "ANNEAT_HOU", displayName:"Anne AT Hou", chineseName: "侯安庭", areaId: 'i-Tower_22F_seat_045',picture:{"large":"https://randomuser.me/api/portraits/men/66.jpg",medium:"https://randomuser.me/api/portraits/med/men/66.jpg",thumbnail:"https://randomuser.me/api/portraits/thumb/men/66.jpg"}},
    {id: "JENNYYC_LU", displayName:"Jenny YC Lu", chineseName: "呂昀臻", areaId: 'i-Tower_22F_seat_044',picture:{"large":"https://randomuser.me/api/portraits/women/15.jpg",medium:"https://randomuser.me/api/portraits/med/women/15.jpg",thumbnail:"https://randomuser.me/api/portraits/thumb/women/15.jpg"}},
    {id: "HANSCH_CHEN", displayName:"Hans CH Chen", chineseName: "陳軍翰", areaId: 'i-Tower_22F_seat_042',picture:{"large":"https://randomuser.me/api/portraits/men/31.jpg",medium:"https://randomuser.me/api/portraits/med/men/31.jpg",thumbnail:"https://randomuser.me/api/portraits/thumb/men/31.jpg"}},
    {id: "TIFFANYHJ_CHEN", displayName:"Tiffany HJ Chen", chineseName: "陳宣榕", areaId: 'i-Tower_22F_seat_041',picture:{"large":"https://randomuser.me/api/portraits/men/78.jpg",medium:"https://randomuser.me/api/portraits/med/men/78.jpg",thumbnail:"https://randomuser.me/api/portraits/thumb/men/78.jpg"}},
    ]
)

// Area management functions
export function addArea(area) {
  // Check if any employee already has this area assigned
  const employeeWithArea = employees.value.find(e => e.areaId === area.id);
  area.assignedEmployee = employeeWithArea? employeeWithArea.id : null;
  areas.value.push(area)
}

export function updateArea(areaId, updates) {
  const index = areas.value.findIndex(a => a.id === areaId)
  if (index !== -1) {
    areas.value[index] = { ...areas.value[index], ...updates }
  }
}

export function deleteArea(areaId) {
  const index = areas.value.findIndex(a => a.id === areaId)
  if (index !== -1) {
    // Unassign employees from this area
    const area = areas.value[index]
    if (area.assignedEmployee) {
      const employee = employees.value.find(e => e.id === employeeId)
      if (employee) {
        employee.areaId = null
      }
    }
    areas.value.splice(index, 1)
  }
}

// Employee assignment functions
export function assignEmployeeToArea(employeeId, areaId) {
  const employee = employees.value.find(e => e.id === employeeId)
  const area = areas.value.find(a => a.id === areaId)
  
  if (employee && area) {
    // Remove employee from previous area if any
    if (employee.areaId) {
      const oldArea = areas.value.find(a => a.id === employee.areaId)
      if (oldArea) {
        oldArea.assignedEmployee = null
      }
    }
    
    // Assign to new area
    employee.areaId = areaId
    area.assignedEmployee = employeeId
  }
}

export function unassignEmployee(employeeId) {
  const employee = employees.value.find(e => e.id === employeeId)
  if (employee && employee.areaId) {
    const area = areas.value.find(a => a.id === employee.areaId)
    if (area) {
      area.assignedEmployee = null
    }
    employee.areaId = null
  }
}