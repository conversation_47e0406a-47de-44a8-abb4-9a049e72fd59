<script setup>
import { ref, watch, computed, onMounted } from 'vue'
import { useTheme, useDisplay } from 'vuetify'
import { useI18n } from 'vue-i18n'
import { GridLayout, GridItem } from 'vue-grid-layout-v3'
import { users, useUserStore } from '@/stores/userStore'
import ThemeSwitcher from '@/components/ThemeSwitcher.vue'
import LanguageSwitcher from '@/components/LanguageSwitcher.vue'
import { getPhoto } from '@/utils/photo'
import { menus } from '@/stores/menuStore'
import { loadWidget } from '@/widgets/WidgetRegistry'

const drawer = ref(false)

const { mobile, mdAndDown } = useDisplay()
// 為不同屏幕尺寸保存布局
const desktopLayout = ref([])
const mobileLayout = ref([])

const { t, locale } = useI18n()

const userStore = useUserStore()

const theme = useTheme()

const switchUser = async (userId) => {
  await userStore.selectUser(userId)
  if (userStore.currentPreferences) {
    locale.value = userStore.currentPreferences.language || 'en';
    theme.global.name.value = userStore.currentPreferences.theme || theme.global.name.value;
  }
}


onMounted(async () => {
  await userStore.initializeStore()
  // 初始化時從 userStore 獲取User Preferences和布局
  // console.log(userStore.currentPreferences)
  if (userStore.currentPreferences) {
    locale.value = userStore.currentPreferences.language || 'en';
    theme.global.name.value = userStore.currentPreferences.theme || theme.global.name.value;
  }
  if (userStore.activeLayout) {
    desktopLayout.value = JSON.parse(JSON.stringify(userStore.activeLayout))
    // 為移動端創建適配的布局
    mobileLayout.value = userStore.activeLayout.map(widget => ({
      ...widget,
      w: 12, // 在移動端佔滿寬度
      x: 0,  // 從左側開始
      // 保持其他屬性不變
      i: widget.i,
      y: widget.y,
      h: widget.h,
      minW: widget.minW,
      minH: widget.minH,
      static: widget.static
    }))
  }
})

watch(users, () => {
  userStore.initializeUser()
  if (userStore.currentPreferences) {
    locale.value = userStore.currentPreferences.language;
    theme.global.name.value = userStore.currentPreferences.theme || theme.global.name.value;
  }
})

// 監聽顯示模式變化
watch(mdAndDown, (isMobile) => {
  if (isMobile) {
    // 切換到移動端布局前，保存當前桌面布局
    desktopLayout.value = JSON.parse(JSON.stringify(userStore.activeLayout))
    userStore.activeLayout = mobileLayout.value
  } else {
    // 切換到桌面布局前，保存當前移動端布局
    mobileLayout.value = JSON.parse(JSON.stringify(userStore.activeLayout))
    userStore.activeLayout = desktopLayout.value
  }
}, { immediate: true })

// 監聽布局變化，更新對應的布局存儲
watch(() => userStore.activeLayout, (newLayout) => {
  if (mdAndDown.value) {
    mobileLayout.value = JSON.parse(JSON.stringify(newLayout))
  } else {
    desktopLayout.value = JSON.parse(JSON.stringify(newLayout))
  }
}, { deep: true })

</script>

<template>
  <v-app>
    <v-app-bar color="primary" height="60">
      <!-- Desktop Navigation -->
      <template v-if="!mdAndDown">
        <router-link to="/">
          <v-img
            :src="theme.global.current.value.dark ? '/logo_256x140_dark.png' : '/logo_256x140.png'"
            width="140"
            height="40"
            class="ma-2"
            contain
            eager
          ></v-img>
      </router-link>
        <v-menu open-on-hover v-for="menu in menus" :key="menu.titleKey">
          <template v-slot:activator="{ props }">
            <v-btn variant="text" v-bind="props">
              {{ t(menu.titleKey) }}
            </v-btn>
          </template>
          <v-list>
            <v-list-item
              v-for="item in menu.items"
              :key="item.titleKey"
              :href="item.link"
              target="_blank"
            >
              <v-list-item-title>{{ t(item.titleKey) }}</v-list-item-title>
            </v-list-item>
          </v-list>
        </v-menu>
      </template>
      <!-- Mobile Navigation -->
      <template v-else>
        <v-app-bar-nav-icon
          @click.stop="drawer = !drawer"
        ></v-app-bar-nav-icon>
      </template>

      <v-spacer></v-spacer>

      <!-- Action Buttons -->
      <v-btn icon="mdi-chat" to="/chat" />

      <!-- Hide badges on mobile -->
      <template v-if="!mobile">
        <v-badge
          :content="userStore.getTodoTasks.total - userStore.getTodoTasks.completed"
          color="error"
          offset-x="5"
          offset-y="5"
        >
          <v-btn icon="mdi-checkbox-marked-outline">
          </v-btn>
        </v-badge>
        <v-badge
          :content="userStore.benefitPoints.remaining"
          color="success"
          offset-x="5"
          offset-y="5"
        >
          <v-btn icon="mdi-gift">
          </v-btn>
        </v-badge>
      </template>

      <ThemeSwitcher />

      <!-- Language and User menus -->
      <LanguageSwitcher />
      <v-menu location="bottom end">
        <template v-slot:activator="{ props }">
          <v-avatar
            v-bind="props"
            class="mx-2"
            size="32"
            style="cursor: pointer;"
          >
            <v-img
              :src="getPhoto(userStore.currentUser)"
              alt="User"
            />
          </v-avatar>
        </template>

        <v-card>
          <v-card-text>
            <div class="mx-auto text-center">
              <v-avatar size="80" class="mb-4">
                <v-img
                  :src="getPhoto(userStore.currentUser)"
                  alt="User"
                />
              </v-avatar>
              <h3>{{ userStore.currentUser.displayName }}</h3>
              <p class="text-caption mt-1">
                {{ userStore.currentUser.jobTitle }}
              </p>
              <p class="text-caption mt-1">
                {{ userStore.currentUser.department }}
              </p>
              <p class="text-caption mt-1">
                {{ userStore.currentUser.mail }}
              </p>
              <p class="text-caption mt-1">
                {{ userStore.getCurrentSettings.roles.join(', ').toUpperCase() }}
              </p>
              <v-divider class="my-3"></v-divider>
              <v-menu location="start">
                <template v-slot:activator="{ props }">
                  <v-btn
                    prepend-icon="mdi-chevron-right"
                    v-bind="props"
                    variant="text"
                    rounded
                  >
                    {{ t('userProfile.switch')}}
                  </v-btn>
                </template>
                <v-list>
                  <v-list-item
                    v-for="user in users"
                    :key="user.id"
                    @click="switchUser(user.id)"
                  >
                    <template v-slot:prepend>
                      <v-avatar size="24">
                        <v-img
                          :src="getPhoto(user)"
                          alt="User"
                        />
                      </v-avatar>
                    </template>
                    <v-list-item-title>{{ user.profile.displayName }}</v-list-item-title>
                  </v-list-item>
                </v-list>
              </v-menu>
              <v-divider class="my-3"></v-divider>
              <v-btn
                prepend-icon="mdi-restart"
                variant="text"
                rounded
                @click="userStore.reset()"
              >
                {{ t('userProfile.reset')}}
              </v-btn>
            </div>
          </v-card-text>
        </v-card>
      </v-menu>

     </v-app-bar>

      <!-- Add Navigation Drawer for Mobile -->
      <v-navigation-drawer
        v-model="drawer"
        temporary
        v-if="mdAndDown"
      >
        <v-list>
          <v-list-group
            v-for="menu in menus"
            :key="menu.titleKey"
            :value="menu.titleKey"
          >
            <template v-slot:activator="{ props }">
              <v-list-item
                v-bind="props"
                :title="t(menu.titleKey)"
              ></v-list-item>
            </template>

            <v-list-item
              v-for="item in menu.items"
              :key="item.titleKey"
              :href="item.link"
              target="_blank"
              :title="t(item.titleKey)"
            ></v-list-item>
          </v-list-group>
        </v-list>
      </v-navigation-drawer>

    <v-main>
      <router-view v-if="$route.path !== '/dashboard'"></router-view>
      <v-container fluid v-else>
        <GridLayout
          v-if="userStore.activeLayout && userStore.activeLayout.length > 0"
          v-model:layout="userStore.activeLayout"
          :col-num="mdAndDown ? 12 : 12"
          :row-height="30"
          :is-draggable="true"
          :is-resizable="true"
          :vertical-compact="true"
          :use-css-transforms="true"
          :margin="mdAndDown ? [10, 10] : [10, 10]"
          :responsive="true"
        >
          <GridItem
            v-for="widget in userStore.activeLayout"
            :key="widget.i"
            :x="widget.x"
            :y="widget.y"
            :w="widget.w"
            :h="widget.h"
            :i="widget.i"
            :min-w="widget.minW"
            :min-h="widget.minH"
            :static="widget.static"
          >
            <component :is="loadWidget(widget.i)" />
         </GridItem>
        </GridLayout>
        <div v-else-if="userStore.activeLayout && userStore.activeLayout.length === 0 && userStore.currentUser">
          <!-- Optional: Show a message if layout is empty for the current user -->
          No widgets configured for this layout.
        </div>
        <div v-else>
          <!-- Optional: Show a loading indicator while activeLayout is being determined -->
          {{ t('common.loading') }}
        </div>
      </v-container>
    </v-main>
  </v-app>
</template>

<style>
.vue-grid-item.vue-grid-placeholder {
  background: #bbdefb;
}
.vue-grid-item {
  transition: all 200ms ease;
  transition-property: left, top;
}
</style>

<style scoped>
.logo {
  height: 6em;
  padding: 1.5em;
  will-change: filter;
  transition: filter 300ms;
}
.logo:hover {
  filter: drop-shadow(0 0 2em #646cffaa);
}
.logo.vue:hover {
  filter: drop-shadow(0 0 2em #42b883aa);
}
</style>