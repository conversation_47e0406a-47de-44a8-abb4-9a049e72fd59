from enum import Enum
from pydantic import BaseModel
from typing import List, Optional


class Message(BaseModel):
    sender: str
    message: str


class ChatRequest(BaseModel):
    messages: List[Message]


class ChatResponse(BaseModel):
    success: bool
    message: Optional[str] = None
    error: Optional[str] = None
    timestamp: str


class ChatStreamResponse(BaseModel):
    content: str
    timestamp: str


class ToolsEnabledRequest(BaseModel):
    user_id: str
    session_id: str
    messages: List[Message]
    is_tools_enabled: bool


class Chat(BaseModel):
    chat_id: str
    role: str
    content: str
    timestamp: str


class ChatMessage(BaseModel):
    role: str
    content: str


class SessionMessage(BaseModel):
    role: str
    content: str
    timestamp: str


class SessionSummary(BaseModel):
    session_id: str
    title: str
    last_updated: str


class Session(BaseModel):
    title: str
    user_id: str
    last_updated: str
    messages: List[SessionMessage]


# --- Request Models ---
class QueryRequest(BaseModel):
    session_id: str
    query: str
    tools_enabled: bool = False


class CompletionResult(BaseModel):
    id: str
    final_response: str
    tools: Optional[List[str]] = []
    think: Optional[str] = None
    evaluate: Optional[str] = None
    refine: Optional[str] = None
    mermaid_code: Optional[str] = None
    refs: Optional[List[str]] = []
    followups: Optional[List[str]] = []
    feedback: Optional[str] = None
