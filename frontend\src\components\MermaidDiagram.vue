<template>
  <div 
    class="mermaid-diagram cursor-pointer" 
    :data-diagram-code="diagramCode" 
    ref="diagramContainer"
    @click="downloadDiagram"
  ></div>
</template>

<script setup>
import { ref, onMounted, watch } from 'vue'
import mermaid from 'mermaid'

const props = defineProps({
  diagramCode: {
    type: String,
    required: true
  }
})

const diagramContainer = ref(null)

const renderDiagram = async () => {
  if (!diagramContainer.value || !props.diagramCode) return
  
  try {
    // Clear the container
    diagramContainer.value.innerHTML = ''
    // Initialize mermaid
    mermaid.initialize({ startOnLoad: true })
    // Render the diagram
    const { svg } = await mermaid.render('mermaid-diagram', props.diagramCode)
    diagramContainer.value.innerHTML = svg
  } catch (error) {
    console.error('Failed to render mermaid diagram:', error)
    diagramContainer.value.innerHTML = `<pre>${props.diagramCode}</pre>`
  }
}

const downloadDiagram = () => {
  if (!diagramContainer.value) return
  const svgElement = diagramContainer.value.querySelector('svg')
  if (!svgElement) return

  const svgData = new XMLSerializer().serializeToString(svgElement)
  const blob = new Blob([svgData], { type: 'image/svg+xml' })
  const url = URL.createObjectURL(blob)

  const a = document.createElement('a')
  a.href = url
  a.download = 'diagram.svg'
  document.body.appendChild(a)
  a.click()
  document.body.removeChild(a)
  URL.revokeObjectURL(url)
}

onMounted(() => {
  renderDiagram()
})

watch(() => props.diagramCode, () => {
  renderDiagram()
})
</script>

<style scoped>
.mermaid-diagram {
  text-align: center;
}
</style>