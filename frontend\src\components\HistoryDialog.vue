<template>
    <v-dialog v-model="state.showHistoryDialog" max-width="600">
      <v-card>
        <v-card-title class="d-flex align-center text-h5">
          <v-icon icon="mdi-history" class="mr-2" color="primary"></v-icon>
          {{ t('chatbot.chatHistory') }}
          <v-spacer></v-spacer>
          <v-btn
            icon="mdi-trash-can"
            color="error"
            class="mr-2"
            @click="clearHistory"
            :title="t('chatbot.clearHistory')"
            size="small"
          ></v-btn>
          <v-btn icon="mdi-close" @click="state.showHistoryDialog = false" size="small"></v-btn>
        </v-card-title>
        <v-card-text>
          <v-list>
            <v-list-item
              v-for="conversation in state.conversations"
              :key="conversation.session_id"
              :active="conversation.session_id === state.currentConversationId"
              @click="loadConversation(conversation.session_id)"
              >
              <v-list-item-title>
                {{ formatDatetime(conversation.created_at) }}
              </v-list-item-title>
              <v-list-item-subtitle>
                {{ conversation.title || 
                  conversation.messages.find(msg => msg.sender === 'user')?.message || t('chatbot.newConversation') }}
              </v-list-item-subtitle>
              <template v-slot:append>
                <v-btn
                  color="error"
                  icon="mdi-delete-outline"
                  variant="tonal"
                  @click.stop="deleteConversation(conversation.session_id)"
                  :title="t('chatbot.deleteConversation')"
                  size="small"
                ></v-btn>
              </template>
            </v-list-item>
          </v-list>
        </v-card-text>
      </v-card>
    </v-dialog>
</template>
<script setup lang="ts">

defineProps({
  state: {
    type: Object,
    required: true
  },
  t: {
    type: Function,
    required: true
  },
  clearHistory: {
    type: Function,
    required: true
  },
  loadConversation: {
    type: Function,
    required: true
  },
  formatDatetime: {
    type: Function,
    required: true
  },
  deleteConversation: {
    type: Function,
    required: true
  }
});
</script>