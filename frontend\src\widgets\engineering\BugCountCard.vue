<script setup>
import { ref, onMounted, computed } from 'vue'
import { useI18n } from 'vue-i18n'

const { t } = useI18n()

const bugStats = ref({
  totalBugs: 0,
  assignedBugs: 0
})

const bugRatio = computed(() => {
  if (bugStats.value.totalBugs === 0) return 0
  return Math.round((bugStats.value.assignedBugs / bugStats.value.totalBugs) * 100)
})

// Mock function - replace with actual API calls
async function fetchBugStats() {
  // TODO: Implement actual API integration with Jira/GitLab
  bugStats.value = {
    totalBugs: 15,
    assignedBugs: 12
  }
}

onMounted(() => {
  fetchBugStats()
})
</script>

<template>
  <v-card height="100%" class="bug-count-card">
    <v-card-title class="d-flex align-center">
      <v-icon icon="mdi-bug" color="primary-variant" class="mr-2"></v-icon>
      {{ t('bugCount.title') }}
      <v-spacer></v-spacer>
      <v-btn
        color="primary"
        size="small"
        prepend-icon="mdi-refresh"
        @click="fetchBugStats"
      >
        {{ t('common.refresh') }}
      </v-btn>
    </v-card-title>
    <v-card-text>
      <v-row>
        <v-col cols="12" sm="4">
          <v-card variant="outlined" class="pa-2">
            <div class="text-subtitle-2 text-medium-emphasis">{{ t('bugCount.totalBugs') }}</div>
            <div class="text-h4 font-weight-bold">{{ bugStats.totalBugs }}</div>
          </v-card>
        </v-col>
        <v-col cols="12" sm="4">
          <v-card variant="outlined" class="pa-2">
            <div class="text-subtitle-2 text-medium-emphasis">{{ t('bugCount.assignedBugs') }}</div>
            <div class="text-h4 font-weight-bold">{{ bugStats.assignedBugs }}</div>
          </v-card>
        </v-col>
        <v-col cols="12" sm="4">
          <v-card variant="outlined" class="pa-2">
            <div class="text-subtitle-2 text-medium-emphasis">{{ t('bugCount.assignedRatio') }}</div>
            <div class="text-h4 font-weight-bold">{{ bugRatio }}%</div>
          </v-card>
        </v-col>
      </v-row>
      <v-row class="mt-4">
        <v-col cols="12">
          <v-progress-linear
            :model-value="bugRatio"
            color="error"
            height="20"
          >
            <template v-slot:default="{ value }">
              <strong>{{ Math.ceil(value) }}%</strong>
            </template>
          </v-progress-linear>
        </v-col>
      </v-row>
    </v-card-text>
  </v-card>
</template>

<style scoped>
.bug-count-card {
  overflow: auto;
}
</style>