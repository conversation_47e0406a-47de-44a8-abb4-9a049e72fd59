import os

# Support Docker environment variable configuration
MCP_HTTP_URL = os.getenv("MCP_HTTP_URL")

MCP_SERVERS = {
    "mcp-atlassian": {
        "type": "stdio",
        "description": "Query or update Atlassian Confluence pages and Jira issues.",
        "command": "mcp-atlassian",
        "args": [
            f"--confluence-url={os.getenv('CONFLUENCE_URL')}",
            f"--confluence-username={os.getenv('CONFLUENCE_USERNAME')}",
            f"--confluence-personal-token={os.getenv('CONFLUENCE_PERSONAL_TOKEN')}",
            f"--jira-url={os.getenv('JIRA_URL')}",
            f"--jira-username={os.getenv('JIRA_USERNAME')}",
            f"--jira-personal-token={os.getenv('JIRA_PERSONAL_TOKEN')}",
        ],
        "is_active": True,
    },
    "mcp-it-helpdesk": {
        "type": "stdio",
        "description": "Query or create IT HelpDesk tickets.",
        "command": "fastmcp",
        "args": ["run", "D:/source/mcp/mcp-server/main.py"],
        "is_active": True,
    },
    "mcp-weather": {
        "type": "streamableHttp",
        "description": "Query Taiwan city weather forecast.",
        "url": "http://localhost:3000",
        "headers": {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {os.getenv('CWA_API_KEY')}",
        },
        "timeout": 60,
        "is_active": False,
    },
    "mcp-taiwan-weather": {
        "type": "stdio",
        "description": "Query Taiwan city weather forecast.",
        "command": "npx",
        "args": ["-y", "@gonetone/mcp-server-taiwan-weather"],
        "env": {f"'CWA_API_KEY': '{os.getenv('CWA_API_KEY')}'"},
        "is_active": False,
    },
    "BPM-HELPER": {
        "type": "http",
        "description": "For BPM-related queries, you can help with:\n- Explaining how to use specific BPM features\n- Recommending appropriate forms for different scenarios\n- Troubleshooting common BPM issues\n- Querying user's current running forms and their status\n\n",
        "url": MCP_HTTP_URL,
        "timeout": 30.0,
        "retry_count": 2,
        "is_active": True,
    },
}
