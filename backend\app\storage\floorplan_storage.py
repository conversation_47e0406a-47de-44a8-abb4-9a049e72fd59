# backend/app/storage/floorplan_storage.py

# Each building has a unique identifier (building:{id}) and contains metadata:
# `building:{id}` -> {
#     "id": "MHQ-Taipei-Office",
#     "name": "MHQ Taipei Office",
#     "country": "Taiwan",
#     "city": "Taipei",
#     "address": "123 Main Street",
#     "location": {                 # for Google Maps
#         "lat": 25.033611,
#         "lng": 121.565000
#       },
#     "image_url": '',
#     "created_at": "2023-10-01T00:00:00Z",
# }
#
# Each floor should be indexed separately:
# `building:{id}:floors` -> Set of all floor IDs
# floors = [
#     { "building_id": "MHQ-Taipei-Office", "floor_id": ["12F", "13F", "14F"]} }
# ]
#
# Each area (seat, meeting room, lavatory, etc.) should be indexed separately:
# `building:{id}:{floor_id}:areas` -> Set of all area IDs
#
# `area:{id}` -> {
#     "id": "CR-A",
#     "floor_id": "1",
#     "building_id": "MHQ-Taipei-Office",
#     "name": "Conference Room A",
#     "type": "meeting_room",
#     "floorplan_id": "{floorplan_id}",
#     "employee_upn": "<EMAIL>"  # If assigned and type is `seat` else None
# }
#
# `seat:assigned:{employee_upn}` -> {
#     "building_id": "{building_id}",
#     "floor_id": "{floor_id}",
#     "area_id": "{area_id}",
# }
#
# `building:{id}:{floor_id}:departments` -> Set of all department IDs on a floor
#
# `abbr:departments` -> [] # Abbreviation of all departments
#
# `abbr:{department_id}` -> {
# 	"ADM": {
# 		"us": "Administration Mangement",
# 		"zh-TW": "行政管理部",
# 		"zh-CN": "行政管理部",
# 	},
# 	"ESG": {
# 		"us": "Environment Saftey and Sustainability",
# 		"zh-TW": "永續發展部",
# 		"zh-CN": "永續發展部",
# 	}    
# }
#
# Querying Recommendations
# Retrieve all buildings: Use building:* (KEYS)
# Retrieve all floors for a building: Use building:{id}:floors (SET)
# Retrieve all areas for a floor in a building: Use building:{id}:{floor_id}:areas (SET)
# Get details for a specific area: Lookup area:{id}
# Find seat assigned to a user: seat:assigned:{employee_upn}

import json
from datetime import datetime, timezone
from typing import List
import os
import redis
from app.utils.logger import ColoredLogger

logger = ColoredLogger("app.storage.floorplan_storage")

CACHE_TTL = 3600*6  # Cache for 6 hours, TODO: add ex=CACHE_TTL

class FloorplanStorage:
    def __init__(self):
        try:
            self.redis = redis.Redis.from_url(os.getenv("REDIS_URL", "redis://localhost:6379"))
            self.redis.ping()
            logger.info("Initialized Redis connection")
        except Exception as e:
            logger.error(f"Failed to initialize Redis connection: {str(e)}")
            raise e

    def set_all_buildings(self, buildings: List[str]) -> int:
        key = f"building:all"
        # Use pipeline for atomic operations
        pipe = self.redis.pipeline()
        pipe.delete(key)
        # Convert list to set to remove duplicates
        unique_buildings = set(buildings)
        if unique_buildings:
            pipe.sadd(key, *unique_buildings)
        # Execute pipeline and get result from sadd operation
        results = pipe.execute()
        count = results[-1] if len(results) > 1 else 0
        logger.info(f"Updated all known buildings: {count}")
        return count

    def get_all_buildings(self) -> List[dict]:
        buildings = []
        for building in self.redis.smembers("building:all"):
            building_data = self.get_building(building.decode('utf-8'))
            if building_data:
                building_id=building.decode('utf-8')
                new_floors = []
                floors = self.get_floors(building_id=building_id)
                for floor in floors:
                    new_departments = []
                    departments = self.get_building_departments(building_id=building_id, floor_id=floor)
                    for department in departments:
                        department_data = self.get_department_data(department)
                        if department_data:
                            new_departments.append({
                                "id": department,
                                "name": department_data,
                            })
                    new_departments.sort(key=lambda x: x["id"])
                    new_floor = {
                        "id": floor,
                        "departments": new_departments,
                    }
                    new_floors.append(new_floor)
                new_floors.sort(key=lambda x: x["id"])
                building_data["floors"] = new_floors  # Add floors to building_data
                buildings.append(building_data)
        return buildings

    def set_building(self, building_id: str, building_data: dict) -> bool:
        building_key = f"building:{building_id}"
        building_data["created_at"] = datetime.now(timezone.utc).isoformat()
        self.redis.set(building_key, json.dumps(building_data))
        return True
        
    def get_building(self, building_id: str) -> dict:
        building_key = f"building:{building_id}"
        building_data = self.redis.get(building_key)
        if building_data:
            return json.loads(building_data)
        return None

    def delete_building(self, building_id: str) -> bool:
        building_key = f"building:{building_id}"
        self.redis.delete(building_key)
        return True

    def set_floors(self, building_id: str, floors: List[str]) -> int:
        building_floors_key = f"building:{building_id}:floors"
        # Use pipeline for atomic operations
        pipe = self.redis.pipeline()
        pipe.delete(building_floors_key)
        # Convert list to set to remove duplicates
        unique_floors = set(floors)
        if unique_floors:
            pipe.sadd(building_floors_key, *unique_floors)
        # Execute pipeline and get result from sadd operation
        results = pipe.execute()
        count = results[-1] if len(results) > 1 else 0
        logger.info(f"Updated {building_id}'s all known floors: {count}")
        return count

    def add_floor(self, building_id: str, floor_id: str) -> bool:
        building_floors_key = f"building:{building_id}:floors"
        self.redis.sadd(building_floors_key, floor_id)
        return True

    def get_floors(self, building_id: str) -> List[str]:
        building_floors_key = f"building:{building_id}:floors"
        return [floor.decode('utf-8') for floor in self.redis.smembers(building_floors_key)]

    def delete_floor(self, building_id: str, floor_id: str) -> bool:
        building_floors_key = f"building:{building_id}:floors"
        self.redis.srem(building_floors_key, floor_id)
        return True

    def set_abbr_departments(self, departments: List[str]) -> int:
        key = f"abbr:departments"
        # Use pipeline for atomic operations
        pipe = self.redis.pipeline()
        pipe.delete(key)
        # Convert list to set to remove duplicates
        unique_departments = set(departments)
        if unique_departments:
            pipe.sadd(key, *unique_departments)
        # Execute pipeline and get result from sadd operation
        results = pipe.execute()
        count = results[-1] if len(results) > 1 else 0
        logger.info(f"Updated all known department abbreviations: {count}")
        return count

    def add_abbr_department(self, department_id: str, department_data: dict) -> bool:
        key = f"abbr:departments"
        self.redis.sadd(key, department_id)

        key = f"abbr:{department_id}"
        self.redis.set(key, json.dumps(department_data))
        return True

    def get_department_data(self, department_id: str) -> dict:
        key = f"abbr:{department_id}"
        department_data = self.redis.get(key)
        if department_data:
            return json.loads(department_data)
        return None

    def get_abbr_departments(self) -> List[dict]:
        key = f"abbr:departments"
        departments = []
        for department_id in self.redis.smembers(key):
            department_key = f"abbr:{department_id.decode('utf-8')}"
            department_data = self.redis.get(department_key)
            if department_data:
                departments.append(json.loads(department_data))
        return departments

    def delete_abbr_department(self, department_id: str) -> bool:
        key = f"abbr:departments"
        self.redis.srem(key, department_id)
        return True

    def delete_all_abbr_departments(self) -> bool:
        key = f"abbr:departments"
        for department_id in self.redis.smembers(key):
            self.delete_abbr_department(department_id.decode('utf-8'))
        logger.info(f"Deleted all department abbreviations")
        return True

    def set_building_departments(self, building_id: str, floor_id: str, departments: List[str]) -> int:
        building_floors_key = f"building:{building_id}:{floor_id}:departments"
        # Use pipeline for atomic operations
        pipe = self.redis.pipeline()
        pipe.delete(building_floors_key)
        # Convert list to set to remove duplicates
        unique_departments = set(departments)
        if unique_departments:
            pipe.sadd(building_floors_key, *unique_departments)
        # Execute pipeline and get result from sadd operation
        results = pipe.execute()
        count = results[-1] if len(results) > 1 else 0
        logger.info(f"Updated {building_id}:{floor_id}'s all known departments: {count}")
        return count

    def add_building_department(self, building_id: str, floor_id: str, department_id: str) -> bool:
        building_floors_key = f"building:{building_id}:{floor_id}:departments"
        self.redis.sadd(building_floors_key, department_id)
        return True

    def get_building_departments(self, building_id: str, floor_id: str) -> List[str]:
        building_floors_key = f"building:{building_id}:{floor_id}:departments"
        return [department.decode('utf-8') for department in self.redis.smembers(building_floors_key)]

    def delete_building_department(self, building_id: str, floor_id: str, department_id: str) -> bool:
        building_floors_key = f"building:{building_id}:{floor_id}:departments"
        self.redis.srem(building_floors_key, department_id)
        return True

    def set_areas(self, building_id: str, floor_id: str, areas: List[str]) -> int:
        building_areas_key = f"building:{building_id}:{floor_id}:areas"
        # Use pipeline for atomic operations
        pipe = self.redis.pipeline()
        pipe.delete(building_areas_key)
        # Convert list to set to remove duplicates
        unique_areas = set(areas)
        if unique_areas:
            pipe.sadd(building_areas_key, *unique_areas)
        # Execute pipeline and get result from sadd operation
        results = pipe.execute()
        count = results[-1] if len(results) > 1 else 0
        logger.info(f"Updated {building_id}:{floor_id}'s all known areas: {count}")
        return count

    def add_area(self, building_id: str, floor_id: str, area_id: str, area_data: dict) -> bool:
        building_areas_key = f"building:{building_id}:{floor_id}:areas"
        self.redis.sadd(building_areas_key, area_id)

        area_key = f"area:{area_id}"
        area_data["building_id"] = building_id
        area_data["floor_id"] = floor_id
        self.redis.set(area_key, json.dumps(area_data))
        return True

    def get_areas(self, building_id: str, floor_id: str) -> List[dict]:
        building_areas_key = f"building:{building_id}:{floor_id}:areas"
        areas = []
        for area_id in self.redis.smembers(building_areas_key):
            area_key = f"area:{area_id.decode('utf-8')}"
            area_data = self.redis.get(area_key)
            if area_data:
                areas.append(json.loads(area_data))
        return areas

    def delete_all_areas(self) -> bool:
        for building in self.redis.smembers("building:all"):
            building_id = self.get_building(building.decode('utf-8'))
            if building_id:
                building_floors_key = f"building:{building_id}:floors"
                for floor_id in self.redis.smembers(building_floors_key):
                    self.delete_areas(building_id, floor_id.decode('utf-8'))
        logger.info(f"Deleted all areas")
        return True

    def delete_areas(self, building_id: str, floor_id: str) -> bool:
        building_areas_key = f"building:{building_id}:{floor_id}:areas"
        
        # Get all area IDs associated with the building and floor
        area_ids = self.redis.smembers(building_areas_key)
        
        # Delete each area's data
        for area_id in area_ids:
            area_key = f"area:{area_id}"
            self.redis.delete(area_key)
        
        # Remove the set tracking areas
        self.redis.delete(building_areas_key)
        
        return True

    def delete_area(self, building_id: str, floor_id: str, area_id: str) -> bool:
        building_areas_key = f"building:{building_id}:{floor_id}:areas"
        area_key = f"area:{area_id}"
        self.redis.srem(building_areas_key, area_id)
        self.redis.delete(area_key)
        return True

    def assign_seat(self, building_id: str, floor_id: str, area_id: str, employee_upn: str) -> bool:
        area_key = f"area:{area_id}"
        area_data = self.redis.get(area_key)
        if area_data:
            area_data = json.loads(area_data)
            area_data["employee_upn"] = employee_upn
            self.redis.set(area_key, json.dumps(area_data))
            seat_assigned_key = f"seat:assigned:{employee_upn}"
            self.redis.set(seat_assigned_key, json.dumps({
                "building_id": building_id,
                "floor_id": floor_id,
                "area_id": area_id
            })) 
            return True
        return False

    def get_seat_assignment(self, employee_upn: str) -> dict:
        seat_assigned_key = f"seat:assigned:{employee_upn}"
        seat_assignment = self.redis.get(seat_assigned_key)
        if seat_assignment:
            return json.loads(seat_assignment)
        return None
    
    def delete_seat_assignments(self) -> bool:
        for employee_upn in self.redis.scan_iter("seat:assigned:*"):
            self.redis.delete(employee_upn)
        logger.info(f"Deleted all seat assignments")
        return True
