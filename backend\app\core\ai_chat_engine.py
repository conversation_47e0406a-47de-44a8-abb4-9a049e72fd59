from app.core.llm_model_manager import LLMModelManager
from app.utils.logger import ColoredLogger

logger = ColoredLogger("app.core.ai_chat_engine")


class ChatEngine:
    """
    Handles chat completions and dynamically selects LLM providers & models.
    """

    def __init__(self):
        logger.info("ChatEngine initialized with dynamic provider selection.")

    async def create_completion(self, messages, provider_name, model_name, **kwargs):
        """Dynamically selects provider and model based on user input."""
        
        # Validate provider
        provider_class = LLMModelManager.get_provider_class(provider_name)
        if not provider_class:
            raise ValueError(f"Unsupported provider: {provider_name}")

        # Validate model
        available_models = [model["name"] for model in LLMModelManager.get_models(provider_name)]
        if model_name not in available_models:
            raise ValueError(f"Model {model_name} is not available for provider {provider_name}")

        # Instantiate provider with selected model
        provider = provider_class(model_name)
        logger.info(f"Using {provider_name} with model {model_name}")
        
        return await provider.create_completion(messages, **kwargs)