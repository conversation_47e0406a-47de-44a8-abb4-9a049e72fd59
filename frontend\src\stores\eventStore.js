import { ref } from 'vue'

export function getEventColor(eventType) {
  switch (eventType) {
    case 'primary':
      return '#008787'
    case 'secondary':
      return '#204a88'
    case 'sports':
      return '#fa943e'
    case 'holiday':
      return '#FF8000'
    default:
      return '#FF8000'
  }
}

export const events = ref([
  {
    title: 'RDC Monthly Meeting',
    description: 'RDC monthly team sync-up meeting',
    start: new Date('2025-01-14'),
    end: new Date('2025-01-14'),
    color: getEventColor('primary'), 
    allDay: false,
  },
  {
    title: 'SBG Monthly Meeting',
    description: 'SBG monthly team sync-up meeting',
    start: new Date('2025-01-15'),
    end: new Date('2025-01-15'),
    color: getEventColor('primary'),
    allDay: true, 
  },
  {
    title: '元旦假期',
    description: '元旦假期',
    start: new Date('2025-01-01'),
    end: new Date('2025-01-01'),
    color: getEventColor('holiday'),
    allDay: true, 
  },
  {
    title: '尾牙',
    description: '尾牙',
    start: new Date('2025-01-08'),
    end: new Date('2025-01-08'),
    color: getEventColor('holiday'),
    allDay: true, 
  },
  {
    title: '2025 GPS Goal Setting kick-off',
    description: '2025 GPS Goal Setting kick-off',
    start: new Date('2025-01-02'),
    end: new Date('2025-01-02'),
    color: getEventColor('secondary'),
    allDay: true,  
  },
  {
    title: '2025 Town Hall Meeting with the BOD',
    description: '2025 Town Hall Meeting with the BOD',
    start: new Date('2025-01-08'),
    end: new Date('2025-01-08'),
    color: getEventColor('secondary'),
    allDay: true,  
  },
  {
    title: '農曆春節假期',
    description: '農曆春節假期',
    start: new Date('2025-01-25'),
    end: new Date('2025-02-02'),
    color: getEventColor('holiday'),
    allDay: true, 
  },
  {
    title: 'RDC Monthly Meeting',
    description: 'RDC monthly team sync-up meeting',
    start: new Date('2025-02-11'),
    end: new Date('2025-02-11'),
    color: getEventColor('primary'), 
    allDay: false,
  },
  {
    title: 'SBG Monthly Meeting',
    description: 'SBG monthly team sync-up meeting',
    start: new Date('2025-02-12'),
    end: new Date('2025-02-12'),
    color: getEventColor('primary'),
    allDay: true, 
  },
  {
    title: '特別補休假抵扣補班(1)',
    description: '特別補休假抵扣補班(1)',
    start: new Date('2025-02-08'),
    end: new Date('2025-02-08'),
    color: getEventColor('holiday'),
    allDay: true, 
  },
  {
    title: '二二八和平記念日假期',
    description: '二二八和平記念日假期',
    start: new Date('2025-02-28'),
    end: new Date('2025-02-28'),
    color: getEventColor('holiday'),
    allDay: true, 
  },
  {
    title: 'RDC Monthly Meeting',
    description: 'RDC monthly team sync-up meeting',
    start: new Date('2025-03-11'),
    end: new Date('2025-03-11'),
    color: getEventColor('primary'), 
    allDay: false,
  },
  {
    title: 'SBG Monthly Meeting',
    description: 'SBG monthly team sync-up meeting',
    start: new Date('2025-03-12'),
    end: new Date('2025-03-12'),
    color: getEventColor('primary'),
    allDay: true, 
  },
  {
    title: 'Moxa Cycling Taiwan',
    description: 'Moxa Cycling Taiwan',
    start: new Date('2025-03-15'),
    end: new Date('2025-03-15'),
    color: getEventColor('sports'),
    allDay: true, 
  },
  {
    title: 'IRIS/ISO9001 Audit',
    description: 'IRIS/ISO9001 Audit',
    start: new Date('2025-03-25'),
    end: new Date('2025-03-27'),
    color: getEventColor('secondary'),
    allDay: true,  
  },
  {
    title: 'GPS Goal Setting complete',
    description: 'GPS Goal Setting complete',
    start: new Date('2025-03-31'),
    end: new Date('2025-03-31'),
    color: getEventColor('secondary'),
    allDay: true,  
  },
  {
    title: '兒童節/清明節假期',
    description: '兒童節/清明節假期',
    start: new Date('2025-04-03'),
    end: new Date('2025-04-06'),
    color: getEventColor('holiday'),
    allDay: true,
  },
  {
    title: 'CT鐵人三項',
    description: 'CT鐵人三項',
    start: new Date('2025-04-26'),
    end: new Date('2025-04-27'),
    color: getEventColor('sports'),
    allDay: true, 
  },
  {
    title: 'RDC Monthly Meeting',
    description: 'RDC monthly team sync-up meeting',
    start: new Date('2025-04-09'),
    end: new Date('2025-04-09'),
    color: getEventColor('primary'),
    allDay: true,
  },
  {
    title: 'SBG Monthly Meeting',
    description: 'SBG monthly team sync-up meeting',
    start: new Date('2025-04-15'),
    end: new Date('2025-04-15'),
    color: getEventColor('primary'),
    allDay: true, 
  },
  {
    title: '五一勞動節假期',
    description: '五一勞動節假期',
    start: new Date('2025-05-01'),
    end: new Date('2025-05-01'),
    color: getEventColor('holiday'),
    allDay: true,
  },
  {
    title: '壘球賽',
    description: '壘球賽',
    start: new Date('2025-05-03'),
    end: new Date('2025-05-03'),
    color: getEventColor('sports'),
    allDay: true,
  },
  {
    title: 'CSIP payout',
    description: 'CSIP payout',
    start: new Date('2025-05-05'),
    end: new Date('2025-05-05'),
    color: getEventColor('secondary'),
    allDay: true,
  },
  {
    title: 'RDC Monthly Meeting',
    description: 'RDC monthly team sync-up meeting',
    start: new Date('2025-05-07'),
    end: new Date('2025-05-07'),
    color: getEventColor('primary'),
    allDay: true,
  },
  {
    title: 'SBG Monthly Meeting',
    description: 'SBG monthly team sync-up meeting',
    start: new Date('2025-05-13'),
    end: new Date('2025-05-13'),
    color: getEventColor('primary'),
    allDay: true, 
  },
  {
    title: '股東大會',
    description: '股東大會',
    start: new Date('2025-05-16'),
    end: new Date('2025-05-16'),
    color: getEventColor('secondary'),
    allDay: true, 
  },
  {
    title: 'Moxa Cycling Taiwan',
    description: 'Moxa Cycling Taiwan',
    start: new Date('2025-05-25'),
    end: new Date('2025-06-02'),
    color: getEventColor('sports'),
    allDay: true, 
  },
  {
    title: '端午節假期',
    description: '端午節假期',
    start: new Date('2025-05-30'),
    end: new Date('2025-06-01'),
    color: getEventColor('holiday'),
    allDay: true, 
  },
  {
    title: 'RDC Monthly Meeting',
    description: 'RDC monthly team sync-up meeting',
    start: new Date('2025-06-17'),
    end: new Date('2025-06-17'),
    color: getEventColor('primary'),
    allDay: true,
  },
  {
    title: 'SBG Monthly Meeting',
    description: 'SBG monthly team sync-up meeting',
    start: new Date('2025-06-18'),
    end: new Date('2025-06-18'),
    color: getEventColor('primary'),
    allDay: true, 
  },
  {
    title: '游泳賽',
    description: '游泳賽',
    start: new Date('2025-06-07'),
    end: new Date('2025-06-07'),
    color: getEventColor('sports'),
    allDay: true, 
  },
  {
    title: '壘球賽預備日',
    description: '壘球賽預備日',
    start: new Date('2025-06-14'),
    end: new Date('2025-06-14'),
    color: getEventColor('sports'),
    allDay: true, 
  },
  {
    title: 'Overseas Cycling',
    description: 'Overseas Cycling',
    start: new Date('2025-06-24'),
    end: new Date('2025-06-29'),
    color: getEventColor('sports'),
    allDay: true, 
  },
  {
    title: 'RDC Monthly Meeting',
    description: 'RDC monthly team sync-up meeting',
    start: new Date('2025-07-15'),
    end: new Date('2025-07-15'),
    color: getEventColor('primary'),
    allDay: true,
  },
  {
    title: 'SBG Monthly Meeting',
    description: 'SBG monthly team sync-up meeting',
    start: new Date('2025-07-16'),
    end: new Date('2025-07-16'),
    color: getEventColor('primary'),
    allDay: true, 
  },
  {
    title: '2026 ABP Start',
    description: '2026 ABP Start',
    start: new Date('2025-07-01'),
    end: new Date('2025-08-31'),
    color: getEventColor('primary'),
    allDay: true, 
  },
  {
    title: 'Group Mid-year Report meeeting',
    description: 'Group Mid-year Report meeeting',
    start: new Date('2025-07-16'),
    end: new Date('2025-07-16'),
    color: getEventColor('secondary'),
    allDay: true, 
  },
  {
    title: '2025 GPS Mid-year Review',
    description: '2025 GPS Mid-year Review',
    start: new Date('2025-07-31'),
    end: new Date('2025-07-31'),
    color: getEventColor('secondary'),
    allDay: true, 
  },
  {
    title: 'Moxa盃籃球賽',
    description: 'Moxa盃籃球賽',
    start: new Date('2025-07-05'),
    end: new Date('2025-07-05'),
    color: getEventColor('sports'),
    allDay: true, 
  },
  {
    title: 'Moxa盃籃球賽',
    description: 'Moxa盃籃球賽',
    start: new Date('2025-07-12'),
    end: new Date('2025-07-12'),
    color: getEventColor('sports'),
    allDay: true, 
  },
  {
    title: 'Moxa盃籃球賽',
    description: 'Moxa盃籃球賽',
    start: new Date('2025-07-19'),
    end: new Date('2025-07-19'),
    color: getEventColor('sports'),
    allDay: true, 
  },
  {
    title: 'Moxa盃籃球賽',
    description: 'Moxa盃籃球賽',
    start: new Date('2025-07-26'),
    end: new Date('2025-07-26'),
    color: getEventColor('sports'),
    allDay: true, 
  },
  {
    title: 'RDC Monthly Meeting',
    description: 'RDC monthly team sync-up meeting',
    start: new Date('2025-08-12'),
    end: new Date('2025-08-12'),
    color: getEventColor('primary'),
    allDay: true,
  },
  {
    title: 'SBG Monthly Meeting',
    description: 'SBG monthly team sync-up meeting',
    start: new Date('2025-08-13'),
    end: new Date('2025-08-13'),
    color: getEventColor('primary'),
    allDay: true, 
  },
  {
    title: 'ISO 14001 & ISO 45001外部稽核',
    description: 'ISO 14001 & ISO 45001外部稽核',
    start: new Date('2025-08-04'),
    end: new Date('2025-08-06'),
    color: getEventColor('secondary'),
    allDay: true, 
  },
  {
    title: '羽球賽',
    description: '羽球賽',
    start: new Date('2025-08-02'),
    end: new Date('2025-08-02'),
    color: getEventColor('sports'),
    allDay: true, 
  },
    {
    title: 'RDC Monthly Meeting',
    description: 'RDC monthly team sync-up meeting',
    start: new Date('2025-09-16'),
    end: new Date('2025-09-16'),
    color: getEventColor('primary'),
    allDay: true,
  },
  {
    title: 'SBG Monthly Meeting',
    description: 'SBG monthly team sync-up meeting',
    start: new Date('2025-09-10'),
    end: new Date('2025-09-10'),
    color: getEventColor('primary'),
    allDay: true, 
  },
  {
    title: '2026 AOP Start',
    description: '2026 AOP Start',
    start: new Date('2025-09-01'),
    end: new Date('2025-09-30'),
    color: getEventColor('secondary'),
    allDay: true, 
  },
  {
    title: '籃球賽',
    description: '籃球賽',
    start: new Date('2025-09-06'),
    end: new Date('2025-09-06'),
    color: getEventColor('sports'),
    allDay: true, 
  },
  {
    title: '籃球賽',
    description: '籃球賽',
    start: new Date('2025-09-13'),
    end: new Date('2025-09-13'),
    color: getEventColor('sports'),
    allDay: true, 
  },
  {
    title: 'RDC Monthly Meeting',
    description: 'RDC monthly team sync-up meeting',
    start: new Date('2025-10-21'),
    end: new Date('2025-10-21'),
    color: getEventColor('primary'),
    allDay: true,
  },
  {
    title: 'SBG Monthly Meeting',
    description: 'SBG monthly team sync-up meeting',
    start: new Date('2025-10-08'),
    end: new Date('2025-10-08'),
    color: getEventColor('primary'),
    allDay: true, 
  },
  {
    title: 'Global Business Interlock',
    description: 'Global Business Interlock',
    start: new Date('2025-10-13'),
    end: new Date('2025-10-17'),
    color: getEventColor('secondary'),
    allDay: true, 
  },
  {
    title: 'RBUSC in MHQ',
    description: 'RBUSC in MHQ',
    start: new Date('2025-10-20'),
    end: new Date('2025-10-21'),
    color: getEventColor('secondary'),
    allDay: true, 
  },
  {
    title: 'Budget Kickoff',
    description: 'Budget Kickoff',
    start: new Date('2025-10-30'),
    end: new Date('2025-10-31'),
    color: getEventColor('secondary'),
    allDay: true, 
  },
  {
    title: '運動會暨家庭日',
    description: '運動會暨家庭日',
    start: new Date('2025-10-18'),
    end: new Date('2025-10-18'),
    color: getEventColor('sports'),
    allDay: true, 
  },
  {
    title: '運動會暨家庭日(預備日)',
    description: '運動會暨家庭日(預備日)',
    start: new Date('2025-10-25'),
    end: new Date('2025-10-25'),
    color: getEventColor('sports'),
    allDay: true, 
  },
  {
    title: 'RDC Monthly Meeting',
    description: 'RDC monthly team sync-up meeting',
    start: new Date('2025-11-11'),
    end: new Date('2025-11-11'),
    color: getEventColor('primary'),
    allDay: true,
  },
  {
    title: 'SBG Monthly Meeting',
    description: 'SBG monthly team sync-up meeting',
    start: new Date('2025-11-19'),
    end: new Date('2025-11-19'),
    color: getEventColor('primary'),
    allDay: true, 
  },
  {
    title: '2025 GPS Year-end Evaluation kick-off',
    description: '2025 GPS Year-end Evaluation kick-off',
    start: new Date('2025-11-17'),
    end: new Date('2025-11-17'),
    color: getEventColor('secondary'),
    allDay: true, 
  },
  {
    title: 'Moxa Run',
    description: 'Moxa Run',
    start: new Date('2025-11-22'),
    end: new Date('2025-11-22'),
    color: getEventColor('sports'),
    allDay: true, 
  },
  {
    title: 'RDC Monthly Meeting',
    description: 'RDC monthly team sync-up meeting',
    start: new Date('2025-12-16'),
    end: new Date('2025-12-16'),
    color: getEventColor('primary'),
    allDay: true,
  },
  {
    title: 'SBG Monthly Meeting',
    description: 'SBG monthly team sync-up meeting',
    start: new Date('2025-12-17'),
    end: new Date('2025-12-17'),
    color: getEventColor('primary'),
    allDay: true, 
  },
  {
    title: 'RD Festival',
    description: 'RD Festival',
    start: new Date('2025-12-03'),
    end: new Date('2025-12-03'),
    color: getEventColor('secondary'),
    allDay: true, 
  },
  {
    title: '2025 GPS Year-end Evaluation complete',
    description: '2025 GPS Year-end Evaluation complete',
    start: new Date('2025-12-31'),
    end: new Date('2025-12-31'),
    color: getEventColor('secondary'),
    allDay: true, 
  },
  {
    title: 'Moxa盃羽球賽',
    description: 'Moxa盃羽球賽',
    start: new Date('2025-12-06'),
    end: new Date('2025-12-06'),
    color: getEventColor('sports'),
    allDay: true, 
  },
])