from app.core.openai_llm_provider import OpenAIL<PERSON><PERSON><PERSON>ider
from app.core.google_llm_provider import GoogleLLMProvider
from app.core.deepseek_llm_provider import DeepSeek<PERSON><PERSON><PERSON>ider
from app.core.aliyun_llm_provider import <PERSON><PERSON><PERSON><PERSON>rovider

class LLMModelManager:
    """Centralized management of providers, models, and their corresponding classes."""

    providers = {
        # "OpenAI": {
        #     "class": OpenAILLMProvider,
        #     "models": [
        #         {"name": "gpt-4.1-mini", "description": "Balanced for intelligence, speed, and cost.", "release_date": "2025-04"},
        #         {"name": "gpt-4.1-nano", "description": "Fastest, most cost-effective GPT-4.1 model.", "release_date": "2024-06"},
        #         {"name": "gpt-4o-mini", "description": "Fast, affordable small model for focused tasks.", "release_date": "2024-07"},
        #     ],
        # },
        "Google": {
            "class": Google<PERSON><PERSON><PERSON>ider,
            "models": [
                {"name": "gemini-2.0-flash", "description": "Fast inference model.", "release_date": "2024-03"},
                {"name": "gemini-2.0-pro", "description": "High-performance model for complex reasoning.", "release_date": "2024-01"},
            ],
        },
        "DeepSeek": {
            "class": DeepSeekLLMProvider,
            "models": [
                {"name": "deepseek-chat", "description": "Fast chat model.", "release_date": "2025-03"},
                {"name": "deepseek-reasoner", "description": "Advanced logical inference model.", "release_date": "2025-05"},
            ],
        },
        "Aliyun": {
            "class": AliyunLLMProvider,
            "models": [
                {"name": "qwen-plus-latest", "description": "Latest advanced version.", "release_date": "2024-05"},
                {"name": "qwen-turbo-latest", "description": "Optimized turbo version.", "release_date": "2024-03"},
                {"name": "qwen-max-latest", "description": "Maximized reasoning power.", "release_date": "2024-01"},
                {"name": "qwen-long-latest", "description": "Extended context model.", "release_date": "2024-06"},
            ],
        },
    }

    @classmethod
    def get_providers(cls):
        """Returns a list of available providers."""
        return list(cls.providers.keys())

    @classmethod
    def get_models(cls, provider_name):
        """Returns models available for a given provider."""
        return cls.providers.get(provider_name, {}).get("models", [])

    @classmethod
    def get_provider_class(cls, provider_name):
        """Returns the corresponding class for a given provider."""
        return cls.providers.get(provider_name, {}).get("class", None)
    
    @classmethod
    def get_providers_and_models(cls):
        """Returns all the available providers and their models."""
        return {
            provider: {
                "models": sorted(details["models"], key=lambda x: x["name"])  # Alphabetically sorted
            }
            for provider, details in cls.providers.items()
        }