from fastapi import APIRouter, Depends
from app.schemas.widget import WidgetLayoutSaveRequest
from app.services.widget_service import WidgetService
from app.dependencies import get_current_user

router = APIRouter()


@router.post("/layout/save")
async def save_widget_layout(
    request: WidgetLayoutSaveRequest,
    current_user=Depends(get_current_user),
):
    await WidgetService.save_layout(user_id=current_user.id, layout=request.layout)
    return {"message": "Layout saved successfully"}
