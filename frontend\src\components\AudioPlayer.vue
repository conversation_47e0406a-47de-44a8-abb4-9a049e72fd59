<template>
    <audio ref="audioPlayer" :src="audioSrc" @ended="handleEnded"></audio>
</template>
  
 <script setup>
import { ref, onMounted, onUnmounted } from "vue";
import { eventBus } from "@/eventBus";
  
const props = defineProps({
    src: { type: String, required: true },
});
  
const audioPlayer = ref(null);
const audioSrc = ref(props.src);
const isPlaying = ref(false);
  
const playAudio = () => {
    try {
      if (audioPlayer.value) {
        audioPlayer.value.play();
        isPlaying.value = true;
        eventBus.emit("audio-playing", true);
      }
    } catch (error) {
      console.error("Error playing audio:", error);
      isPlaying.value = false;
      eventBus.emit("audio-playing", false);
    }
};
  
const stopAudio = () => {
    if (audioPlayer.value) {
      audioPlayer.value.pause();
      audioPlayer.value.currentTime = 0;
      isPlaying.value = false;
      eventBus.emit("audio-playing", false);
    }
};
  
const handleEnded = () => {
    isPlaying.value = false;
    eventBus.emit("audio-playing", false);
    console.log("音频播放结束");
};
  
onMounted(() => {
    eventBus.on("play-audio", () => playAudio()); 
    eventBus.on("stop-audio", () => stopAudio()); 
    console.log("Audio component mounted, ready to play audio");
});

onUnmounted(() => {
    eventBus.off("play-audio");
    eventBus.off("stop-audio");
    console.log("Audio component unmounted, event listeners removed");
})
</script>
  