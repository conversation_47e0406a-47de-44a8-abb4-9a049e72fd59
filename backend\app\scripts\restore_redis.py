# Run this:
# python restore_redis.py {...\backups\your_backup_file.json} --flush
import redis
import json
import os
import argparse
from app.utils.logger import ColoredLogger

logger = ColoredLogger("app.scripts")

# Configuration
REDIS_URL = os.getenv("REDIS_URL", "redis://localhost:6379")

def get_redis_connection():
    """Establishes a connection to Redis."""
    try:
        r = redis.Redis.from_url(REDIS_URL)
        r.ping()
        logger.info(f"Successfully connected to Redis at {REDIS_URL}")
        return r
    except redis.exceptions.ConnectionError as e:
        logger.error(f"Could not connect to Redis at {REDIS_URL}: {e}")
        raise

def restore_redis_data(r: redis.Redis, backup_file_path: str, flush_before_restore: bool = False):
    """Restores data from a JSON file to Redis."""
    try:
        with open(backup_file_path, 'r') as f:
            data_to_restore = json.load(f)
    except FileNotFoundError:
        logger.error(f"Backup file not found: {backup_file_path}")
        raise
    except json.JSONDecodeError as e:
        logger.error(f"Error decoding JSON from backup file {backup_file_path}: {e}")
        raise
    except IOError as e:
        logger.error(f"Error reading backup file {backup_file_path}: {e}")
        raise

    if flush_before_restore:
        logger.info("Flushing all data from Redis before restore...")
        r.flushdb()
        logger.info("Redis flushed.")

    total_keys = len(data_to_restore)
    logger.info(f"Found {total_keys} keys to restore from {backup_file_path}.")
    restored_count = 0

    pipe = r.pipeline()

    for i, (key, item) in enumerate(data_to_restore.items()):
        key_type = item['type']
        value = item['value']
        ttl = item.get('ttl') # ttl might be None if not set

        # Delete key first to ensure clean restore, especially for complex types
        pipe.delete(key)

        if key_type == 'string':
            pipe.set(key, value)
        elif key_type == 'list':
            if value: # Ensure value is not empty
                pipe.rpush(key, *value)
        elif key_type == 'set':
            if value: # Ensure value is not empty
                pipe.sadd(key, *value)
        elif key_type == 'zset':
            if value: # Ensure value is not empty
                pipe.zadd(key, value) # value is a dict {member: score}
        elif key_type == 'hash':
            if value: # Ensure value is not empty
                pipe.hmset(key, value) # value is a dict
        else:
            logger.warning(f"Unsupported key type '{key_type}' for key '{key}' in backup. Skipping.")
            continue
        
        if ttl is not None and ttl > 0:
            pipe.expire(key, ttl)
        
        restored_count += 1
        if (i + 1) % 100 == 0 or (i + 1) == total_keys:
            pipe.execute() # Execute batch of commands
            logger.info(f"Restored {i + 1}/{total_keys} keys...")
            pipe = r.pipeline() # Start a new pipeline for the next batch
    
    if (i + 1) % 100 != 0 : # Execute any remaining commands
        pipe.execute()

    logger.info(f"Successfully restored {restored_count} keys to Redis from {backup_file_path}.")

def main():
    parser = argparse.ArgumentParser(description="Restore Redis data from a backup file.")
    parser.add_argument("backup_file", help="Path to the Redis backup JSON file.")
    parser.add_argument("--flush", action="store_true", help="Flush all data from Redis before restoring.")
    args = parser.parse_args()

    if not os.path.exists(args.backup_file):
        logger.error(f"Backup file does not exist: {args.backup_file}")
        return

    try:
        r = get_redis_connection()
        restore_redis_data(r, args.backup_file, args.flush)
    except Exception as e:
        logger.error(f"An error occurred during the restore process: {e}")

if __name__ == "__main__":
    main()
