import os
from abc import ABC, abstractmethod
from dotenv import load_dotenv
from tenacity import retry, stop_after_attempt, wait_exponential, retry_if_exception_type

load_dotenv()

class ChatEngineError(Exception):
    """Base exception for ChatEngine errors."""
    pass

class InvalidAPIKeyError(ChatEngineError):
    """Exception raised for invalid API keys."""
    pass

class RateLimitError(ChatEngineError):
    """Exception raised when API rate limit is exceeded."""
    pass

class TimeoutError(ChatEngineError):
    """Base exception for ChatEngine errors."""
    pass

class LLMProvider(ABC):
    """Abstract base class for LLM providers."""
    
    def __init__(self, model):
        self.base_url = os.getenv("AI_API_BASE")
        self.api_key = os.getenv("AI_API_KEY")
        self.model = model

    @retry(
        stop=stop_after_attempt(5),
        wait=wait_exponential(multiplier=1, min=4, max=10),
        retry=retry_if_exception_type(RateLimitError) | retry_if_exception_type(TimeoutError) | retry_if_exception_type(ChatEngineError),
        reraise=True
    )
    @abstractmethod
    async def create_completion(self, messages, **kwargs):
        """Abstract method for chat completion."""
        pass
