# HTTP MCP 服務器 Dockerfile
FROM python:3.12-slim

# 設置工作目錄
WORKDIR /app

# 設置環境變量
ENV PYTHONPATH=/app/src
ENV PYTHONUNBUFFERED=1
ENV PYTHONIOENCODING=utf-8

# 安裝系統依賴
RUN apt-get update && apt-get install -y \
    curl \
    && rm -rf /var/lib/apt/lists/*

# 複製項目文件
COPY . /app/

# 安裝 Python 依賴
RUN pip install --no-cache-dir \
    fastapi \
    uvicorn[standard] \
    aiohttp \
    pydantic \
    requests \
    httpx \
    mcp

# 安裝項目依賴（如果有 requirements.txt）
RUN if [ -f requirements.txt ]; then pip install --no-cache-dir -r requirements.txt; fi

# 創建日誌目錄
RUN mkdir -p /app/logs

# 暴露端口
EXPOSE 8001

# 健康檢查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8001/health || exit 1

# 啟動命令
CMD ["python", "start_http_server.py"]
