{"name": "chat", "version": "0.1.0", "private": true, "dependencies": {"@emotion/react": "^11.13.5", "@emotion/styled": "^11.13.5", "@mui/material": "^5.16.7", "@mui/system": "^6.1.8", "@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "antd": "^5.22.7", "react": "^18.3.1", "react-dom": "^18.3.1", "react-scripts": "5.0.1", "react-toastify": "^11.0.3", "typescript": "^4.9.5", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}