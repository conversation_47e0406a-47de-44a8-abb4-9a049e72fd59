import os
from openai import AsyncOpenAI
from dotenv import load_dotenv
from app.core.llm_provider_base import LLMProvider, ChatEngineError, InvalidAPIKeyError, RateLimitError, TimeoutError
from app.utils.logger import ColoredLogger

logger = ColoredLogger("app.core.google_llm_provider")

load_dotenv()

class GoogleLLMProvider(LLMProvider):
    def __init__(self, model):
        super().__init__(model)
        self.base_url = os.getenv("GOOGLE_API_BASE", "https://generativelanguage.googleapis.com/v1beta/openai")
        self.api_key = os.getenv("GOOGLE_API_KEY", "Your API Key")
        self.client = AsyncOpenAI(base_url=self.base_url, api_key=self.api_key)
    
    async def create_completion(self, messages, tools=None, tool_choice=None, temperature=0.7, stream=False):
        try:
            # 如果 tool_choice 是 "auto"，Gemini 需要将其转换为字符串 "auto"
            if tool_choice == "auto":
                tool_choice = "auto"
            # 如果 tool_choice 是 None 但有 tools，设置为 "auto"
            elif tool_choice is None and tools:
                tool_choice = "auto"
            # 如果没有 tools，完全移除 tool_choice 参数
            elif not tools:
                tool_choice = None
            
            # 确保 tools 格式符合 Gemini 的要求
            if tools:
                logger.info("ChatEngine: Adapting tools format for Gemini API")
                # 确保每个工具的格式正确
                for tool in tools:
                    if "function" in tool and "parameters" in tool["function"]:
                        # 确保 parameters 是有效的 JSON Schema
                        if not tool["function"]["parameters"]:
                            tool["function"]["parameters"] = {"type": "object", "properties": {}}
            
            # 构建请求参数，只包含非 None 的参数
            params = {
                "model": self.model,
                "messages": messages,
                "temperature": temperature,
                "stream": stream,
            }
            
            if tools:
                params["tools"] = tools
            
            if tool_choice is not None:
                params["tool_choice"] = tool_choice
                        
            return await self.client.chat.completions.create(**params)
        except InvalidAPIKeyError:
            logger.error("Invalid API key provided.")
            raise InvalidAPIKeyError("Invalid API key provided.")
        except TimeoutError:
            logger.error("Request timed out.")
            raise TimeoutError("Request timed out.")
        except RateLimitError:
            logger.error("Rate limit exceeded. Please check your plan and billing details.")
            raise RateLimitError("Rate limit exceeded. Please check your plan and billing details.")
        except Exception as e:
            logger.error(f"An unexpected error, {str(e)}.")
            raise ChatEngineError(f"An unexpected error occurred: {str(e)}")