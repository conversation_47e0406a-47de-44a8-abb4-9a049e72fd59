<template>
    <v-menu>
        <template v-slot:activator="{ props }">
            <v-btn variant="text" v-bind="props">
            {{ !mdAndDown ? languages.find(lang => lang.code === locale)?.name : languages.find(lang => lang.code === locale)?.abbr }}
            </v-btn>
        </template>
        <v-list>
            <v-list-item
            v-for="lang in languages"
            :key="lang.code"
            @click="changeLanguage(lang.code)"
            >
            <v-list-item-title>{{ lang.name }}</v-list-item-title>
            </v-list-item>
        </v-list>
    </v-menu>
</template>
<script setup>
import { useI18n } from 'vue-i18n'
import { useUserStore } from '@/stores/userStore'
import { useDisplay } from 'vuetify'

const { mdAndDown } = useDisplay()

const { locale } = useI18n()
const userStore = useUserStore()

const languages = [
  { code: 'en', name: 'English', abbr: 'EN' },
  { code: 'zh-TW', name: '繁體中文', abbr: 'TW' },
  { code: 'zh-CN', name: '简体中文', abbr: 'CN' }
]

// 更新语言处理器
const changeLanguage = (lang) => {
  // 切换语言
  if (lang === userStore.currentPreferences.language) return;
  locale.value = lang;
  userStore.updateLanguage(lang);
}
</script>