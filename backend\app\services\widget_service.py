from app.models import WidgetLayout
from app.core.database import async_session


class WidgetService:
    @staticmethod
    async def save_layout(user_id: int, layout: list):
        async with async_session() as session:
            # Remove existing layout
            await session.execute(
                delete(WidgetLayout).where(WidgetLayout.user_id == user_id)
            )

            # Insert new layout
            for widget in layout:
                new_entry = WidgetLayout(
                    user_id=user_id,
                    widget_id=widget.widget_id,
                    x=widget.x,
                    y=widget.y,
                    w=widget.w,
                    h=widget.h,
                )
                session.add(new_entry)

            await session.commit()
