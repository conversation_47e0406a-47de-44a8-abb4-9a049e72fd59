<script setup>
import { ref } from 'vue'

const pipelineStages = ref([
  {
    name: 'Contacting',
    count: 45,
    value: 450000,
    color: 'info',
    conversion: 75
  },
  {
    name: 'Qualifying',
    count: 32,
    value: 640000,
    color: 'primary',
    conversion: 60
  },
  {
    name: 'Proposing',
    count: 18,
    value: 360000,
    color: 'warning',
    conversion: 45
  },
  {
    name: 'Closing',
    count: 12,
    value: 240000,
    color: 'secondary',
    conversion: 80
  },
  {
    name: 'Delivering',
    count: 8,
    value: 160000,
    color: 'success',
    conversion: 65
  }
])

const totalValue = ref(pipelineStages.value.reduce((sum, stage) => sum + stage.value, 0))
const totalDeals = ref(pipelineStages.value.reduce((sum, stage) => sum + stage.count, 0))
</script>

<template>
  <v-card class="pipeline-overview-card">
    <v-card-title class="d-flex align-center">
      <v-icon icon="mdi-pipe" color="primary" class="mr-2"></v-icon>
      Pipeline Overview
    </v-card-title>

    <v-card-text>
      <div class="d-flex justify-space-between align-center mb-2">
        <div>
          <div class="text-h5 font-weight-light">
            ${{ totalValue.toLocaleString() }}
          </div>
          <div class="text-subtitle-2 text-medium-emphasis">
            Total Pipeline Value
          </div>
        </div>
        <div class="text-right">
          <div class="text-h5 font-weight-light">
            {{ totalDeals }}
          </div>
          <div class="text-subtitle-2 text-medium-emphasis">
            Total Opportunities
          </div>
        </div>
      </div>

      <v-sheet class="pipeline-stages">
        <v-row
          v-for="stage in pipelineStages"
          :key="stage.name"
          class="mb-1"
          align="center"
        >
          <v-col cols="12" sm="4">
            <div class="text-subtitle-2 font-weight-medium">{{ stage.name }}</div>
            <div class="d-flex align-center mt-1">
              <v-icon :color="stage.color" icon="mdi-circle" size="small" class="mr-2"></v-icon>
              <span class="text-body-2">{{ stage.count }} opportunities</span>
            </div>
          </v-col>
          
          <v-col cols="12" sm="4">
            <div class="text-subtitle-2">${{ stage.value.toLocaleString() }}</div>
          </v-col>
          
          <v-col cols="12" sm="4">
            <v-progress-linear
              :model-value="stage.conversion"
              :color="stage.color"
              height="14"
              rounded
            >
              <template v-slot:default="{ value }">
                <div class="text-caption">{{ Math.ceil(value) }}% conversion</div>
              </template>
            </v-progress-linear>
          </v-col>
        </v-row>
      </v-sheet>
    </v-card-text>
  </v-card>
</template>