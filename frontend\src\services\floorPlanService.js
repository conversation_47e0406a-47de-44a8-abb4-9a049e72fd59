import axios from 'axios'

const API_URL = import.meta.env.VITE_BACKEND_API_URL || 'http://localhost:8000'

const userClient = axios.create({
  baseURL: API_URL,
  headers: {
    'Content-Type': 'application/json'
  }
})


export const getBuildings = async () => {
    try {
      const response = await fetch(`${API_URL}/buildings`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        }
      });
      const data = await response.json();
      return data;
    } catch (error) {
      console.error(`Failed to get buildings:`, error);
      return {
        success: false,
        error: error.message || `Failed to get buildings.`,
        timestamp: new Date().toLocaleTimeString()
      };
    }
}


export const getAreas = async (buildingId, floorId) => {
    try {
      const response = await fetch(`${API_URL}/buildings/${buildingId}/floors/${floorId}/areas`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        }
      });
      const data = await response.json();
      return data;
    } catch (error) {
      console.error(`Failed to get areas for floor ${buildingId}:${floorId}:`, error);
      return {
        success: false,
        error: error.message || `Failed to get areas for floor ${buildingId}:${floorId}`,
        timestamp: new Date().toLocaleTimeString()
      };
    }
  }
  