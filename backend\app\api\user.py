from datetime import datetime, timezone
from re import A
from fastapi import APIRouter, Depends, HTTPException, Request
from functools import lru_cache
import os
from app.schemas.user import UserId
from app.services.user_service import UserService
from app.utils.auth import get_graph_token
from app.utils.logger import ColoredLogger

logger = ColoredLogger("app.api.user")

router = APIRouter()


@lru_cache()
def get_user_service():
    return UserService()


# This will get all active employees from HR system
# POST /employees/sync
@router.post("/employees/sync")
async def sync_employees(
    request: Request, service: UserService = Depends(get_user_service)
):
    x_api_key = os.getenv("X_API_KEY")
    api_key = request.headers.get("x-api-key")
    if not api_key or api_key != x_api_key:
        raise HTTPException(status_code=403, detail="Unauthorized - Invalid API key")
    try:
        response = await service.sync_employees(api_key)
        return {
            "success": True,
            "data": response,
            "timestamp": datetime.now(timezone.utc).isoformat(),
        }
    except Exception as e:
        logger.error("POST /employees/sync: %s", str(e))
        return {
            "success": False,
            "error": str(e),
            "timestamp": datetime.now(timezone.utc).isoformat(),
        }


# This will get all active positions from HR system
# POST /positions/sync
@router.post("/positions/sync")
async def sync_positions(
    request: Request, service: UserService = Depends(get_user_service)
):
    x_api_key = os.getenv("X_API_KEY")
    api_key = request.headers.get("x-api-key")
    if not api_key or api_key != x_api_key:
        raise HTTPException(status_code=403, detail="Unauthorized - Invalid API key")
    try:
        response = await service.sync_positions(api_key)
        return {
            "success": True,
            "data": response,
            "timestamp": datetime.now(timezone.utc).isoformat(),
        }
    except Exception as e:
        logger.error("POST /positions/sync: %s", str(e))
        return {
            "success": False,
            "error": str(e),
            "timestamp": datetime.now(timezone.utc).isoformat(),
        }


# This will get all active departments from HR system
# POST /departments/sync
@router.post("/departments/sync")
async def sync_departments(
    request: Request, service: UserService = Depends(get_user_service)
):
    x_api_key = os.getenv("X_API_KEY")
    api_key = request.headers.get("x-api-key")
    if not api_key or api_key != x_api_key:
        raise HTTPException(status_code=403, detail="Unauthorized - Invalid API key")
    try:
        response = await service.sync_departments(api_key)
        return {
            "success": True,
            "data": response,
            "timestamp": datetime.now(timezone.utc).isoformat(),
        }
    except Exception as e:
        logger.error("POST /departments/sync: %s", str(e))
        return {
            "success": False,
            "error": str(e),
            "timestamp": datetime.now(timezone.utc).isoformat(),
        }


# This will get all user ids from AAD. This will not be used frequently since POST /users/sync will be called.
# POST /oids/sync
@router.post("/oids/sync")
async def sync_oids(
    request: Request,
    token: str = Depends(get_graph_token),
    service: UserService = Depends(get_user_service),
):
    x_api_key = os.getenv("X_API_KEY")
    api_key = request.headers.get("x-api-key")
    if not api_key or api_key != x_api_key:
        raise HTTPException(status_code=403, detail="Unauthorized - Invalid API key")
    try:
        response = await service.sync_oids(token)
        return {
            "success": True,
            "data": response,
            "timestamp": datetime.now(timezone.utc).isoformat(),
        }
    except Exception as e:
        logger.error("POST /oids/sync: %s", str(e))
        return {
            "success": False,
            "error": str(e),
            "timestamp": datetime.now(timezone.utc).isoformat(),
        }


# Before syncing users, we need to sync employees data from HR system first by POST /employees/sync
# This will get and validate all users from AAD against HR system via user principal name
# POST /users/sync
@router.post("/users/sync")
async def sync_users(
    request: Request,
    token: str = Depends(get_graph_token),
    service: UserService = Depends(get_user_service),
):
    x_api_key = os.getenv("X_API_KEY")
    api_key = request.headers.get("x-api-key")
    if not api_key or api_key != x_api_key:
        raise HTTPException(status_code=403, detail="Unauthorized - Invalid API key")
    try:
        response = await service.sync_users_by_upn(token)
        return {
            "success": True,
            "data": response,
            "timestamp": datetime.now(timezone.utc).isoformat(),
        }
    except Exception as e:
        logger.error("POST /users/sync: %s", str(e))
        return {
            "success": False,
            "error": str(e),
            "timestamp": datetime.now(timezone.utc).isoformat(),
        }


# POST /user/profile
# return:
# {
#     "profile": {
#         "@odata.context": "https://graph.microsoft.com/v1.0/$metadata#users/$entity",
#         "businessPhones": [],
#         "displayName": "Jenny YC Lu (呂昀臻)",
#         "givenName": "JennyYC",
#         "jobTitle": "Data Engineer",
#         "mail": "<EMAIL>",
#         "mobilePhone": null,
#         "officeLocation": "Moxa Taipei Office- i-Tower",
#  }
@router.post("/user/profile")
async def get_user_profile(
    request: Request,
    token: str = Depends(get_graph_token),
    service: UserService = Depends(get_user_service),
):
    try:
        oid = request.headers.get("user_id")
        response = await service.get_user_profile(oid, token)
        return {
            "success": True,
            "data": response,
            "timestamp": datetime.now(timezone.utc).isoformat(),
        }
    except Exception as e:
        logger.error("POST /user/profile: %s", str(e))
        return {
            "success": False,
            "error": str(e),
            "timestamp": datetime.now(timezone.utc).isoformat(),
        }


# This will return { 'id': user_id, 'profile': user_profile, 'employee_data': employee_data }.
# GET /users/{user_id}/employee_profile
@router.get(
    "/users/{user_id}/employee_profile"
)  # Changed to GET and added user_id in path
async def get_employee_with_profile(
    user_id: str,  # Get user_id from path
    service: UserService = Depends(get_user_service),
):
    try:
        # No need to get user_id from header anymore, it's a path parameter
        response = service.get_employee_with_profile(user_id)
        if response is None:
            # This could be because the user_id doesn't exist, or only partial data was found.
            # Adjust status_code and detail as per your API's error handling strategy.
            raise HTTPException(
                status_code=404,
                detail=f"Employee or profile data not found for user ID: {user_id}",
            )

        return {
            "success": True,
            "data": response,
            "timestamp": datetime.now(timezone.utc).isoformat(),
        }
    except (
        HTTPException
    ) as http_exc:  # Re-raise HTTPException to keep its status_code and detail
        raise http_exc
    except Exception as e:
        logger.error(f"GET /users/{user_id}/employee_profile: {str(e)}")
        # Generic error for unexpected issues
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/users/{user_id}/photo")
async def get_user_photo(
    user_id: str,  # Get user_id from path
    service: UserService = Depends(get_user_service),
    token: str = Depends(get_graph_token),
):
    try:
        response = await service.get_user_photo_base64(user_id, token)
        if response is None:
            # This could be because the user_id doesn't exist, or only partial data was found.
            # Adjust status_code and detail as per your API's error handling strategy.
            raise HTTPException(
                status_code=404, detail=f"User photo not found for user ID: {user_id}"
            )

        return {
            "success": True,
            "data": response,
            "timestamp": datetime.now(timezone.utc).isoformat(),
        }
    except (
        HTTPException
    ) as http_exc:  # Re-raise HTTPException to keep its status_code and detail
        raise http_exc
    except Exception as e:
        logger.error(f"GET /users/{user_id}/photo: {str(e)}")
        # Generic error for unexpected issues
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/orgchart")
async def get_orgchart(
    request: Request, service: UserService = Depends(get_user_service)
):
    try:
        response = service.get_orgchart()
        return {
            "success": True,
            "data": response,
            "timestamp": datetime.now(timezone.utc).isoformat(),
        }
    except Exception as e:
        logger.error("GET /orgchart: %s", str(e))
        return {
            "success": False,
            "error": str(e),
            "timestamp": datetime.now(timezone.utc).isoformat(),
        }


@router.get("/users/{user_id}/settings")
async def get_user_settings(
    user_id: str,  # Get user_id from path
    service: UserService = Depends(get_user_service),
):
    try:
        # No need to get user_id from header anymore, it's a path parameter
        response = service.get_user_settings(user_id)
        if response is None:
            # This could be because the user_id doesn't exist, or only partial data was found.
            # Adjust status_code and detail as per your API's error handling strategy.
            raise HTTPException(
                status_code=404, detail=f"Settings not found for user ID: {user_id}"
            )

        return {
            "success": True,
            "data": response,
            "timestamp": datetime.now(timezone.utc).isoformat(),
        }
    except (
        HTTPException
    ) as http_exc:  # Re-raise HTTPException to keep its status_code and detail
        raise http_exc
    except Exception as e:
        logger.error(f"GET /users/{user_id}/settings: {str(e)}")
        # Generic error for unexpected issues
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/users/{user_id}/settings")
async def set_user_settings(
    user_id: str,  # Get user_id from path
    request: Request,
    service: UserService = Depends(get_user_service),
):
    try:
        settings = await request.json()
        response = service.set_user_settings(user_id, settings)
        return {
            "success": True,
            "data": response,
            "timestamp": datetime.now(timezone.utc).isoformat(),
        }
    except Exception as e:
        logger.error("POST /users/{user_id}/settings: %s", str(e))
        return {
            "success": False,
            "error": str(e),
            "timestamp": datetime.now(timezone.utc).isoformat(),
        }


@router.get("/users/{user_id}/preferences")
async def get_user_preferences(
    user_id: str,  # Get user_id from path
    service: UserService = Depends(get_user_service),
):
    try:
        # No need to get user_id from header anymore, it's a path parameter
        response = service.get_user_preferences(user_id)
        if response is None:
            # This could be because the user_id doesn't exist, or only partial data was found.
            # Adjust status_code and detail as per your API's error handling strategy.
            raise HTTPException(
                status_code=404, detail=f"Preferences not found for user ID: {user_id}"
            )

        return {
            "success": True,
            "data": response,
            "timestamp": datetime.now(timezone.utc).isoformat(),
        }
    except (
        HTTPException
    ) as http_exc:  # Re-raise HTTPException to keep its status_code and detail
        raise http_exc
    except Exception as e:
        logger.error(f"GET /users/{user_id}/preferences: {str(e)}")
        # Generic error for unexpected issues
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/users/{user_id}/preferences")
async def set_user_preferences(
    user_id: str,  # Get user_id from path
    request: Request,
    service: UserService = Depends(get_user_service),
):
    try:
        preferences = await request.json()
        response = service.set_user_preferences(user_id, preferences)
        return {
            "success": True,
            "data": response,
            "timestamp": datetime.now(timezone.utc).isoformat(),
        }
    except Exception as e:
        logger.error("POST /users/{user_id}/preferences: %s", str(e))
        return {
            "success": False,
            "error": str(e),
            "timestamp": datetime.now(timezone.utc).isoformat(),
        }
