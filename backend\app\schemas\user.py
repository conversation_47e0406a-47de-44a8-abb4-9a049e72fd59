from pydantic import BaseModel


# Pydantic input model
class UserQuery(BaseModel):
    user_principal_name: str


class UserId(BaseModel):
    oid: str


class UserProfile(BaseModel):
    oid: str
    user_principal_name: str
    mail: str
    display_name: str
    given_name: str
    surname: str
    user_type: str
    job_title: str
    department: str
    office_location: str
    country: str
    photo: str
    roles: list[str]
    manager_id: str
    preferred_language: str
    theme: str
