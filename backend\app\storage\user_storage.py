# backend/app/storage/user_storage.py

# | Redis Key                    | Purpose                                    | Notes / Suggestions                                                                              |
# | ---------------------------- | ------------------------------------------ | -----------------------------------------------------------------------------------------------  |
# | `user:all_oids`              | Set of all known user IDs                  | ✅ Good for Celery batch jobs. Consider TTL policy or use SCAN-based iteration.                  |
# | `user:{id}:profile`          | AAD profile data (read-only)               | ✅ Cache for speed; set TTL if you plan to auto-refresh periodically.                            |
# | `user:{id}:settings`         | Admin-configurable roles, floor plan, etc. | ✅ Separate from AAD profile. Consider splitting large substructures if needed.                  |
# | `user:{id}:preferences`      | Theme, language, etc. (user-configurable)  | ✅ Ideal to keep UI prefs lightweight and scoped.                                                |
# | `user:{id}:active_layout_id` | ID of the user's active layout             | ✅ Ideal for quick lookup.                                                                       |
# | `user:{id}:layouts`          | All saved homepage layouts                 | ✅ Optionally index active layout ID for fast lookup.                                            |
# | `user:{id}:to_dos`           | User's to-do list                          | ✅ Simple and effective. Consider ZSET if sorting by due time is needed.                         |
# | `hr:accounts`                | All employees' UPN                         | ✅ Ideal for quick lookup.                                                                       |
# | `aad:upn_to_id:{upn}`        | = oid                                      | ✅ Ideal for quick lookup. e.g. aad:upn_to_id:<EMAIL> = aad-1234                    |
# | `hr:employee:{upn}`          | = employee data                            | ✅ Ideal for quick lookup. e.g. hr:employee:aad-1234 = { "id": "1234", "name": "John Doe", ... } |
# | `hr:position:{id}`           | = position data                            | ✅ Ideal for quick lookup.                                                                       |
# | `hr:department:{id}`         | = department data                          | ✅ Ideal for quick lookup.                                                                       |
# | `sync:hr:last_updated`       | Timestamp of last sync                     | ✅ Useful for periodic sync checks.                                                              |
# | `sync:aad:last_updated`      | Timestamp of last sync                     | ✅ Useful for periodic sync checks.                                                              |
# | `hr:orgchart`                | Set of positions for org chart             | ✅ Useful for quick lookup.                                                                      |

# Get current layout:
# const activeLayoutId = await redis.get(`user:${id}:active_layout_id`)
# const layout = await redis.hget(`user:${id}:layouts`, activeLayoutId)

# Read full user profile for dashboard:
# const [
#   profile,
#   settings,
#   preferences,
#   layoutId,
#   layouts
# ] = await redis.mget([
#   `user:${id}:profile`,
#   `user:${id}:settings`,
#   `user:${id}:preferences`,
#   `user:${id}:active_layout_id`,
#   `user:${id}:layouts`,
# ])

# TTL Strategy
# user:{id}:profile: Add TTL (e.g. 6h) to reflect AAD sync frequency.
# user:{id}:chat_session:{sid}: Optional TTL to auto-prune inactive sessions.
# user:{id}:preferences, settings, layouts: No TTL (persistent config).
# user:{id}:to_dos: Optional TTL if tied to short-lived tasks.

import json
from datetime import datetime, timezone
from typing import List
import os
import redis
from app.utils.logger import ColoredLogger

logger = ColoredLogger("app.storage.user_storage")

CACHE_TTL = 3600*6  # Cache for 6 hours, TODO: add ex=CACHE_TTL

class UserStorage:
    def __init__(self):
        try:
            self.redis = redis.Redis.from_url(
                os.getenv("REDIS_URL", "redis://localhost:6379")
            )
            self.redis.ping()
            logger.info("Initialized Redis connection")
        except Exception as e:
            logger.error(f"Failed to initialize Redis connection: {str(e)}")
            raise e
        self.prefix = "user"
        self.index_prefix = "user_index"

    def _key(self, user_id: str, postfix: str = None) -> str:
        if postfix is None:
            return f"{self.prefix}:{user_id}"
        return f"{self.prefix}:{user_id}:{postfix}"

    def _index_key(self, user_id: str, postfix: str = None) -> str:
        if postfix is None:
            return f"{self.index_prefix}:{user_id}"
        return f"{self.index_prefix}:{user_id}:{postfix}"

    def get_employees(self) -> List[str]:
        key = "hr:accounts"
        # Decode bytes to strings and convert to list
        return [employee.decode('utf-8') for employee in self.redis.smembers(key)]

    def update_employees(self, employees: List[str]) -> int:
        key = "hr:accounts"
        # Use pipeline for atomic operations
        pipe = self.redis.pipeline()
        pipe.delete(key)
        # Convert list to set to remove duplicates
        unique_employees = set(employees)
        if unique_employees:
            pipe.sadd(key, *unique_employees)
        # Execute pipeline and get result from sadd operation
        results = pipe.execute()
        count = results[-1] if len(results) > 1 else 0
        logger.info(f"Updated all known employees UPN: {count}")
        return count

    def set_employee_data(self, upn: str, data: dict) -> None:
        key = f"hr:employee:{upn}"
        try:
            json_data = json.dumps(data)
            self.redis.set(key, json_data)
            logger.info(f"Saved data for employee {upn}")
        except Exception as e:
            logger.error(f"Failed to encode employee data for {upn}: {e}")
            raise e
# Employee data example:
# {
#   "id": "********",
#   "effectiveDate": "2024-01-01",
#   "lastUpdateDate": "2025-05-24",
#   "name": "XXXXXXXXXXXXXX",
#   "activeDirectoryAccount": "XXXXX_XXXX",
#   "expirationDate": null,
#   "employeeStatus": "A",
#   "positionId": "RD000035",
#   "costCenterNumber": "106052",
#   "hireDate": "2008-08-18",
#   "countryCode": "886",
#   "phoneNumber": "02-********",
#   "extension": "",
#   "locationId": "MHQ001",
#   "locationName": "TPE Office"
#   "allPositions": [              # All positions of the employee, only updated after sync_positions
#     "RD000035",
#     "RD000025"
#   ],
# }
    def get_employee_data(self, upn: str) -> dict:
        key = f"hr:employee:{upn}"
        data = self.redis.get(key)
        if data:
            try:
                return json.loads(data)
            except Exception as e:
                logger.error(f"Failed to parse employee data JSON for {upn}: {e}")
                return None
        return None

    def get_employee_profile(self, upn: str) -> dict | None:
        id = self.get_aad_id_by_upn(upn)
        if id:
            profile = self.get_user_profile(id)
            return profile
        return None

    def get_employee_photo(self, upn: str) -> str:
        id = self.get_aad_id_by_upn(upn)
        if id:
            profile = self.get_user_profile(id)
            if profile:
                photo = profile.get('photo')
                if photo:
                    return f"data:image/jpeg;base64,${photo}"
        return ''

    def is_employee(self, upn: str) -> bool:
        key = "hr:accounts"
        return self.redis.sismember(key, upn)

    def get_hr_accounts(self) -> List[str]:
        key = "hr:accounts"
        # Decode bytes to strings and convert to list
        return [account.decode('utf-8') for account in self.redis.smembers(key)]

    def set_position_data(self, id: str, data: dict) -> None:
        key = f"hr:position:{id}"
        try:
            json_data = json.dumps(data)
            self.redis.set(key, json_data)
            logger.info(f"Saved data for position {id}")
        except Exception as e:
            logger.error(f"Failed to encode position data for {id}: {e}")
            raise e

# Position data example:
# {
# 	"id": "RD000035",
# 	"name": "Software Development",
# 	"employeeId": "********",
# 	"activeDirectoryAccount": "XXXXXXXX",
# 	"recordNumber": 0,
# 	"effectiveDate": "2024-01-01",
# 	"effectiveSequence": 0,
# 	"departmentId": "100R202000",
# 	"officerCode": "1",
# 	"levelId": "M0002",
# 	"indicator": "P",
# 	"reportToPositionId": "RD000025",
# 	"email": "<EMAIL>",
# 	"ehrImportDateTime": "2025-05-25 21:29:57",
# 	"chineseName": "軟體開發",
# 	"positionStatus": "A",
# 	"jobCode": "RDA",
# 	"employeeClass": "LIL"
# }
    def get_position_data(self, id: str) -> dict:
        key = f"hr:position:{id}"
        data = self.redis.get(key)
        if data:
            try:
                return json.loads(data)
            except Exception as e:
                logger.error(f"Failed to parse position data JSON for {id}: {e}")
                return None
        return None

    def set_department_data(self, id: str, data: dict) -> None:
        key = f"hr:department:{id}"
        try:
            json_data = json.dumps(data)
            self.redis.set(key, json_data)
            logger.info(f"Saved data for department {id}")
        except Exception as e:
            logger.error(f"Failed to encode department data for {id}: {e}")
            raise e

# Department data example:
# {
# 	"setId": "10098",
# 	"id": "100R202000",
# 	"effectiveDate": "2024-01-01",
# 	"effectiveStatus": "A",
# 	"name": "Edge Application Dept.",
# 	"shortName": "R&D Center",
# 	"companyId": "100",
# 	"chineseName": "研發中心邊緣運算應用部",
# 	"chineseShortName": "研發中心",
# 	"nameEffectiveDate": "2024-01-01",
# 	"lastUpdateDate": "2023-12-28",
# 	"managerPositionId": "RD000035",
# 	"businessUnitId": "10098"
# }
    def get_department_data(self, id: str) -> dict:
        key = f"hr:department:{id}"
        data = self.redis.get(key)
        if data:
            try:
                return json.loads(data)
            except Exception as e:
                logger.error(f"Failed to parse department data JSON for {id}: {e}")
                return None
        return None

    def get_all_departments(self) -> List[dict]:
        key = "hr:department:*"
        departments = []
        for department_key in self.redis.scan_iter(key):
            department_data = self.redis.get(department_key)
            if department_data:
                try:
                    departments.append(json.loads(department_data))
                except Exception as e:
                    logger.error(f"Failed to parse department data JSON for {department_key}: {e}")
        return departments

    def get_aad_id_by_upn(self, upn: str) -> str | None:
        key = f"aad:upn_to_id:{upn}"
        user_id = self.redis.get(key)
        if user_id:
            return user_id.decode('utf-8')
        return None

    def set_aad_upn_to_id(self, upn: str, oid: str) -> None:
        key = f"aad:upn_to_id:{upn}"
        self.redis.set(key, oid)
        logger.info(f"Saved AAD UPN to ID mapping for {upn}")

    def remove_aad_upn_to_id(self, upn: str) -> None:
        key = f"aad:upn_to_id:{upn}"
        self.redis.delete(key)
        logger.info(f"Removed AAD UPN to ID mapping for {upn}")

    def get_upn_by_aad_id(self, user_id: str) -> str | None:
        user_profile = self.get_user_profile(user_id)
        if user_profile:
            return user_profile.get('userPrincipalName').lower()
        return None

    def add_oids(self, oids: List[str]) -> int:
        key = self._key('all_oids')
        return self.redis.sadd(key, *oids)  # Use * to unpack list elements

    def remove_oid(self, oid: str) -> int:
        key = self._key('all_oids')
        return self.redis.srem(key, oid)  # Use srem to remove a single item

    def remove_oids(self, oids: List[str]) -> int:
        key = self._key('all_oids')
        return self.redis.srem(key, *oids)  # Use * to unpack list elements

    def get_oids(self) -> List[str]:
        key = self._key('all_oids')
        # Decode bytes to strings and convert to list
        return [oid.decode('utf-8') for oid in self.redis.smembers(key)]

    def set_oids(self, oids: List[str]) -> int:
        key = self._key('all_oids')
        # Use pipeline for atomic operations
        pipe = self.redis.pipeline()
        pipe.delete(key)
        # Convert list to set to remove duplicates
        unique_oids = set(oids)
        if unique_oids:
            pipe.sadd(key, *unique_oids)
        # Execute pipeline and get result from sadd operation
        results = pipe.execute()
        count = results[-1] if len(results) > 1 else 0
        logger.info(f"Updated all known user IDs: {count}")
        return count

    def get_user_settings(self, user_id: str) -> dict:
        key = self._key(user_id,'settings')
        data = self.redis.get(key)
        if data:
            try:
                return json.loads(data)
            except Exception as e:
                logger.error(f"Failed to parse user settings JSON for {user_id}: {e}")
                return None
        return None

    def set_user_settings(self, user_id: str, settings_data: dict) -> None:
        key = self._key(user_id, 'settings')
        try:
            json_data = json.dumps(settings_data)
            self.redis.set(key, json_data)
            logger.info(f"Saved settings for user {user_id}")
        except Exception as e:
            logger.error(f"Failed to encode user settings for {user_id}: {e}")
            raise e

    def remove_user_settings(self, user_id: str) -> None:
        key = self._key(user_id,'settings')
        self.redis.delete(key)
        logger.info(f"Removed settings for user {user_id}")  

    def get_user_preferences(self, user_id: str) -> dict:
        key = self._key(user_id,'preferences')
        data = self.redis.get(key)
        if data:
            try:
                return json.loads(data)
            except Exception as e:
                logger.error(f"Failed to parse user preferences JSON for {user_id}: {e}")
                return None
        return None

    def set_user_preferences(self, user_id: str, preferences_data: dict) -> None:
        key = self._key(user_id, 'preferences')
        try:
            json_data = json.dumps(preferences_data)
            self.redis.set(key, json_data)
            logger.info(f"Saved preferences for user {user_id}")
        except Exception as e:
            logger.error(f"Failed to encode user preferences for {user_id}: {e}")
            raise e

    def remove_user_preferences(self, user_id: str) -> None:
        key = self._key(user_id,'preferences')
        self.redis.delete(key)
        logger.info(f"Removed preferences for user {user_id}")  

    def set_user_profile(self, user_id: str, profile_data: dict) -> None:
        key = self._key(user_id, 'profile')
        try:
            json_data = json.dumps(profile_data)
            self.redis.set(key, json_data)
            logger.info(f"Saved profile for user {user_id}")
        except Exception as e:
            logger.error(f"Failed to encode user profile for {user_id}: {e}")
            raise e

    def remove_user_profile(self, user_id: str) -> None:
        key = self._key(user_id, 'profile')
        self.redis.delete(key)
        logger.info(f"Removed profile for user {user_id}")

# Sample profile data:
# {
#   "@odata.context": "https://graph.microsoft.com/v1.0/$metadata#users/$entity",
#   "businessPhones": [],
#   "displayName": "XXXXXXXXXXXX",
#   "givenName": "XXXXX",
#   "jobTitle": "Sr. SW Engineer",
#   "mail": "<EMAIL>",
#   "mobilePhone": null,
#   "officeLocation": "Moxa Taipei Office",
#   "preferredLanguage": null,
#   "surname": "XXXX",
#   "userPrincipalName": "<EMAIL>",
#   "id": "00009c7e-8f78-4478-9baf-9b036df0b7c4",
#   "companyName": "Moxa Inc.",
#   "department": "Device Mgmt. Solution Dept. Section 1",
#   "countryCode": "TW",
#   "managerId": "5bbb951d-ef72-43bd-befe-d49205458453",
#   "photo": null,
#   "roles": []
# }
    def get_user_profile(self, user_id: str) -> dict:
        key = self._key(user_id, 'profile')
        data = self.redis.get(key)
        if data:
            try:
                return json.loads(data)
            except Exception as e:
                logger.error(f"Failed to parse user profile JSON for {user_id}: {e}")
                return None
        return None

    def get_user_profile_by_upn(self, upn: str) -> dict:
        user_id = self.get_aad_id_by_upn(upn)
        if not user_id:
            return None
        key = self._key(user_id, 'profile')
        data = self.redis.get(key)
        if data:
            try:
                return json.loads(data)
            except Exception as e:
                logger.error(f"Failed to parse user profile JSON for {user_id}: {e}")
                return None
        return None

    def get_employee_with_profile(self, user_id: str) -> dict | None:
        user_profile = self.get_user_profile(user_id)

        if not user_profile:
            logger.warning(f"No user profile found for user_id {user_id}")
            return None # Or return {'profile': None, 'employee_data': None} if you prefer a dict even on total failure

        user_settings = self.get_user_settings(user_id)
        user_preferences = self.get_user_preferences(user_id)

        # Initialize with profile and settings, employee_data will be None initially
        combined_data = {
            'id': user_id,
            'profile': user_profile,
            'employee_data': None,
            'settings': user_settings,
            'preferences': user_preferences
        }

        upn = user_profile.get("userPrincipalName") # Assuming UPN is in profile

        if not upn:
            logger.warning(f"No UPN found in profile for user_id {user_id}. Returning profile only.")
            return combined_data # Returns {'id': user_id, 'profile': user_profile, 'employee_data': None}

        employee_data = self.get_employee_data(upn.lower()) # Ensure UPN is lowercase
        
        if employee_data:
            # If you want to merge employee_data at the top level:
            # temp_combined = employee_data.copy()
            # temp_combined['profile'] = user_profile
            # return temp_combined
            # 
            # Or, to keep it nested under 'employee_data' key:
            combined_data['employee_data'] = employee_data
        else:
            logger.warning(f"No HR employee data found for UPN {upn} (from user_id {user_id}). Returning profile with no employee data.")
            # combined_data['employee_data'] is already None, so no change needed here if that's the desired state

        return combined_data

    def get_employees_with_profile_by_location(self, location_name: str) -> List[dict]:
        matching_employees_with_profiles = []
        all_employee_upns = self.get_employees()

        for upn in all_employee_upns:
            employee_data = self.get_employee_data(upn)
            if employee_data and employee_data.get("locationName") == location_name:
                combined_data = {
                    'id': null,
                    'employee_data': employee_data,
                    'profile': None
                }
                
                # Attempt to get AAD ID and then the profile
                oid = self.get_aad_id_by_upn(upn)
                if oid:
                    user_profile = self.get_user_profile(oid)
                    if user_profile:
                        combined_data['id'] = oid
                        combined_data['profile'] = user_profile
                    else:
                        logger.warning(f"No profile found for OID {oid} (UPN: {upn})")
                else:
                    logger.warning(f"No OID found for UPN {upn} when fetching profile.")
                
                matching_employees_with_profiles.append(combined_data)
            elif not employee_data:
                logger.warning(f"No employee data found for UPN {upn} while checking location.")

        return matching_employees_with_profiles

    def get_orgchart(self) -> List[dict]:
        key = "hr:orgchart"
        # Decode bytes to strings, then load JSON to dict
        return [json.loads(position.decode('utf-8')) for position in self.redis.smembers(key)]

    def set_orgchart(self, positions: List[dict]) -> int:
        key = "hr:orgchart"
        pipe = self.redis.pipeline()
        pipe.delete(key)
        # Serialize dicts to JSON strings and remove duplicates
        unique_positions = {json.dumps(pos, sort_keys=True) for pos in positions}
        if unique_positions:
            pipe.sadd(key, *unique_positions)
        results = pipe.execute()
        count = results[-1] if len(results) > 1 else 0
        logger.info(f"Set all known positions: {count} for orgchart")
        return count