<script setup>
import { ref } from 'vue'
import { useI18n } from 'vue-i18n'
import { useUserStore } from '@/stores/userStore'

const { t, locale } = useI18n();
const userStore = useUserStore()
const annualLeaves = userStore.getAnnualLeaves
</script>

<template>
  <v-card height="100%" class="annual-leaves-card">
    <v-card-title class="d-flex align-center">
      <v-icon icon="mdi-calendar" color="primary" class="mr-2"></v-icon>
      {{ t('annualLeaves.title') }}
    </v-card-title>
    <v-card-text>
      <div class="d-flex justify-space-between align-center mb-2">
        <div>
          <div class="text-subtitle-2">{{ t('annualLeaves.remaining') }} / {{ t('annualLeaves.granted') }}</div>
          <div class="text-h5 font-weight-bold primary--text">{{ annualLeaves.remaining }} / {{ annualLeaves.granted }}</div>
        </div>
      </div>
      <v-progress-linear
        :model-value="(annualLeaves.remaining / annualLeaves.granted) * 100"
        color="success"
        height="10"
        rounded
      ></v-progress-linear>
    </v-card-text>
  </v-card>
</template>