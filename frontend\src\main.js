import { createApp } from 'vue'
import { createPinia } from 'pinia'
import './style.css'
import App from './App.vue'
import router from './router'
import i18n from './i18n'

// Vuetify
import 'vuetify/styles'
import { createVuetify } from 'vuetify'
import * as components from 'vuetify/components'
import * as directives from 'vuetify/directives'
import '@mdi/font/css/materialdesignicons.css'

// Vue Grid Layout
import VueGridLayout from 'vue-grid-layout-v3'


const vuetify = createVuetify({
  components: {
  ...components,
},
  directives,
  theme: {
    defaultTheme: 'aqua-mint',
    variations: {
      colors: ['primary', 'secondary', 'surface'],
      lighten: 1,
      darken: 2,
    },
    themes: {
      'aqua-mint': {
        "dark": false,
        "colors": {
          "primary": "#008787",
          "primary-variant": "#004343",
          "secondary": "#1EFFFF",
          "secondary-variant": "#69FFFF",
          "background": "#FFFFFF",
          "surface": "#F2F2F2",
          "error": "#B00020",
          "on-primary": "#FFFFFF",
          "on-secondary": "#000000",
          "on-background": "#000000",
          "on-surface": "#000000",
          "on-error": "#FFFFFF"
        }
      },
      'aqua-mint-dark': {
        "dark": true,
        "colors": {
          "primary": "#69FFFF",
          "primary-variant": "#1EFFFF",
          "secondary": "#B4FFFF",
          "secondary-variant": "#008787",
          "background": "#090A0F",
          "surface": "#181717",
          "error": "#CF6679",
          "on-primary": "#000000",
          "on-secondary": "#000000",
          "on-background": "#FFFFFF",
          "on-surface": "#FFFFFF",
          "on-error": "#000000"
        }
      },
      'ocean-sky': {
        "dark": false,
        "colors": {
          "primary": "#009DDB",
          "primary-variant": "#004E6D",
          "secondary": "#50CEFF",
          "secondary-variant": "#8BDEFF",
          "background": "#FFFFFF",
          "surface": "#F2F2F2",
          "error": "#B00020",
          "on-primary": "#FFFFFF",
          "on-secondary": "#000000",
          "on-background": "#000000",
          "on-surface": "#000000",
          "on-error": "#FFFFFF"
        }
      },
      'ocean-sky-dark': {
        "dark": true,
        "colors": {
          "primary": "#8BDEFF",
          "primary-variant": "#50CEFF",
          "secondary": "#C5EFFF",
          "secondary-variant": "#009DDB",
          "background": "#181717",
          "surface": "#004E6D",
          "error": "#CF6679",
          "on-primary": "#000000",
          "on-secondary": "#000000",
          "on-background": "#FFFFFF",
          "on-surface": "#FFFFFF",
          "on-error": "#000000"
        }
      },
      'lime-grove': {
        "dark": false,
        "colors": {
          "primary": "#77B800",
          "primary-variant": "#3C5C00",
          "secondary": "#BAFF3B",
          "secondary-variant": "#D1FF7D",
          "background": "#FFFFFF",
          "surface": "#F2F2F2",
          "error": "#B00020",
          "on-primary": "#FFFFFF",
          "on-secondary": "#000000",
          "on-background": "#000000",
          "on-surface": "#000000",
          "on-error": "#FFFFFF"
        }
      },
      'lime-grove-dark': {
        "dark": true,
        "colors": {
          "primary": "#D1FF7D",
          "primary-variant": "#BAFF3B",
          "secondary": "#E8FFBE",
          "secondary-variant": "#77B800",
          "background": "#004343",
          "surface": "#006565",
          "error": "#CF6679",
          "on-primary": "#000000",
          "on-secondary": "#000000",
          "on-background": "#FFFFFF",
          "on-surface": "#FFFFFF",
          "on-error": "#000000"
        }
      },
      'sunset-amber': {
        "dark": false,
        "colors": {
          "primary": "#FA943E",
          "primary-variant": "#984804",
          "secondary": "#FCBF8B",
          "secondary-variant": "#FDD4B2",
          "background": "#FFFFFF",
          "surface": "#F2F2F2",
          "error": "#B00020",
          "on-primary": "#FFFFFF",
          "on-secondary": "#000000",
          "on-background": "#000000",
          "on-surface": "#000000",
          "on-error": "#FFFFFF"
        }
      },
      'sunset-amber-dark': {
        "dark": true,
        "colors": {
          "primary": "#FDD4B2",
          "primary-variant": "#FCBF8B",
          "secondary": "#FEEAD8",
          "secondary-variant": "#FA943E",
          "background": "#1B1E2D",
          "surface": "#373D5B",
          "error": "#CF6679",
          "on-primary": "#000000",
          "on-secondary": "#000000",
          "on-background": "#FFFFFF",
          "on-surface": "#FFFFFF",
          "on-error": "#000000"
        }
      },
      'twilight-indigo': {
        "dark": false,
        "colors": {
          "primary": "#6771A4",
          "primary-variant": "#373D5B",
          "secondary": "#B3B8D1",
          "secondary-variant": "#E1E3ED",
          "background": "#FFFFFF",
          "surface": "#F2F2F2",
          "error": "#B00020",
          "on-primary": "#FFFFFF",
          "on-secondary": "#000000",
          "on-background": "#000000",
          "on-surface": "#000000",
          "on-error": "#FFFFFF"
        }
      },
      'twilight-indigo-dark': {
        "dark": true,
        "colors": {
          "primary": "#B3B8D1",
          "primary-variant": "#6771A4",
          "secondary": "#E1E3ED",
          "secondary-variant": "#373D5B",
          "background": "#181717",
          "surface": "#3B3838",
          "error": "#CF6679",
          "on-primary": "#000000",
          "on-secondary": "#000000",
          "on-background": "#FFFFFF",
          "on-surface": "#FFFFFF",
          "on-error": "#000000"
        }
      },
      'crimson-blaze': {
        "dark": false,
        "colors": {
          "primary": "#E46C06",
          "primary-variant": "#984804",
          "secondary": "#FA943E",
          "secondary-variant": "#FCBF8B",
          "background": "#FFFFFF",
          "surface": "#FEEAD8",
          "error": "#B00020",
          "on-primary": "#FFFFFF",
          "on-secondary": "#000000",
          "on-background": "#000000",
          "on-surface": "#000000",
          "on-error": "#FFFFFF"
        }
      },
      'crimson-blaze-dark': {
        "dark": true,
        "colors": {
          "primary": "#FCBF8B",
          "primary-variant": "#FA943E",
          "secondary": "#FEEAD8",
          "secondary-variant": "#FDD4B2",
          "background": "#1B1E2D",
          "surface": "#265656",
          "error": "#CF6679",
          "on-primary": "#000000",
          "on-secondary": "#000000",
          "on-background": "#FFFFFF",
          "on-surface": "#FFFFFF",
          "on-error": "#000000"
        }
      },
      'midnight-mirage':{
        "dark": false,
        "colors": {
          "primary": "#373D5B",
          "primary-variant": "#1B1E2D",
          "secondary": "#6771A4",
          "secondary-variant": "#B3B8D1",
          "background": "#FFFFFF",
          "surface": "#F4F6F4",
          "error": "#B00020",
          "on-primary": "#FFFFFF",
          "on-secondary": "#000000",
          "on-background": "#000000",
          "on-surface": "#000000",
          "on-error": "#FFFFFF"
        }
      },
      'midnight-mirage-dark':{
        "dark": true,
        "colors": {
          "primary": "#B3B8D1",
          "primary-variant": "#6771A4",
          "secondary": "#E1E3ED",
          "secondary-variant": "#373D5B",
          "background": "#181717",
          "surface": "#3B3838",
          "error": "#CF6679",
          "on-primary": "#000000",
          "on-secondary": "#000000",
          "on-background": "#FFFFFF",
          "on-surface": "#FFFFFF",
          "on-error": "#000000"
        }
      },
      'verdant-vortex': {
        "dark": false,
        "colors": {
          "primary": "#77B800",
          "primary-variant": "#598A00",
          "secondary": "#BAFF3B",
          "secondary-variant": "#D1FF7D",
          "background": "#FFFFFF",
          "surface": "#F2F2F2",
          "error": "#B00020",
          "on-primary": "#FFFFFF",
          "on-secondary": "#000000",
          "on-background": "#000000",
          "on-surface": "#000000",
          "on-error": "#FFFFFF"
        }
      },
      'verdant-vortex-dark':{
        "dark": true,
        "colors": {
          "primary": "#D1FF7D",
          "primary-variant": "#BAFF3B",
          "secondary": "#E8FFBE",
          "secondary-variant": "#77B800",
          "background": "#1B1E2D",
          "surface": "#004E6D",
          "error": "#CF6679",
          "on-primary": "#000000",
          "on-secondary": "#000000",
          "on-background": "#FFFFFF",
          "on-surface": "#FFFFFF",
          "on-error": "#000000"
        }
      },
    }
  }
})

const pinia = createPinia()
const app = createApp(App)
app.use(i18n)
app.use(vuetify)
app.use(VueGridLayout)
app.use(router)
app.use(pinia)
app.mount('#app')
