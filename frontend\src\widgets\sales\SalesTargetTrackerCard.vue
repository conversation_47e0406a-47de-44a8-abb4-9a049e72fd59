<script setup>
import { ref, watch } from 'vue'

const salesTarget = ref(1000000)
const currentSales = ref(750000)
const bonusThresholds = ref([
  { target: 800000, bonus: 5000, color: 'warning' },
  { target: 900000, bonus: 10000, color: 'success' },
  { target: 1000000, bonus: 20000, color: 'info' },
])

const progress = ref((currentSales.value / salesTarget.value) * 100)

const nextBonus = ref(null)

const updateNextBonus = () => {
  nextBonus.value = bonusThresholds.value.find(threshold => threshold.target > currentSales.value) || null
}

// Initial calculation
updateNextBonus()

// Update when currentSales changes
watch(currentSales, () => {
  updateNextBonus()
})
</script>

<template>
  <v-card class="mx-auto" max-width="600">
    <v-card-title class="d-flex align-center">
      <v-icon icon="mdi-target" color="primary" class="mr-2"></v-icon>
      Sales Target Tracker
    </v-card-title>

    <v-card-text>
      <div class="text-h4 font-weight-thin mb-4">
        ${{ currentSales.toLocaleString() }}
        <span class="text-subtitle-1 font-weight-light">/ ${{ salesTarget.toLocaleString() }}</span>
      </div>

      <v-progress-linear
        v-model="progress"
        color="primary"
        height="25"
        striped
      >
        <template v-slot:default="{ value }">
          <strong>{{ Math.ceil(value) }}%</strong>
        </template>
      </v-progress-linear>

      <v-sheet class="mt-6">
        <div class="text-h6 mb-2">Bonus Thresholds</div>
        <v-timeline density="compact" align="start">
          <v-timeline-item
            v-for="threshold in bonusThresholds"
            :key="threshold.target"
            :dot-color="threshold.color"
            size="small"
          >
            <div class="d-flex justify-space-between align-center">
              <div>
                <strong>${{ threshold.target.toLocaleString() }}</strong>
                <div class="text-caption">Bonus: ${{ threshold.bonus.toLocaleString() }}</div>
              </div>
              <v-chip
                v-if="currentSales >= threshold.target"
                color="success"
                size="small"
              >
                Achieved
              </v-chip>
            </div>
          </v-timeline-item>
        </v-timeline>
      </v-sheet>
    </v-card-text>

    <v-divider></v-divider>

    <v-card-actions class="justify-center">
      <v-btn
        variant="text"
        block
        :color="nextBonus ? 'primary' : 'success'"
      >
        {{ nextBonus 
          ? `$${(nextBonus.target - currentSales).toLocaleString()} to next bonus` 
          : 'All targets achieved!' }}
      </v-btn>
    </v-card-actions>
  </v-card>
</template>