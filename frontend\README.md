# Overview
This is a Vuetify frontend server for Moxa Employee Portal application. It provides the frontend UI for inteacting with various widgets and AI chatbot functionality.

# Development
## Prerequisites
- Node.js (v18 or higher)
- Git
## Git Workflow
**TODO**: define git workflow here.

# Installation
First, clone the repository, then install the dependencies:

```bash
npm install
```

# Environment Variables
Before running the app, make sure to set up your environment variables. Copy the `.env.example` to `.env` and replace the settings there.
Key environment variables needed:
- `VITE_BACKEND_API_URL`: http://localhost:8000
- `VITE_GOOGLE_MAPS_API_KEY`=YourGoogleMapsAPIKey -> for office floor plan


See `.env.example` for all required variables.

# Running the App
```bash
npm run dev
```

The frontend will be running on http://localhost:5173.

# Troubleshooting
## Common Issues
1. Node Version Issues
   - Ensure you're using the correct Node.js version
   - Try removing node_modules and package-lock.json
   - Run `npm install` again

2. Build Errors
   - Check for ESLint errors: `npm run lint`
   - Verify all dependencies are installed
   - Clear npm cache: `npm cache clean --force`

3. Runtime Errors
   - Check browser console (F12) for errors
   - Verify environment variables are set correctly
   - Check backend API connectivity

## Getting Help
1. Check the console logs from development tools by pressing F12
2. Review the documentation
3. Contact Graham for help

# Roadmap
## 第一階段：啟動期（Launch）—「打造可用且有感的入口」
目標：提供基本的個人化資訊入口、組織資訊存取與 AI 助理服務。
### 平台基礎建設
- [x] 多語言支援
- [x] 響應式設計（RWD）
- [x] 主題切換支援
- [ ] 新使用者導引／教學
- [ ] 支援使用 SSO 的 AAD 登入
### 基本角色導向首頁
- [x] 依使用者角色顯示的 Widget-able 版面
- [x] 使用者資訊（包含員工基本資訊）
- [ ] 個人可儲存的自定義版面（簡單版）
### 常用核心 Widgets
- [x] 公告列表／公司部門新聞
- [x] 福利點數
- [x] 年假資訊
- [x] 重要活動
- [x] 我的最愛應用程式
- [x] To-Do 清單／倒數計時
- [x] Power BI 卡片
- [x] 天氣卡片
### 各部門關鍵 Widgets（MVP 級）
- [x] Engineering: CI/CD、Bug Count、工單看板
- [x] Sales: Sales KPI、Pipeline、合約狀態
- [ ] IT: 工單查詢、IT 資訊
- [ ] HR: 福利查詢、HR 資訊
- [ ] 財務：薪資查詢、帳務資訊
### AI 聊天助理 MVP
- [x] 多模型選擇（OpenAI, Google, Aliyun）
- [x] 支援 Session 管理
- [x] 模式切換（快速／推理／研究）
- [x] RAG 基礎整合（LightRAG）
- [x] 聲音輸入輸出（TTS / STT）
- [x] MCP 協定支援（Stdio, HTTP）
### 組織導向資訊查找
- [x] 組織圖（報表線清楚）
- [x] 樓層導覽整合（建物、區域視角）
- [x] 行事曆整合（Company Calendar）
## 第二階段：擴展期（Growth）—「賦能工作者與提升部門效率」
目標：擴展個人化、自助與協作功能，提升工作效率與部門價值。
### Layout & Widget 升級
- [ ] 多版面切換（例：業務模式 / 專案模式）
- [ ] Widget Library（角色可選、即插即用）
- [ ] 小工具角色權限控管
- [ ] 自訂版面儲存（多組）
### 小工具模組擴展
- [ ] Learning Center Widget (集成 LMS 或內部訓練資料，提供個人學習進度與推薦)
- [ ] 部門KPI看板 (不同部門可透過簡單設定管理目標與成果)
- [ ] 內推與徵才 Widget (提供同仁推薦與即時回饋，連接 HRM 系統)
- [ ] 資訊安全教育提醒 (定期浮出視窗或 Widget，提示需完成的教育)
### AI 智能輔助擴展
- [ ] 即時搜尋網頁回答
- [ ] AI 文件摘要卡片（PowerBI、公告等）
- [ ] AI Widget 建議（根據使用情境）
- [ ] AI Meeting Assistant（與 Outlook 整合）
- [ ] 文件上傳與摘要
- [ ] Agent 模式
### 協作與社交互動
- [ ] 留言／按讚／評論功能
- [ ] 員工動態牆（新進、升遷）
- [ ] 生日／週年通知
- [ ] 部門主頁 Widget
### 管理與維運後台
- [ ] Widget Store 後台（審核發佈）
- [ ] 系統管理頁面（角色、Widget、用戶）
- [ ] Back Office 頁面（樓層與新聞維護）
### 整合與效能工具
- [ ] 員工通訊錄
- [ ] 員工／會議室空閒查詢
- [ ] 小工具之間的事件中心／Webhook
- [ ] 個人 To-Do 整合其他系統（BPM/Jira）
## 第三階段：進化期（Mature）—「智慧化與自主學習平台」
目標：實現全方位數位員工體驗平台，持續最佳化並開放生態系發展。
### 自學與個人化
- [ ] AI 排序與推薦 Widget
- [ ] A/B 測試平台（UX/AI 評估）
- [ ] 根據使用者偏好自動調整首頁
### 使用行為分析與優化
- [ ] Widget 使用率統計
- [ ] 使用熱區點擊分析
- [ ] AI 聊天紀錄分析（命中率與回饋）
- [ ] 使用時間與趨勢分析
### 生態整合與企業價值鏈延伸
- [ ] 整合打卡／門禁系統
- [ ] 食堂訂餐
- [ ] Moxa Connect活動登錄
- [ ] 整合福利商城
- [ ] 整合企業知識庫（Notion, Confluence, SharePoint）
- [ ] 整合績效／KPI 追蹤系統
### 資訊安全與治理
- [ ] 敏感資料遮罩（KPI、薪資）
- [ ] 操作稽核與異常警示
- [ ] OAuth 範圍控管與快取
- [ ] 角色切換功能
- [ ] 個資點擊才顯示（組織圖 Email）

# Project Structure
```bash
frontend/
├── public/
│    └── index.html           # Main HTML template
├── src/
│    ├── assets/              # Images, icons, styles, fonts
│    ├── components/          # Reusable UI components (buttons, cards, dialogs)
│    │    ├── ChatInput.vue
│    │    ├── MermaidDiagram.vue
│    │    └── OrgChart.vue
│    ├── composables/         # Vue composition functions
│    ├── locales/             # Translation files
│    │    ├── en.json
│    │    ├── zh-CN.json
│    │    └── zh-TW.json
│    ├── pages/               # Top-level pages (Home.vue, Profile.vue, Admin.vue)
│    │    ├── CompanyCalendarPage.vue
│    │    ├── OfficeSimulationPage.vue
│    │    ├── OrgChartPage.vue
│    │    └── ChatBotPage.vue
│    ├── plugins/             # Vue plugins (e.g., Vue Router, Pinia) 
│    ├── router/              # Vue Router setup
│    ├── store/               # Pinia (state management, e.g., user store, widget store)
│    │    ├── eventStore.js
│    │    ├── filterStore.js
│    │    ├── layoutStore.js
│    │    ├── menuStore.js
│    │    └── userStore.js
│    ├── widgets/             # Widgets library (individual, lazy-loaded widgets)
│    │    ├── common/
│    │    │    ├── AnnouncementListCard.vue
│    │    │    ├── AnnualLeavesCard.vue
│    │    │    ├── BenefitPointsCard.vue
│    │    │    ├── ...
│    │    │    └── CompanyCalendarCard.vue
│    │    ├── hr/
│    │    ├── it/
│    │    ├── logistics/
│    │    ├── sales/
│    │    ├── engineering/
│    │    └── customer_support/
│    ├── services/            # API clients
│    │    ├── chatService.js
│    │    ├── floorplanService.js
│    │    └── userService.js 
│    ├── utils/               # Utility functions/helpers (formatters, validators)
│    ├── App.vue              # Root component
│    ├── main.js              # App entry point
├── vite.config.js            # Bundler config
├── package.json              # npm packages
└── README.md
```

# Build
## Development Build
```bash
npm run dev
```

## Production Build
```bash
npm run build
```
The build output will be in the dist directory. 

### Build Optimization Notes 
1.  Large Chunk Warnings 
   - Some chunks are larger than 500KB after minification 
   - Consider the following optimizations: 
      - Use dynamic import() for code splitting 
      - Configure build.rollupOptions.output.manualChunks in vite.config.js 
      - Adjust chunk size warning limit if needed     
2.  Current Build Statistics
   - Build time: ~1.5 minutes 
   - Main bundle (index-Dff2EAio.js): ~2.2MB (650KB gzipped) 
   - CSS bundle: ~804KB (115KB gzipped) 
   - Various diagram-related chunks ranging from 0.1KB to 425KB   
3.  Performance Considerations 
   - Large assets are properly code-split 
   - Material Design Icons are included in multiple formats 
   - SVG assets might need optimization (iTower_22F is 3.5MB)

# Performance Evaluation
Evaluating AI chat application’s **performance** involves **quantitative** and **qualitative** metrics across three key dimensions: **throughput**, **credibility**, and **capability**:

---

## 🔷 1. **Throughput (Speed & Scalability)**

This is about how efficiently this system processes queries and scales under load.

### ✅ **Key Metrics**

| Metric                       | Description                                                               |
| ---------------------------- | ------------------------------------------------------------------------- |
| **Latency**                  | Average time from user input to first response token and to full response |
| **Tokens/sec**               | Average generation rate (tokens per second) from LLM                      |
| **QPS (Queries per Second)** | Max number of concurrent requests handled                                 |
| **Memory/CPU usage**         | During peak vs idle periods                                               |
| **Success rate**             | % of successful completions vs timeouts/errors                            |
| **Load test**                | How the system performs with 10x, 100x concurrent users                   |

### 🔧 **Tools to Measure**

* **Backend logs** (timestamp deltas)
* **OpenTelemetry / Prometheus + Grafana**
* **Locust, k6 or JMeter** for load testing

### 🧪 Suggested Test

Run batch scenarios:

* Simple questions (quick mode)
* Function calls / tool usage (MCP)
* Streaming RAG responses

---

## 🔷 2. **Credibility (Trustworthiness & Factual Accuracy)**

This evaluates whether the chatbot gives **reliable, transparent, and traceable** answers.

### ✅ **Key Metrics**

| Metric                     | Description                                                 |
| -------------------------- | ----------------------------------------------------------- |
| **Faithfulness**           | Does output align with verified facts?                      |
| **Hallucination rate**     | % of generated info not grounded in context or reality      |
| **Citations**              | Are RAG responses traceable to real documents?              |
| **Tool usage correctness** | Does the LLM call the correct tool with correct args?       |
| **User feedback**          | % of “thumbs up/down” or other UX-based trust signals       |
| **Audit logs**             | Whether reasoning steps or sources are recorded per message |

### 🧪 Suggested Evaluation Approach

* Sample **100+ chat logs**, rate answers on:

  * Accurate / Misleading / Hallucinated
  * Citations (if used)
  * Reasoning chain (if applicable)
* Use tools like [TruthfulQA](https://arxiv.org/abs/2109.07958) or synthetic QA for automated scoring
* Track feedback if frontend allows it

---

## 🔷 3. **Capability (Functional Intelligence)**

This is about what this chatbot **can do** and **how well** it does it.

### ✅ **Key Metrics**

| Area                      | Evaluation                                                               |
| ------------------------- | ------------------------------------------------------------------------ |
| **Instruction following** | Does it follow prompts correctly, especially reasoning / research modes? |
| **Tool calling**          | Correct tool chosen? Arguments parsed accurately?                        |
| **Multilingual support**  | Handles various input/output languages reliably?                         |
| **RAG integration**       | Can it use LightRAG context effectively to improve responses?            |
| **Memory handling**       | Contextual understanding of prior conversation turns?                    |
| **Fallback quality**      | Graceful degradation when tools fail or LLM errors out                   |

### 🧪 Suggested Method

* Run benchmark tasks across modes:

  * 🧠 Reasoning (math, logic)
  * 📖 Research (e.g. deep answers with LightRAG)
  * ⚙️ Tool (calls MCP tools with structured inputs)
  * 💬 Follow-up questions
* Use prompt-based test suites, e.g.:

  * [OpenAI evals](https://github.com/openai/evals)
  * [HELM](https://crfm.stanford.edu/helm/latest/)
  * Custom evaluation prompts

---

## 📊 Example Dashboard (KPI Suggestions)

| Category    | Metric                             | Target          |
| ----------- | ---------------------------------- | --------------- |
| Throughput  | P95 latency (ms)                   | < 3000          |
|             | QPS under 100 users                | > 20            |
| Credibility | Citation coverage (%)              | > 90%           |
|             | Manual accuracy score              | > 85%           |
| Capability  | Tool call success rate             | > 95%           |
|             | Instruction follow rate            | > 90%           |
|             | RAG value add (BLEU, human rating) | Measurable gain |

---

## 🎯 Final Tip: **Continuous Evaluation Loop**

* Add automatic logging of: `query + LLM output + tool call + RAG source + feedback + latency`
* Build an internal **“eval viewer”** to filter & review outputs weekly
* Consider **A/B testing** different system prompts, models, or RAG providers

---

