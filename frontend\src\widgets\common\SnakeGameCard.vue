<script setup>
import { ref, onMounted, onUnmounted, watch, nextTick } from 'vue'
import { useTheme } from 'vuetify'
import { useI18n } from 'vue-i18n'

const { t } = useI18n()

const props = defineProps({
  isDark: {
    type: Boolean,
    default: false
  }
})

const canvas = ref(null)
const score = ref(0)
const gameOver = ref(false)
const isPaused = ref(false)
const theme = useTheme()

let ctx
let snake = [
  { x: 10, y: 10 }
]
let food = { x: 15, y: 15 }
let dx = 1
let dy = 0
let gameLoop

const gridSize = 20
const tileCount = 20

function drawGame() {
  if (!canvas.value || !ctx) return
  clearCanvas()
  moveSnake()
  drawSnake()
  drawFood()
  drawScore()
  
  if (gameOver.value) {
    drawGameOver()
    return
  }
  
  if (!isPaused.value) {
    setTimeout(drawGame, 100)
  }
}

function clearCanvas() {
  ctx.fillStyle = theme.global.current.value.dark ? '#1E1E1E' : '#FFFFFF'
  ctx.fillRect(0, 0, canvas.value.width, canvas.value.height)
}

function drawSnake() {
  ctx.fillStyle = theme.global.current.value.dark ? '#4CAF50' : '#2E7D32'
  snake.forEach(segment => {
    ctx.fillRect(segment.x * gridSize, segment.y * gridSize, gridSize - 2, gridSize - 2)
  })
}

function drawFood() {
  ctx.fillStyle = theme.global.current.value.dark ? '#F44336' : '#D32F2F'
  ctx.fillRect(food.x * gridSize, food.y * gridSize, gridSize - 2, gridSize - 2)
}

function drawScore() {
  ctx.fillStyle = theme.global.current.value.dark ? '#FFFFFF' : '#000000'
  ctx.font = '20px Arial'
  ctx.fillText(`${t('snakeGameCard.score')}: ${score.value}`, 10, 30)
}

function drawGameOver() {
  ctx.fillStyle = theme.global.current.value.dark ? '#FFFFFF' : '#000000'
  ctx.font = '40px Arial'
  ctx.fillText(`${t('snakeGameCard.gameOver')}!`, canvas.value.width / 4, canvas.value.height / 2)
  ctx.font = '20px Arial'
  ctx.fillText(`${t('snakeGameCard.instruction')} ${t('snakeGameCard.restart')}`, canvas.value.width / 4, canvas.value.height / 2 + 40)
}

function moveSnake() {
  const head = { x: snake[0].x + dx, y: snake[0].y + dy }
  
  // Check wall collision
  if (head.x < 0 || head.x >= tileCount || head.y < 0 || head.y >= tileCount) {
    gameOver.value = true
    return
  }
  
  // Check self collision
  if (snake.some(segment => segment.x === head.x && segment.y === head.y)) {
    gameOver.value = true
    return
  }
  
  snake.unshift(head)
  
  // Check food collision
  if (head.x === food.x && head.y === food.y) {
    score.value += 10
    generateFood()
  } else {
    snake.pop()
  }
}

function generateFood() {
  food = {
    x: Math.floor(Math.random() * tileCount),
    y: Math.floor(Math.random() * tileCount)
  }
  
  // Ensure food doesn't spawn on snake
  while (snake.some(segment => segment.x === food.x && segment.y === food.y)) {
    food = {
      x: Math.floor(Math.random() * tileCount),
      y: Math.floor(Math.random() * tileCount)
    }
  }
}

function handleKeyPress(e) {
  if (e.code === 'Space') {
    if (gameOver.value) {
      resetGame()
    } else {
      isPaused.value = !isPaused.value
      if (!isPaused.value) drawGame()
    }
    return
  }
  
  if (gameOver.value || isPaused.value) return
  
  switch (e.code) {
    case 'KeyW':
      if (dy !== 1) { dx = 0; dy = -1 }
      break
    case 'KeyS':
      if (dy !== -1) { dx = 0; dy = 1 }
      break
    case 'KeyA':
      if (dx !== 1) { dx = -1; dy = 0 }
      break
    case 'KeyD':
      if (dx !== -1) { dx = 1; dy = 0 }
      break
  }
}

function resetGame() {
  snake = [{ x: 10, y: 10 }]
  dx = 1
  dy = 0
  score.value = 0
  gameOver.value = false
  isPaused.value = false
  generateFood()
  drawGame()
}

onMounted(() => {
  // 使用 nextTick 确保 DOM 已更新
  nextTick(() => {
    if (!canvas.value) return
    ctx = canvas.value.getContext('2d')
    canvas.value.width = gridSize * tileCount
    canvas.value.height = gridSize * tileCount
    window.addEventListener('keydown', handleKeyPress)
    resetGame()
  })
})

onUnmounted(() => {
  window.removeEventListener('keydown', handleKeyPress)
})

watch(() => theme.global.current.value.dark, () => {
  if (!gameOver.value && !isPaused.value) {
    drawGame()
  }
})
</script>

<template>
  <v-card class="casual-game-card">
    <v-card-title class="d-flex justify-space-between align-center">
      <v-icon icon="mdi-gamepad-variant" color="primary" class="mr-2"></v-icon>
      {{ t('snakeGameCard.title') }}
      <v-spacer></v-spacer>
      <v-btn
        icon="mdi-refresh"
        variant="text"
        size="small"
        @click="resetGame"
      ></v-btn>
    </v-card-title>
    <v-card-text class="text-center">
      <canvas
        ref="canvas"
        class="game-canvas"
        tabindex="0"
      ></canvas>
      <div class="mt-2">
        <v-chip
          class="mr-2"
          :color="isPaused ? 'warning' : 'success'"
          size="small"
        >
          {{ isPaused ? t('snakeGameCard.paused') : t('snakeGameCard.playing') }}
        </v-chip>
        <span class="text-subtitle-2">{{ t('snakeGameCard.instruction') }} {{ isPaused ? t('snakeGameCard.resume') : t('snakeGameCard.pause') }}</span>
      </div>
    </v-card-text>
  </v-card>
</template>

<style scoped>
.casual-game-card {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.game-canvas {
  border: 1px solid rgba(0, 0, 0, 0.12);
  border-radius: 4px;
}

:deep(.v-card-text) {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
</style>