from pydantic import BaseModel
from typing import List, Optional


class WidgetSettings(BaseModel):
    refresh_interval: Optional[int] = None
    other_settings: Optional[dict] = None


class WidgetPosition(BaseModel):
    widget_id: str
    x: int
    y: int
    w: int
    h: int
    settings: Optional[WidgetSettings] = None


class WidgetLayoutSaveRequest(BaseModel):
    layout: List[WidgetPosition]
