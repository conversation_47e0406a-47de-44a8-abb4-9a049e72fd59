import { ref } from 'vue'

export const defaultLayouts = () => ref([
    {
        "layoutId": "default-layout",
        "applicableRoles": [],		        // applicable for all roles
        "widgets": [
            { "i": "benefit-points",           "x": 0, "y": 0,  "w": 3, "h": 4 },
            { "i": "annual-leaves",            "x": 3, "y": 0,  "w": 3, "h": 4 },
            { "i": "todo",                     "x": 6, "y": 0,  "w": 3, "h": 4 },
            { "i": "countdown-timer",          "x": 9, "y": 0,  "w": 3, "h": 4 },
            { "i": "announcement-list",        "x": 0, "y": 4,  "w": 3, "h": 12 },
            { "i": "upcoming-company-event",   "x": 3, "y": 4,  "w": 3, "h": 12 },
            { "i": "company-news",             "x": 6, "y": 4,  "w": 3, "h": 12 },
            { "i": "ehs-news",                 "x": 9, "y": 4,  "w": 3, "h": 12 },
            { "i": "common-apps",              "x": 0, "y": 16, "w": 3, "h": 11 },
            { "i": "todo-list",                "x": 3, "y": 16, "w": 6, "h": 11 },
            { "i": "snake-game",               "x": 9, "y": 16, "w": 3, "h": 12 },
        ]
    },
    {
        "layoutId": "sales-default-layout",
        "applicableRoles": ["sales"],		// applicable for sales
        "widgets": [
            { "i": "benefit-points",           "x": 0, "y": 0,  "w": 3, "h": 4 },
            { "i": "annual-leaves",            "x": 3, "y": 0,  "w": 3, "h": 4 },
            { "i": "todo",                     "x": 6, "y": 0,  "w": 3, "h": 4 },
            { "i": "sales-kpi",                "x": 9, "y": 0,  "w": 3, "h": 4 },
            { "i": "sales-apps",               "x": 0, "y": 4,  "w": 3, "h": 13 },
            { "i": "sales-target-tracker",     "x": 3, "y": 4,  "w": 4, "h": 14 },
            { "i": "pipeline-overview",        "x": 7, "y": 4,  "w": 5, "h": 14 },
            { "i": "contract-status",          "x": 0, "y": 18, "w": 7, "h": 13 },
        ]
    },
    {
        "layoutId": "engineering-default-layout",
        "applicableRoles": ["it", "engineering", "quality"], // applicable for it, engineering, quality
        "widgets": [
            { "i": "benefit-points",           "x": 0, "y": 0,  "w": 3, "h": 4 },
            { "i": "annual-leaves",            "x": 3, "y": 0,  "w": 3, "h": 4 },
            { "i": "todo",                     "x": 6, "y": 0,  "w": 3, "h": 4 },
            { "i": "bug-count",                "x": 9, "y": 0,  "w": 3, "h": 4 },
            { "i": "announcement-list",        "x": 0, "y": 4,  "w": 3, "h": 12 },
            { "i": "upcoming-company-event",   "x": 3, "y": 4,  "w": 3, "h": 12 },
            { "i": "company-news",             "x": 6, "y": 4,  "w": 3, "h": 12 },
            { "i": "ehs-news",                 "x": 9, "y": 4,  "w": 3, "h": 12 },
            { "i": "ticket-board",             "x": 0, "y": 16, "w": 6, "h": 11 },
            { "i": "cicd-status",              "x": 6, "y": 16, "w": 6, "h": 11 },
        ]
    },
])
