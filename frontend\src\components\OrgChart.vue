<script setup>
import { ref, computed, watch, onMounted } from 'vue';
import { OrgChart } from 'd3-org-chart';
import { useI18n } from 'vue-i18n';
import { useTheme } from 'vuetify';
import { getPhoto } from '@/utils/photo';
import { storeToRefs } from 'pinia';
import { useFilterStore } from '@/stores/filterStore';

const theme = useTheme();
const filterStore = useFilterStore();
const { filterValue } = storeToRefs(filterStore);

const props = defineProps(['data']);
const { t } = useI18n();
const chartReference = ref(null);
const svgElementContainer = ref(null);
const chartReady = ref(false);

const fgColor = computed(() => theme.current.value.colors["on-surface"]);
const bgColor = computed(() => theme.current.value.colors["surface"]);
console.log(fgColor.value, bgColor.value);
// const bgColor = "#373D5B";

function renderChart(data) {
  if (!data) return;
  chartReference.value
    .container(svgElementContainer.value)
    .data(data)
    .nodeHeight((d) => 130)
    .nodeWidth((d) => 230)
    .childrenMargin((d) => 50)
    .compactMarginBetween((d) => 35)
    .compactMarginPair((d) => 30)
    .neighbourMargin((a, b) => 20)
    .nodeContent(function (d, i, arr, state) {
        const imageDiffVert = 27;
        return `
        <div style='width:${d.width}px;height:${d.height}px;padding-top:${imageDiffVert - 2}px;padding-left:1px;padding-right:1px'>
            <div style="font-family: 'Inter', sans-serif;background-color:${bgColor.value};margin-left:-1px;width:${d.width - 2}px;height:${d.height - imageDiffVert}px;border-radius:10px;border: ${d.data._highlighted || d.data._upToTheRootHighlighted ? '5px solid #E27396' : '1px solid #E4E2E9'};">
            <div style="display:flex;justify-content:flex-end;margin-top:5px;margin-right:8px;color:${fgColor.value};">${d.data.location}</div>
            <div style="background-color:${bgColor.value};margin-top:${-imageDiffVert - 20}px;margin-left:${15}px;border-radius:100px;width:50px;height:50px;"></div>
            <div style="margin-top:${-imageDiffVert - 20}px;">   <img src="${getPhoto(d.data)}" style="margin-left:${20}px;border-radius:100px;width:40px;height:40px;"/></div>
            <div style="font-size:15px;color:${fgColor.value};margin-left:20px;margin-top:10px">  ${d.data.name} </div>
            <div style="color:${fgColor.value};margin-left:20px;margin-top:3px;font-size:10px;"> ${d.data.departmentName} </div>
            <div style="color:${fgColor.value};margin-left:20px;margin-top:3px;font-size:10px;"> ${d.data.positionName} </div>
            <div style="color:${fgColor.value};margin-left:20px;margin-top:3px;font-size:10px;"><a href:"mailto:${d.data.mail}" target="_blank">${d.data.mail}</a></div>
            </div>
        </div>
        `;
    })
    .render();

  chartReady.value = true;
}
watch(() => props.data, (value) => {
  renderChart(value);
});

// Watch for theme changes and re-render the chart
watch(() => theme.current.value.colors, () => {
  renderChart(props.data);
}, { deep: true });

onMounted(() => {
  chartReference.value = new OrgChart();
  renderChart(props.data);
});

function resetChart() {
  filterStore.resetFilter(); // Reset the filter in Pinia
  const chart = chartReference.value;
  if (!chart ||!chart.data()) return;
  chart.clearHighlighting();
  const data = chart.data();
  data.forEach((d) => (d._expanded = false));
  chart.data(data).render().fit();
}

function filterChart(e) {
  const value = e.target.value;
  filterStore.setFilter(value); // Update the filter in Pinia

  const chart = chartReference.value;
  if (!chart ||!chart.data()) return;

  chart.clearHighlighting();
  const data = chart.data();
  data.forEach((d) => (d._expanded = false));
  let count = 0
  let highlighted = []
  data.forEach((d) => {
    if (value !== '' && (d.name.toLowerCase().includes(value.toLowerCase())) || 
      (d.departmentName.toLowerCase().includes(value.toLowerCase())) || 
      (d.id.toLowerCase().includes(value.toLowerCase())) || 
      (d.positionName.toLowerCase().includes(value.toLowerCase())) ) {
      d._highlighted = true;
      d._expanded = true;
      count++;
      highlighted.push(d)
    }
  });
  if (count === 1) {
    chart.setUpToTheRootHighlighted(highlighted[0].id);
  }
  chart.data(data).render().fit();
}
</script>
<template>
  <v-container fluid>
    <!-- Add search bar -->
    <v-card class="search-bar" elevation="10">
      <v-text-field
        v-model="filterValue"
        :label="t('common.search')"
        type="text"
        density="compact"
        variant="outlined"
        prepend-inner-icon="mdi-magnify"
        single-line
        hide-details
        clearable
        @keyup.enter="filterChart"
        @click:clear="resetChart"
      ></v-text-field>
    </v-card>
    <div ref="svgElementContainer"></div>
  </v-container>
</template>
<style scoped>
.search-bar {
  position: absolute;
  top: 100px;
  right: 20px;
  width: 400px;
  padding: 20px;
  opacity: 0.75;
  backdrop-filter: blur(5px);
  background-color: rgb(var(--v-theme-surface-lighten-1));
  color: rgb(var(--v-theme-on-surface));
}
</style>