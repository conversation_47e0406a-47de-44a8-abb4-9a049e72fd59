<template>
      <div
        v-for="message in visibleMessages"
        :key="message.id"
        class="d-flex mb-4"
        :class="message.sender === 'user' ? 'justify-end' : 'justify-start'"
      >
        <div
          class="d-flex align-start"
          :class="message.sender === 'user' ? 'flex-row-reverse' : ''"
        >
          <v-avatar
            class="mx-2"
            size="24"
          >
            <v-img
              v-if="message.sender === 'user'"
              :src="getPhoto(userStore.currentUser)" 
              alt="User" 
              rounded="xs"
              width="24"
              height="24"
            />
            <v-img 
              v-else
              :src="MoxartIcon" 
              alt="Moxart"
              rounded="xs"
              width="24"
              height="24"
            />
          </v-avatar>
          <div>
            <v-card
              color="surface-lighten-1"
              class="pa-3 rounded-lg message-card elevation-2"
              max-width="600px"
            >
              <div v-if="message.sender === 'bot' && message.think" class="mb-2">
                <v-expansion-panels class="secondary text-on-secondary">
                  <v-expansion-panel>
                    <v-expansion-panel-title>
                      <v-icon icon="mdi-thought-bubble" class="mr-2"></v-icon>
                      {{ t('chatbot.reasoning') }}
                    </v-expansion-panel-title>
                    <v-expansion-panel-text>
                      <div class="markdown-content" v-html="marked(message.think)"></div>
                    </v-expansion-panel-text>
                  </v-expansion-panel>
                </v-expansion-panels>
              </div>
              <div v-if="message.sender === 'bot' && message.evaluate" class="mb-2">
                <v-expansion-panels class="secondary text-on-secondary">
                  <v-expansion-panel>
                    <v-expansion-panel-title>
                      <v-icon icon="mdi-test-tube" class="mr-2"></v-icon>
                      {{ t('chatbot.evaluate') }}
                    </v-expansion-panel-title>
                    <v-expansion-panel-text>
                      <div class="markdown-content" v-html="marked(message.evaluate)"></div>
                    </v-expansion-panel-text>
                  </v-expansion-panel>
                </v-expansion-panels>
              </div>
              <div v-if="message.sender === 'bot' && message.refine" class="mb-2">
                <v-expansion-panels class="secondary text-on-secondary">
                  <v-expansion-panel>
                    <v-expansion-panel-title>
                      <v-icon icon="mdi-fire" class="mr-2"></v-icon>
                      {{ t('chatbot.refine') }}
                    </v-expansion-panel-title>
                    <v-expansion-panel-text>
                      <div class="markdown-content" v-html="marked(message.refine)"></div>
                    </v-expansion-panel-text>
                  </v-expansion-panel>
                </v-expansion-panels>
              </div>
              <div v-if="message.sender === 'bot' && message.rag" class="mb-2">
                <v-expansion-panels class="secondary text-on-secondary">
                  <v-expansion-panel>
                    <v-expansion-panel-title>
                      <v-icon icon="mdi-thought-bubble" class="mr-2"></v-icon>
                      {{ t('chatbot.knowledgeBase') }}
                    </v-expansion-panel-title>
                    <v-expansion-panel-text>
                      <div class="markdown-content" v-html="marked(message.rag)"></div>
                    </v-expansion-panel-text>
                  </v-expansion-panel>
                </v-expansion-panels>
              </div>
              <div v-if="message.sender === 'bot' && message.tools && message.tools.length > 0" class="mb-2">
                <v-alert color="secondary" type="info" dense text variant="elevated" icon="mdi-toolbox-outline">
                  <strong>{{ t('chatbot.callTool') }}: </strong>
                  <ul style="margin: 0; padding-left: 1.2em;">
                    <li v-for="tool in message.tools" :key="tool">{{ tool }}</li>
                  </ul>
                </v-alert>
              </div>
              <div class="text-body-1 markdown-content" v-html="marked(message.message)"></div>
              <div v-if="message.sender === 'bot' && message.mermaid" class="text-blue-grey-lighten-1 mb-2">
                <v-alert color="secondary-variant" type="success" dense text variant="elevated" icon="mdi-drawing-box">
                  <strong>{{ t('chatbot.diagram') }}: </strong>
                  <div class="d-flex flex-column">
                    <MermaidDiagram :diagram-code="message.mermaid" />
                  </div>
                </v-alert>
              </div>
              <div v-if="message.sender === 'bot' && message.refs & message.refs.length > 0" class="mb-2">
                <v-expansion-panels class="secondary text-on-secondary">
                  <v-expansion-panel>
                    <v-expansion-panel-title>
                      <v-icon icon="mdi-eye-check" class="mr-2"></v-icon>
                      {{ t('chatbot.reference') }}
                    </v-expansion-panel-title>
                    <v-expansion-panel-text>
                      <div class="markdown-content" v-html="marked(message.refs.join('/n'))"></div>
                    </v-expansion-panel-text>
                  </v-expansion-panel>
                </v-expansion-panels>
              </div>
              <div class="text-caption text-right mt-1 opacity-medium">
                <v-btn
                  icon size="small"
                  color="primary"
                  variant="text"
                  @click="handleCopy(message.message)"
                  :title="t('chatbot.copyMessage')"
                >
                  <v-icon>mdi-content-copy</v-icon>
                </v-btn>
                <v-btn v-if="message.sender === 'bot'" 
                  icon size="small" 
                  :color="message.feedback === 'good'? 'error' : 'primary'"
                  variant="text" 
                  :title="t('chatbot.goodResponse')"
                  @click="updateFeedback(message.id, message.feedback === 'good'? 'neutral': 'good')"
                >
                  <v-icon>mdi-thumb-up-outline</v-icon>
                </v-btn>
                <v-btn v-if="message.sender === 'bot'" 
                  icon size="small" 
                  :color="message.feedback === 'bad'? 'error' : 'primary'"
                  variant="text" 
                  :title="t('chatbot.badResponse')"
                  @click="updateFeedback(message.id, message.feedback === 'bad'? 'neutral': 'bad')"
                >
                  <v-icon>mdi-thumb-down-outline</v-icon>
                </v-btn>
                <v-btn v-if="message.sender === 'bot'" 
                  icon size="small" 
                  :color="isPlaying ? 'error' : 'primary'" 
                  variant="text" 
                  @click="handleSpeak(message.message)"
                  :title="t('chatbot.speakMessage')"
                >
                  <v-icon>{{ isPlaying ? 'mdi-stop-circle' : 'mdi-volume-high' }}</v-icon>
                </v-btn>
                <v-btn v-if="message.sender === 'user'" 
                  icon size="small" 
                  color="primary" 
                  variant="text" 
                  @click="handleResend(message.message)"
                  :title="t('chatbot.resend')"
                >
                  <v-icon>mdi-send-circle-outline</v-icon>
                </v-btn>
                {{ message.timestamp }}
              </div>
            </v-card>
            <div v-if="message.sender === 'bot' && message.followups && message.followups.length > 0" 
              class="mt-2"
              style="display: flex; justify-content: flex-end;"
              >
              <v-list bg-color="background">
                <v-list-item v-for="followup in message.followups" :key="followup" class="text-right">
                  <v-card class="pa-2 wrap-text hover-card" rounded="xl" color="primary" variant="outlined" @click="handleResend(followup)">
                    {{ followup }}
                  </v-card>
                </v-list-item>
              </v-list>
            </div>
          </div>
        </div>
      </div>
</template>

<script setup>
import MermaidDiagram from '@/components/MermaidDiagram.vue';
import { getPhoto } from '@/utils/photo'

defineProps({
  visibleMessages: Array,
  userStore: Object,
  MoxartIcon: String,
  isPlaying: Boolean,
  t: Function,
  marked: Function,
  handleSpeak: Function,
  handleCopy: Function,
  handleResend: Function,
  updateFeedback: Function,
});
</script>

<style scoped>
/* 為手機模式添加樣式 */
@media (max-width: 960px) {
  .message-card {
    max-width: 85vw !important;
  }
  
  .markdown-content pre {
    max-width: 80vw;
    overflow-x: auto;
  }
}

.markdown-content :deep(p) {
  margin: 0.5em 0;
}

.markdown-content :deep(h1),
.markdown-content :deep(h2),
.markdown-content :deep(h3),
.markdown-content :deep(h4),
.markdown-content :deep(h5),
.markdown-content :deep(h6) {
  margin: 0.5em 0;
  font-weight: bold;
}

.markdown-content :deep(ul),
.markdown-content :deep(ol) {
  margin: 0.5em 0;
  padding-left: 1.5em;
}

.markdown-content :deep(code) {
  background-color: rgba(0, 0, 0, 0.05);
  padding: 0.2em 0.4em;
  border-radius: 3px;
  font-family: monospace;
}

.markdown-content :deep(pre code) {
  display: block;
  padding: 1em;
  overflow-x: auto;
  background-color: rgba(0, 0, 0, 0.05);
}

.markdown-content :deep(a) {
  color: rgb(var(--v-theme-on-surface));
  text-decoration: underline;
  font-style: italic;
}

.markdown-content :deep(a:hover) {
  text-decoration: underline;
}

.markdown-content :deep(blockquote) {
  margin: 0.5em 0;
  padding-left: 1em;
  border-left: 4px solid rgba(0, 0, 0, 0.1);
  color: rgba(0, 0, 0, 0.7);
}

.message-card {
  position: relative;
  transition: all 0.3s ease;
}
</style>
