<script setup>
import { ref } from 'vue'
import { useI18n } from 'vue-i18n';

const { t, locale } = useI18n()

const salesKPI = ref({
  target: 100,
  achieved: 75,
  ratio: 75
})

</script>

<template>
  <v-card height="100%" class="sales-kpi-card">
    <v-card-title class="d-flex align-center">
      <v-icon icon="mdi-chart-line" color="primary" class="mr-2"></v-icon>
      {{ t('salesKPICard.title') }}
    </v-card-title>
    <v-card-text>
      <div class="d-flex justify-space-between align-center mb-4">
        <div>
          <div class="text-subtitle-2">{{ t('salesKPICard.target') }}</div>
          <div class="text-h5 font-weight-bold primary--text">${{ salesKPI.target }}M</div>
        </div>
        <div class="text-center">
          <div class="text-subtitle-2">{{ t('salesKPICard.actual') }}</div>
          <div class="text-h5 font-weight-bold success--text">${{ salesKPI.achieved }}M</div>
        </div>
        <div class="text-right">
          <div class="text-subtitle-2">{{ t('salesKPICard.percentage') }}</div>
          <div class="text-h5 font-weight-bold" :class="salesKPI.ratio >= 80 ? 'success--text' : 'error--text'">
            {{ salesKPI.ratio }}%
          </div>
        </div>
      </div>
      <v-progress-linear
        :model-value="(salesKPI.achieved / salesKPI.target) * 100"
        color="success"
        height="10"
        rounded
      ></v-progress-linear>
    </v-card-text>
  </v-card>
</template>