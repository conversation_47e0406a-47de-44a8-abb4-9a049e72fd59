<script setup>
import { ref } from 'vue'
import { useI18n } from 'vue-i18n'

const { t, locale } = useI18n()

const links = ref([
  { title: 'Salesforce', icon: 'mdi-account-group', color: 'blue', url: 'https://moxa.my.salesforce.com/' },
  { title: 'Partner Zone', icon: 'mdi-leaf', color: 'green', url: 'http://esource.moxa.com/' },
  { title: 'CV Portal', icon: 'mdi-cash', color: 'green-darken-2', url: 'http://cvportal.moxa.com/' },
  { title: 'Moxa Website (EN)', icon: 'mdi-gavel', color: 'deep-purple', url: 'https://www.moxa.com/en' },
  { title: 'Brand Center', icon: 'mdi-domain', color: 'blue-grey', url: 'https://moxa.sharepoint.com/sites/TheMoxaBrand/SitePages/Business-Templates.aspx' }
])

</script>

<template>
  <v-card height="100%" class="useful-links-card">
    <v-card-title class="d-flex align-center">
      <v-icon icon="mdi-link" color="primary" class="mr-2"></v-icon>
      {{ t('usefulApps.sales') }}
    </v-card-title>
    <v-card-text>
      <div class="d-flex flex-column align-start">
        <v-btn
          v-for="link in links"
          :key="link.title"
          :color="link.color"
          :prepend-icon="link.icon"
          class="ma-1 w-100"
          variant="tonal"
          :href="link.url"
          target="_blank"
        >
          {{ link.title }}
        </v-btn>
      </div>
    </v-card-text>
  </v-card>
</template>