<template>
    <v-menu>
      <template v-slot:activator="{ props }">
        <v-btn icon v-bind="props">
          <v-icon>mdi-palette</v-icon>
        </v-btn>
      </template>
  
      <v-list>
          <v-list-item
          v-for="(themeItem, i) in themes"
          :key="i"
          @click="setTheme(themeItem.value)"
          :style="{ backgroundColor: getThemeColor(themeItem.value, 'primary') }"
        >
          <v-list-item-title
            :style="{ color: getContrastColor(getThemeColor(themeItem.value, 'on-surface')) }"
            :class="{ 'font-weight-bold': themeItem.value === currentThemeName, 'font-weight-thin': themeItem.value != currentThemeName }"
          >
            {{ themeItem.title }}
          </v-list-item-title>
        </v-list-item>
    </v-list>
    </v-menu>
  </template>
  
  <script setup>
  import { useTheme } from 'vuetify'
  import { useUserStore } from '@/stores/userStore';
  import { ref, computed } from 'vue';

  const userStore = useUserStore();
  const theme = useTheme()
  const currentThemeName = computed(() => theme.global.name.value);
  
  const formatThemeName = (name) => {
    return name
      .replace(/([A-Z])/g, ' $1') // Add space before capital letters
      .replace(/-/g, ' ') // Replace hyphens with spaces
      .split(' ')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1)) // Capitalize each word
      .join(' ');
  };

  const themes = computed(() => {
    return Object.keys(theme.themes.value)
      .filter(themeName => themeName !== 'light' && themeName !== 'dark') // Exclude 'light' and 'dark' themes
      .map(themeName => ({
        title: formatThemeName(themeName),
        value: themeName
      }));
  });  
  
  const setTheme = (themeName) => {
    if (themeName === currentThemeName) return; // Do nothing if already set
    theme.global.name.value = themeName
    userStore.updateTheme(themeName);
  };

  const getThemeColor = (themeName, color) => {
    // Access the theme definition directly from Vuetify's theme service
    const themeDefinition = theme.themes.value[themeName];
    return themeDefinition.colors[color]
  };

  const getThemePrimaryColor = (themeName) => {
    // Access the theme definition directly from Vuetify's theme service
    const themeDefinition = theme.themes.value[themeName];
    return themeDefinition ? themeDefinition.colors.primary : '#FFFFFF'; // Default to white if not found
  };
  const getContrastColor = (hexcolor) => {
    if (!hexcolor) return '#000000'; // Default to black if no color

    // Remove # if present
    const r = parseInt(hexcolor.substr(1, 2), 16);
    const g = parseInt(hexcolor.substr(3, 2), 16);
    const b = parseInt(hexcolor.substr(5, 2), 16);

    // Calculate luminance
    const y = (r * 299 + g * 587 + b * 114) / 1000;

    // Return black for light colors, white for dark colors
    return (y >= 128) ? '#000000' : '#FFFFFF';
    };
  </script>
  
  <style scoped>
  .v-list-item--active {
  /* You can add additional styles here if the background color isn't enough */
    font-weight: bold;
  }
  /* Add any component-specific styles here if needed */
  </style>