from annotated_types import LowerCase
import httpx
import asyncio
import base64
import json
from typing import Dict, List
import os
from app.storage.user_storage import UserStorage
from app.utils import auth
from app.utils.logger import ColoredLogger

logger = ColoredLogger("app.services.user_service")

GRAPH_API_BASE = os.getenv("GRAPH_API_BASE", "https://graph.microsoft.com/v1.0")
DIP_HR_BASE = os.getenv("DIP_HR_BASE", "https://bpm.mgwp.moxa.com/v1")


class UserService:
    def __init__(self):
        self.user_storage = UserStorage()
        logger.info("User Service Initialized")

    async def _fetch_graph_data_with_retry(self, api_call_coroutine, user_id_for_logging: str, call_description: str, max_retries: int = 3):
        """Helper to fetch data from Graph API with token refresh and retry logic."""
        current_token = await auth.get_graph_token() # Get initial/current token
        for attempt in range(max_retries):
            try:
                return await api_call_coroutine(current_token)
            except httpx.HTTPStatusError as e:
                if e.response.status_code == 401 and attempt < max_retries - 1:
                    logger.warning(f"{call_description}: Token expired for user {user_id_for_logging}. Attempt {attempt + 1}. Refreshing token.")
                    current_token = await auth.refresh_graph_token() # Refresh token
                    # Update the token for subsequent calls within this scope if needed, 
                    # or ensure get_graph_token() provides the refreshed one next time.
                else:
                    logger.error(f"{call_description}: HTTP error for user {user_id_for_logging} after {attempt + 1} attempts: {str(e)}")
                    # raise # Re-raise the exception if it's not a 401 or max retries reached
                    return None
            except Exception as e:
                logger.error(f"{call_description}: Non-HTTP error for user {user_id_for_logging} on attempt {attempt + 1}: {str(e)}")
                # raise # Re-raise other exceptions
                return None
        logger.error(f"{call_description}: Failed for user {user_id_for_logging} after {max_retries} attempts.")
        return None # Or raise an exception

    async def make_request(self, url, token):
        headers = {"Authorization": f"Bearer {token}", "ConsistencyLevel": "eventual"}
        async with httpx.AsyncClient() as client:
            resp = await client.get(url, headers=headers)
            resp.raise_for_status()
            return resp

    async def sync_positions(self, api_key: str) -> int:
        url = f"{DIP_HR_BASE}/Positions?positionStatus=A&limit=10000&offset=0"
        orgchart = []
        count = 0
        headers = {
            "x-api-key": f"{api_key}",
            "Content-Type": "application/json",
        }

        async with httpx.AsyncClient() as client:
            try:
                resp = await client.get(url, headers=headers)
                resp.raise_for_status()
                data = resp.json()
                count = data.get("count", 0)
                value = data.get("value")
                if value:
                    for position in value:
                        self.user_storage.set_position_data(position.get("id"), position)

                        upn = (position.get("activeDirectoryAccount") + "@moxa.com").lower()
                        employee_data = self.user_storage.get_employee_data(upn)
                        user_id = self.user_storage.get_aad_id_by_upn(upn)
                        profile = self.user_storage.get_user_profile(user_id)
                        orgchart_pos = {
                            "id": position.get("id"),
                            "userId": user_id,
                            "name": profile.get("displayName") if profile else position.get("activeDirectoryAccount").replace("_", " "),
                            "mail": profile.get("mail") if profile else position.get("activeDirectoryAccount") + "@moxa.com",
                            "photo": profile.get("photo") if profile else '',
                            "positionName": position.get("name"),
                            "positionChineseName": position.get("chineseName"),
                            "employeeId": position.get("employeeId"),
                            "departmentName": self.user_storage.get_department_data(position.get("departmentId")).get("name"),
                            "departmentChineseName": self.user_storage.get_department_data(position.get("departmentId")).get("chineseName"),
                            "parentId": position.get("reportToPositionId").strip(),
                            "location": employee_data.get("locationName") if employee_data else '',
                        }
                        orgchart.append(orgchart_pos)

                        upn = (position.get("activeDirectoryAccount") + "@moxa.com").lower()
                        employee_data = self.user_storage.get_employee_data(upn)
                        if employee_data:
                            all_positions = employee_data.get("allPositions")
                            if all_positions:
                                if position.get("id") not in all_positions:
                                    all_positions.append(position.get("id"))
                                    employee_data["allPositions"] = all_positions
                            else:
                                employee_data["allPositions"] = [position.get("id")]

                            # Update employee_data
                            self.user_storage.set_employee_data(upn, employee_data)

                    # Ensure root node is first
                    orgchart.sort(key=lambda x: x["parentId"] != "")
                    self.user_storage.set_orgchart(orgchart)
            except Exception as e:
                logger.error(f"Failed to fetch positions: {str(e)}")
                raise

        logger.info(f"Fetched {count} positions")
        return count

    async def sync_departments(self, api_key: str) -> int:
        url = f"{DIP_HR_BASE}/Departments?effectiveStatus=A"
        count = 0
        headers = {
            "x-api-key": f"{api_key}",
            "Content-Type": "application/json",
        }

        async with httpx.AsyncClient() as client:
            try:
                resp = await client.get(url, headers=headers)
                resp.raise_for_status()
                data = resp.json()
                count = data.get("count", 0)
                value = data.get("value")
                if value:
                    for department in value:
                        self.user_storage.set_department_data(department.get("id"), department)
            except httpx.HTTPStatusError as e:
                logger.error(f"Failed to fetch departments: {str(e)}")
                raise

        logger.info(f"Fetched {count} departments")
        return count

    async def sync_employees(self, api_key: str) -> int:
        employee_upns = []
        url = f"{DIP_HR_BASE}/Employees?employeeStatus=A&limit=10000&offset=0"

        headers = {
            "x-api-key": f"{api_key}",
            "Content-Type": "application/json",
        }

        async with httpx.AsyncClient() as client:
            try:
                resp = await client.get(url, headers=headers)
                resp.raise_for_status()
                data = resp.json()
                value = data.get("value")
                if value:
                    for employee_data in value:
                        upn = f"{employee_data.get("activeDirectoryAccount")}@moxa.com".lower()
                        self.user_storage.set_employee_data(upn, employee_data)
                        employee_upns.append(upn)
                    self.user_storage.update_employees(employee_upns)
            except httpx.HTTPStatusError as e:
                logger.error(f"Failed to fetch employees: {str(e)}")
                raise

        logger.info(f"Fetched {len(employee_upns)} employees")
        return len(employee_upns)


    # GET /users?$select=id&$filter=userType eq 'Member'
    # {
    #     "@odata.context": "    # {
    #     "@odata.context": "URL_ADDRESS.microsoft.com/v1.0/$metadata#users",
    #     "value": [
    #         {
    #             "id": "48d31887-5fad-4d73-a9f5-3c356e68a038"
    #         },
    #         {
    #             "id": "85b11202-5e89-4c61-9254-30d3589699c7"
    #         }
    #     ]
    # }
    async def get_oids(self, access_token: str) -> list:
        users = []
        url = f"{GRAPH_API_BASE}/users?$select=id&$filter=userType eq 'Member'"
        # url = f"{GRAPH_API_BASE}/users?$select=id,userPrincipalName,displayName&$filter=endswith(userprincipalname, '@moxa.com')&$count=true"

        headers = {
            "Authorization": f"Bearer {access_token}",
            "ConsistencyLevel": "eventual",  # Required for $count and some filters
        }

        async with httpx.AsyncClient() as client:
            while url:
                resp = await client.get(url, headers=headers)
                resp.raise_for_status()
                data = resp.json()
                value = data.get("value")
                if value:
                    users.extend([user["id"] for user in value])
                    await asyncio.sleep(1)  # Avoid rate limiting
                url = data.get("@odata.nextLink")  # Use this for pagination

        logger.info(f"Fetched {len(users)} users")
        return users

    async def sync_oids(self, access_token: str):
        oids = set(await self.get_oids(access_token))
        self.user_storage.set_oids(oids)
        return len(oids)

    # GET /users/{user-id}
    # {
    #     "@odata.context": "https://graph.microsoft.com/v1.0/$metadata#users/$entity",
    #     "businessPhones": [
    #         "****** 555 0109"
    #     ],
    #     "displayName": "Megan Bowen",
    #     "givenName": "Megan",
    #     "jobTitle": "Auditor",
    #     "mail": "<EMAIL>",
    #     "mobilePhone": null,
    #     "officeLocation": "12/1110",
    #     "preferredLanguage": "en-US",
    #     "surname": "Bowen",
    #     "userPrincipalName": "<EMAIL>",
    #     "id": "48d31887-5fad-4d73-a9f5-3c356e68a038"
    # }
    async def get_user(self, user_id: str, access_token: str) -> dict:
        url = f"{GRAPH_API_BASE}/users/{user_id}"
        async with httpx.AsyncClient() as client:
            resp = await client.get(
                url, headers={"Authorization": f"Bearer {access_token}"}
            )
            resp.raise_for_status()
            return resp.json()

    # GET /users/{user-id}?$select=companyName,department,country,userType,usageLocation
    # {
    #     "@odata.context": "
    # {
    #     "@odata.context": "URL_ADDRESS.microsoft.com/v1.0/$metadata#users/$entity",
    #     "companyName": "Moxa",
    #     "department": "Engineering",
    #     "country": "United States",
    #     "userType": "Member",
    #     "usageLocation": "US"
    # }
    async def get_user_additional_info(self, user_id: str, access_token: str) -> dict:
        url = f"{GRAPH_API_BASE}/users/{user_id}?$select=companyName,department,country,userType,usageLocation"
        async with httpx.AsyncClient() as client:
            resp = await client.get(
                url, headers={"Authorization": f"Bearer {access_token}"}
            )
            resp.raise_for_status()
            return resp.json()

    # GET /users/{user-id}/photo/$value
    async def get_user_photo_base64(
        self, user_id: str, access_token: str
    ) -> str | None:
        url = f"{GRAPH_API_BASE}/users/{user_id}/photo/$value"
        async with httpx.AsyncClient() as client:
            resp = await client.get(
                url, headers={"Authorization": f"Bearer {access_token}"}
            )
            if resp.status_code == 200:
                return base64.b64encode(resp.content).decode()
            return None

    # GET /users/{user-id}/manager?$select=id
    async def get_user_manager(self, user_id: str, access_token: str) -> dict | None:
        url = f"{GRAPH_API_BASE}/users/{user_id}/manager?$select=id"
        async with httpx.AsyncClient() as client:
            resp = await client.get(
                url, headers={"Authorization": f"Bearer {access_token}"}
            )
            if resp.status_code == 200:
                data = resp.json()
                if data:
                    return data.get("id")
            return None

    async def get_user_profile(
        self, user_id: str, access_token: str, use_cache: bool = True
    ) -> dict:
        logging.info(
            f"UserService:get_user_profile: Fetching profile for user {user_id}"
        )
        # Try to get from Redis cache first
        cached_profile = self.user_storage.get_user_profile(user_id)
        logging.info(f"UserService:get_user_profile: Cached profile: {cached_profile}")
        if cached_profile and use_cache:
            return cached_profile

        url = f"{GRAPH_API_BASE}/users/{user_id}"
        async with httpx.AsyncClient() as client:
            resp = await client.get(
                url, headers={"Authorization": f"Bearer {access_token}"}
            )
            resp.raise_for_status()
            profile = resp.json()
            logging.info(profile)
            if profile:
                additional_info = await self.get_user_additional_info(
                    user_id, access_token
                )
                profile["companyName"] = additional_info.get("companyName")
                profile["department"] = additional_info.get("department")
                profile["countryCode"] = additional_info.get("usageLocation")
                profile["managerId"] = await self.get_user_manager(
                    user_id, access_token
                )
                profile["photo"] = await self.get_user_photo_base64(
                    user_id, access_token
                )

                # Store in Redis cache for future requests
                self.user_storage.set_user_profile(user_id, profile)
            return profile

    async def sync_users(self, access_token: str, use_cache: bool = True) -> int:
        users = []
        url = f"{GRAPH_API_BASE}/users?$filter=userType eq 'Member'"

        retry_count = 0
        max_retries = 3

        async def get_new_token():
            # Implement your token refresh logic here
            # This could involve calling your authentication service
            # or using a refresh token to get a new access token
            new_token = await auth.refresh_graph_token()
            return new_token

        current_token = access_token
        while url:
            try:
                resp = await self.make_request(url, current_token)
                resp.raise_for_status()
                retry_count = 0  # Reset retry count on successful request
                
                data = resp.json()
                profiles = data.get("value")
                if profiles:
                    for profile in profiles:
                        account = profile.get("userPrincipalName").lower()
                        # Valid employee
                        if self.user_storage.is_employee(account):
                            self.user_storage.set_aad_upn_to_id(account, profile['id'])

                            if self.user_storage.get_user_profile(profile['id']) and use_cache:
                                continue  # Skip if profile already exist
        
                            try:
                                additional_info = await self.get_user_additional_info(profile['id'], current_token)
                                profile["companyName"] = additional_info.get("companyName")
                                profile["department"] = additional_info.get("department")
                                profile["countryCode"] = additional_info.get("usageLocation")
                            except httpx.HTTPStatusError as e:
                                if e.response.status_code == 401 and retry_count < max_retries:
                                    retry_count += 1
                                    current_token = await get_new_token()
                                    continue
                                raise
                            except Exception as e:
                                logger.error(f"Failed to fetch additional info for user {profile['id']}: {str(e)}")
                                profile["companyName"] = None
                                profile["department"] = None
                                profile["countryCode"] = None
                            try:
                                profile['managerId'] = await self.get_user_manager(profile['id'], current_token)
                            except httpx.HTTPStatusError as e:
                                if e.response.status_code == 401 and retry_count < max_retries:
                                    retry_count += 1
                                    current_token = await get_new_token()
                                    continue
                                raise
                            except Exception as e:
                                logger.error(f"Failed to fetch manager for user {profile['id']}: {str(e)}")
                                profile['managerId'] = None
                            try:
                                profile['photo'] = await self.get_user_photo_base64(profile['id'], current_token)
                            except httpx.HTTPStatusError as e:
                                if e.response.status_code == 401 and retry_count < max_retries:
                                    retry_count += 1
                                    current_token = await get_new_token()
                                    continue
                                raise
                            except Exception as e:
                                logger.error(f"Failed to fetch photo for user {profile['id']}: {str(e)}")
                                profile['photo'] = None
                            # Store in Redis cache for future requests
                            self.user_storage.set_user_profile(profile['id'], profile)
                            if profile['id'] not in users:
                                users.append(profile['id'])
                                logger.info(f"In progress fetching {len(users)} users")                        # Store in Redis cache for future requests
                        else:
                            self.user_storage.remove_aad_upn_to_id(account)
                            self.user_storage.remove_user_profile(profile['id'])
                        await asyncio.sleep(1)  # Avoid rate limiting

                url = data.get("@odata.nextLink")  # Use this for pagination

            except httpx.HTTPStatusError as e:
                if e.response.status_code == 401 and retry_count < max_retries:
                    retry_count += 1
                    current_token = await get_new_token()
                    continue
                raise
            except Exception as e:
                logger.error(f"Failed to fetch users: {str(e)}")
                raise

        # Update Redis cache with final list of users
        self.user_storage.set_oids(users)
        logger.info(f"Finished fetching {len(users)} users")
        return len(users)

    async def sync_users_by_upn(self, access_token: str, use_cache: bool = True) -> int:
        users_synced_ids = []
        # Initial token, though _fetch_graph_data_with_retry will manage its own
        # current_token = access_token 

        hr_accounts = self.user_storage.get_hr_accounts()
        if not hr_accounts:
            logger.error(f"No HR accounts found. Ensure 'hr:accounts' is populated.")
            return 0

        logger.info(f"Starting sync for {len(hr_accounts)} HR accounts.")

        for i, account_upn in enumerate(hr_accounts):
            logger.info(f"Processing account {i+1}/{len(hr_accounts)}: {account_upn}")
            # Get user profile by UPN from Graph API
            # This initial call also needs retry for token expiration
            main_profile_url = f"{GRAPH_API_BASE}/users('{account_upn}')"
            
            resp = await self._fetch_graph_data_with_retry(
                lambda token: (self.make_request(main_profile_url, token)),
                account_upn, 
                "fetch_main_profile"
            )

            if not resp:
                logger.warning(f"Could not fetch main profile for UPN {account_upn}. Skipping.")
                self.user_storage.remove_aad_upn_to_id(account_upn)
                # If we had an OID previously, we might want to remove that profile too, but we don't have OID yet.
                continue

            try:
                profile = resp.json()
            except Exception as e:
                logger.error(f"Failed to parse JSON for UPN {account_upn}: {str(e)}")
                continue

            user_oid = profile.get('id')
            if not user_oid:
                logger.warning(f"No OID found in profile for UPN {account_upn}. Skipping.")
                continue

            self.user_storage.set_aad_upn_to_id(account_upn, user_oid)

            if use_cache and self.user_storage.get_user_profile(user_oid):
                logger.info(f"Profile for OID {user_oid} (UPN {account_upn}) already cached and use_cache is True. Skipping Graph API calls for details.")
                if user_oid not in users_synced_ids:
                    users_synced_ids.append(user_oid)
                continue
            
            # Fetch additional details using the helper for retries
            additional_info = await self._fetch_graph_data_with_retry(
                lambda token: self.get_user_additional_info(user_oid, token),
                user_oid, "fetch_additional_info"
            )
            profile["companyName"] = additional_info.get("companyName") if additional_info else None
            profile["department"] = additional_info.get("department") if additional_info else None
            profile["countryCode"] = additional_info.get("usageLocation") if additional_info else None

            manager_id = await self._fetch_graph_data_with_retry(
                lambda token: self.get_user_manager(user_oid, token), # Assuming get_user_manager returns manager_id directly
                user_oid, "fetch_manager"
            )
            profile['managerId'] = manager_id

            photo_base64 = await self._fetch_graph_data_with_retry(
                lambda token: self.get_user_photo_base64(user_oid, token),
                user_oid, "fetch_photo"
            )
            profile['photo'] = photo_base64
            
            self.user_storage.set_user_profile(user_oid, profile)
            if user_oid not in users_synced_ids:
                users_synced_ids.append(user_oid)
            
            logger.info(f"Successfully processed and cached profile for OID {user_oid} (UPN {account_upn}). Fetched {len(users_synced_ids)} unique user profiles so far.")
            await asyncio.sleep(0.5)  # Avoid rate limiting

        self.user_storage.set_oids(users_synced_ids) # Store all successfully synced OIDs
        logger.info(f"Finished. Synced {len(users_synced_ids)} user profiles.")
        return len(users_synced_ids)

    def get_employee_with_profile(self, user_id: str) -> dict | None:
        """Retrieves combined employee HR data, AAD profile and settings by user_id (AAD Object ID)."""
        try:
            return self.user_storage.get_employee_with_profile(user_id)
        except Exception as e:
            logger.error(f"Error retrieving data for user_id {user_id}: {str(e)}")
            # Depending on desired error handling, you might raise e, or return None/empty dict
            return None

    def get_orgchart(self) -> List:
        """Retrieves the complete organization chart hierarchy"""
        orgchart = self.user_storage.get_orgchart()
        orgchart.sort(key=lambda x: x["parentId"] != "")
        return orgchart

    def set_user_settings(self, user_id: str, settings: dict) -> None:
        """Sets user settings for a given user_id (AAD Object ID)."""
        self.user_storage.set_user_settings(user_id, settings)

    def get_user_settings(self, user_id: str) -> dict:
        """Retrieves user settings for a given user_id (AAD Object ID)."""
        return self.user_storage.get_user_settings(user_id)
    
    def set_user_preferences(self, user_id: str, preferences: dict) -> None:
        """Sets user preferences for a given user_id (AAD Object ID)."""
        self.user_storage.set_user_preferences(user_id, preferences)

    def get_user_preferences(self, user_id: str) -> dict:
        """Retrieves user preferences for a given user_id (AAD Object ID)."""
        return self.user_storage.get_user_preferences(user_id)