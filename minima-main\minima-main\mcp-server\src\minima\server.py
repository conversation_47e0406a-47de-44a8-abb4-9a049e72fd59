import logging
import mcp.server.stdio
from typing import Annotated
from mcp.server import Server
from .requestor import request_data
from pydantic import BaseModel, Field
from mcp.server.stdio import stdio_server
from mcp.shared.exceptions import McpError
from mcp.server import NotificationOptions, Server
from mcp.server.models import InitializationOptions
from mcp.types import (
    GetPromptResult,
    Prompt,
    PromptArgument,
    PromptMessage,
    TextContent,
    Tool,
    INVALID_PARAMS,
    INTERNAL_ERROR,
)


# Configure logging to ensure proper character display
# Note: Cannot modify sys.stdout as MCP server needs it for client communication
logging.basicConfig(
    level=logging.DEBUG,
    format="%(asctime)s - %(levelname)s - %(message)s",
    handlers=[
        logging.FileHandler("app.txt", encoding="utf-8"),
        # <PERSON><PERSON>andler automatically uses system default encoding, usually UTF-8 on modern systems
        logging.StreamHandler(),
    ],
)

server = Server("minima")


class Query(BaseModel):
    text: Annotated[str, Field(description="context to find")]


@server.list_tools()
async def list_tools() -> list[Tool]:
    return [
        Tool(
            name="minima-query",
            description="For BPM-related queries, you can help with:\n- Explaining how to use specific BPM features\n- Recommending appropriate forms for different scenarios\n- Troubleshooting common BPM issues\n\n",
            inputSchema=Query.model_json_schema(),
        )
    ]


@server.list_prompts()
async def list_prompts() -> list[Prompt]:
    logging.info("List of prompts")
    return [
        Prompt(
            name="minima-query",
            description="Find a context in a local files",
            arguments=[
                PromptArgument(
                    name="context", description="Context to search", required=True
                )
            ],
        )
    ]


@server.call_tool()
async def call_tool(name, arguments: dict) -> list[TextContent]:
    logging.info(f"Tool called: {name}")
    logging.debug(f"Arguments: {arguments}")

    if name != "minima-query":
        logging.error(f"Unknown tool: {name}")
        raise ValueError(f"Unknown tool: {name}")

    logging.info("Calling tools")
    try:
        # Check for either "text" or "context" parameter
        if not arguments:
            logging.error("Arguments are required")
            raise McpError(INVALID_PARAMS, "Arguments are required")

        # Support both parameter names for compatibility
        if "text" in arguments:
            context = arguments["text"]
        elif "context" in arguments:
            context = arguments["context"]
        else:
            logging.error("Either 'text' or 'context' parameter is required")
            raise McpError(
                INVALID_PARAMS, "Either 'text' or 'context' parameter is required"
            )

        # Get text directly from arguments to avoid encoding issues with Query model
        if not arguments or "text" not in arguments:
            logging.error("Missing 'text' parameter in arguments")
            raise McpError(INVALID_PARAMS, "Missing 'text' parameter")

        context = arguments["text"]
        # Ensure context is a string
        if not isinstance(context, str):
            context = str(context)

        logging.info(f"Context: {context}")
        if not context:
            logging.error("Context is required")
            raise McpError(INVALID_PARAMS, "Context is required")

        # Log original query string encoding information
        logging.debug(
            f"Context encoding info - type: {type(context)}, repr: {repr(context)}"
        )

        # Special handling for Chinese characters
        try:
            # Ensure context is UTF-8 encoded
            context_bytes = context.encode("utf-8")
            context = context_bytes.decode("utf-8")
            logging.debug(f"Re-encoded context: {context}")
        except UnicodeError as e:
            logging.error(f"Unicode error when processing context: {e}")
            # If re-encoding fails, try to remove problematic characters
            context = "".join(c for c in context if ord(c) < 128)
            logging.debug(f"Cleaned context: {context}")

        output = await request_data(context)
        if "error" in output:
            logging.error(output["error"])
            raise McpError(INTERNAL_ERROR, output["error"])

        # Ensure output contains expected data
        if "result" not in output or "output" not in output.get("result", {}):
            logging.error(f"Unexpected response format: {output}")
            raise McpError(INTERNAL_ERROR, "Unexpected response format from indexer")

        output_text = output["result"]["output"]
        logging.info(f"Output: {output_text[:100]}...")  # Log only first 100 characters

        return [TextContent(type="text", text=output_text)]
    except ValueError as e:
        logging.error(str(e))
        raise McpError(INVALID_PARAMS, str(e))
    except Exception as e:
        logging.exception(f"Unexpected error in call_tool: {str(e)}")
        raise McpError(INTERNAL_ERROR, f"Unexpected error: {str(e)}")


@server.get_prompt()
async def get_prompt(name: str, arguments: dict | None) -> GetPromptResult:
    # Check for either "context" or "text" parameter
    if not arguments:
        logging.error("Arguments are required")
        raise McpError(INVALID_PARAMS, "Arguments are required")

    # Support both parameter names for compatibility
    if "context" in arguments:
        context = arguments["context"]
    elif "text" in arguments:
        context = arguments["text"]
    else:
        logging.error("Either 'context' or 'text' parameter is required")
        raise McpError(
            INVALID_PARAMS, "Either 'context' or 'text' parameter is required"
        )

    output = await request_data(context)
    if "error" in output:
        error = output["error"]
        logging.error(error)
        return GetPromptResult(
            description=f"Failed to find a {context}",
            messages=[
                PromptMessage(
                    role="user",
                    content=TextContent(type="text", text=error),
                )
            ],
        )

    logging.info(f"Get prompt: {output}")
    output = output["result"]["output"]
    return GetPromptResult(
        description=f"Found content for this {context}",
        messages=[
            PromptMessage(role="user", content=TextContent(type="text", text=output))
        ],
    )


async def main():
    async with mcp.server.stdio.stdio_server() as (read_stream, write_stream):
        await server.run(
            read_stream,
            write_stream,
            InitializationOptions(
                server_name="minima",
                server_version="0.0.1",
                capabilities=server.get_capabilities(
                    notification_options=NotificationOptions(),
                    experimental_capabilities={},
                ),
            ),
        )
