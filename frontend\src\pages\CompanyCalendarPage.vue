<script setup>
import { ref } from 'vue'
import { useI18n } from 'vue-i18n'
import { VCalendar } from 'vuetify/labs/VCalendar'
import { events } from '@/stores/eventStore.js'

const { t, locale } = useI18n()

const calendar = ref()
const selectedDates = ref([new Date()])

</script>

<template>
  <v-container fluid class="pa-6">
    <v-card>
      <v-card-title class="d-flex align-center text-h5">
        <v-icon icon="mdi-calendar" color="primary" class="mr-2" size="large"></v-icon>
        {{ t('companyEvents.title')  }}
      </v-card-title>
      <v-card-text>
        <v-row>
          <v-col cols="12">
            <v-sheet>
              <v-calendar
                ref="calendar"
                v-model="selectedDates"
                :events="events"
                color="primary"
                type="month"
              >
                <template v-slot:event="{ event }">
                  <v-tooltip :text="event.description" location="bottom">
                    <template v-slot:activator="{ props }">
                      <div class="text-truncate font-weight-bold" v-bind="props" :style="{ backgroundColor: event.color, color: 'white', borderRadius: '4px' }">{{ event.title }}</div>
                    </template>
                  </v-tooltip>
                </template>
              </v-calendar>
            </v-sheet>
          </v-col>
        </v-row>
      </v-card-text>
    </v-card>
  </v-container>
</template>
