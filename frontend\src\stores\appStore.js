export const apps = [
	{ 
		'id': 'e-hr', 
		'name': { 'en': 'e-HR',  'zh-TW': 'e-HR', 'zh-CN': 'e-HR'}, 
		'alias': [], 
		'roles': [],
		'business_units': [],
		'url': 'https://moxa.my.salesforce.com/'
	},
	{ 
		'id': 'e-rma', 
		'name': { 'en': 'e-RMA',  'zh-TW': 'e-RMA', 'zh-CN': 'e-RMA'}, 
		'alias': [], 
		'roles': [], 
		'business_units': [],
		'url': 'https://www.moxa.com/en'
	},
	{ 
		'id': 'litmos', 
		'name': { 'en': 'Litmos',  'zh-TW': 'Litmos', 'zh-CN': 'Litmos'}, 
		'alias': [], 
		'roles': ['sales'], 
		'url': 'https://www.moxa.com/en'
	},
	{ 
		'id': 'marketo', 
		'name': { 'en': 'Marketo',  'zh-TW': 'Marketo', 'zh-CN': 'Marketo'}, 
		'alias': [], 
		'roles': ['marketing'], 
		'url': 'https://www.moxa.com/en'
	},
	{ 
		'id': 'partner-zone', 
		'name': { 'en': 'Partner Zone',  'zh-TW': 'Partner Zone', 'zh-CN': 'Partner Zone'},
		'alias': ['esource'], 
		'roles': ['sales', 'marketing'], 
		'url': 'http://esource.moxa.com/'
	},
	{ 
		'id': 'salesforce', 
		'name': { 'en': 'Salesforce',  'zh-TW': 'Salesforce', 'zh-CN': 'Salesforce'},
		'alias': ['CRM', 'SFDC', 'salesforce.com'], 
		'roles': ['sales', 'product_business'], 
		'url': 'https://moxa.my.salesforce.com/'
	},
	{ 
		'id': 'global-website', 
		'name': { 'en': 'Global Website',  'zh-TW': '英文官網', 'zh-CN': '英文官網'},
		'alias': [], 
		'roles': [], 
		'url': 'https://www.moxa.com/en'
	},
	{ 
		'id': 'china-website', 
		'name': { 'en': 'China Website',  'zh-TW': '中國官網', 'zh-CN': '中國官網'}, 
		'alias': [], 
		'roles': [], 
		'business_units': ['MCN'],
		'url': 'https://www.moxa.com.cn/'
	},
	{ 
		'id': 'ka-contract', 
		'name': { 'en': 'KA Contract',  'zh-TW': 'KA Contract', 'zh-CN': 'KA Contract'},
		'alias': [],
		'roles': ['sales'], 
		'business_units': [],
		'url': 'https://www.moxa.com/en'
	},
	{ 
		'id': 'software-activation-portal', 
		'name': { 'en': 'Software Activation Portal',  'zh-TW': 'Software Activation Portal', 'zh-CN': 'Software Activation Portal'}, 
		'alias': [], 
		'roles': ['sales', 'product_business'], 
		'business_units': [],
		'url': 'https://www.moxa.com/en'
	},
	{ 
		'id': 'e-order', 
		'name': { 'en': 'e-Order',  'zh-TW': 'e-Order', 'zh-CN': 'e-Order'}, 
		'alias': [], 
		'roles': ['sales', 'logistics'], 
		'business_units': [],
		'url': 'https://www.moxa.com/en'
	},
	{ 
		'id': 'e-pricing', 
		'name': { 'en': 'e-Pricing',  'zh-TW': 'e-Pricing', 'zh-CN': 'e-Pricing'}, 
		'alias': ['pricing'], 
		'roles': ['sales', 'product_business'], 
		'business_units': [],
		'url': 'https://www.moxa.com/en'
	},
	{ 
		'id': 'sap', 
		'name': { 'en': 'SAP',  'zh-TW': 'SAP', 'zh-CN': 'SAP'}, 
		'alias': [], 
		'roles': ['finance', 'logistics'], 
		'business_units': [],
		'url': 'https://www.moxa.com/en'
	},
	{ 
		'id': 'srm', 
		'name': { 'en': 'SRM',  'zh-TW': 'SRM', 'zh-CN': 'SRM'}, 
		'alias': [], 
		'roles': ['logistics'], 
		'business_units': ['MHQ'],
		'url': 'https://www.moxa.com/en'
	},
	{ 
		'id': 'ufs', 
		'name': { 'en': 'UFS',  'zh-TW': 'UFS', 'zh-CN': 'UFS'}, 
		'alias': [], 
		'roles': ['logistics'], 
		'url': 'https://www.moxa.com/en'
	},
	{ 
		'id': 'wms', 
		'name': { 'en': 'WMS',  'zh-TW': 'WMS', 'zh-CN': 'WMS'}, 
		'alias': [], 
		'roles': ['logistics'], 
		'business_units': ['MHQ'],
		'url': 'https://www.moxa.com/en'
	},
	{ 
		'id': 'mes', 
		'name': { 'en': 'MES',  'zh-TW': 'MES', 'zh-CN': 'MES'}, 
		'alias': [], 
		'roles': ['manufacturing'], 
		'business_units': ['MHQ'],
		'url': 'https://www.moxa.com/en'
	},
	{ 
		'id': 'cv-portal', 
		'name': { 'en': 'CV Portal',  'zh-TW': 'CV Portal', 'zh-CN': 'CV Portal'}, 
		'alias': [], 
		'roles': ['sales', 'product_business'], 
		'business_units': [],
		'url': 'https://www.moxa.com/en'
	},
	{ 
		'id': 'e-invoice', 
		'name': { 'en': 'e-Invoice',  'zh-TW': 'e-Invoice', 'zh-CN': 'e-Invoice'}, 
		'alias': [], 
		'roles': ['finance'], 
		'business_units': ['MHQ'],
		'url': 'https://www.moxa.com/en'
	},
	{ 
		'id': 'gpms', 
		'name': { 'en': 'GPMS',  'zh-TW': 'GPMS', 'zh-CN': 'GPMS'}, 
		'alias': [], 
		'roles': ['quality'], 
		'business_units': ['MHQ'],
		'url': 'https://www.moxa.com/en'
	},
	{ 
		'id': 'intralink', 
		'name': { 'en': 'Intralink',  'zh-TW': 'Intralink', 'zh-CN': 'Intralink'}, 
		'alias': [], 
		'roles': ['product_engineering'], 
		'business_units': ['MHQ'],
		'url': 'https://www.moxa.com/en'
	},
	{ 
		'id': 'obsolescene-ceol', 
		'name': { 'en': 'Obsolescene CEOL',  'zh-TW': 'Obsolescene CEOL', 'zh-CN': 'Obsolescene CEOL'}, 
		'alias': ['ceol'], 
		'roles': ['product_engineering'], 
		'business_units': ['MHQ'],
		'url': 'https://www.moxa.com/en'
	},
	{ 
		'id': 'pdm', 
		'name': { 'en': 'PDM',  'zh-TW': 'PDM', 'zh-CN': 'PDM'}, 
		'alias': [], 
		'roles': ['product_engineering'], 
		'business_units': ['MHQ'],
		'url': 'https://www.moxa.com/en'
	},
	{ 
		'id': 'relex', 
		'name': { 'en': 'Relex',  'zh-TW': 'Relex', 'zh-CN': 'Relex'}, 
		'alias': [], 
		'roles': ['product_engineering'], 
		'business_units': ['MHQ'],
		'url': 'https://www.moxa.com/en'
	},
	{ 
		'id': 'moxa-connect', 
		'name': { 'en': 'Moxa Connect',  'zh-TW': 'Moxa Connect', 
		'zh-CN': 'Moxa Connect'}, 
		'alias': ['新幸福人生'], 
		'roles': [], 
		'business_units': [],
		'url': 'https://www.moxa.com/en'
	},
	{ 
		'id': 'bpm', 
		'name': { 'en': 'BPM',  'zh-TW': 'BPM', 'zh-CN': 'BPM'}, 
		'alias': [], 
		'roles': [], 
		'business_units': [],
		'url': 'https://www.moxa.com/en'
	},
	{ 
		'id': 'cafeteria', 
		'name': { 'en': 'Cafeteria',  'zh-TW': '食堂', 'zh-CN': '食堂'}, 
		'alias': [], 
		'roles': [], 
		'business_units': ['MHQ', 'MAT'],
		'url': 'https://www.moxa.com/en'
	},
	{ 
		'id': 'digital-platform', 
		'name': { 'en': 'Digital Platform',  'zh-TW': '數位工作站', 'zh-CN': '數位工作站'}, 
		'alias': [], 
		'roles': [], 
		'business_units': ['MHQ', 'MAT'],
		'url': 'https://www.moxa.com/en'
	},
	{ 
		'id': 'e-moxa', 
		'name': { 'en': 'e-Moxa',  'zh-TW': 'e-Moxa', 'zh-CN': 'e-Moxa'}, 
		'alias': [], 
		'roles': [], 
		'business_units': [],
		'url': 'https://www.moxa.com/en'
	},
	{ 
		'id': 'jira', 
		'name': { 'en': 'Jira',  'zh-TW': 'Jira', 'zh-CN': 'Jira'}, 
		'alias': [], 
		'roles': [], 
		'business_units': [],
		'url': 'https://www.moxa.com/en'
	},
	{ 
		'id': 'confluence', 
		'name': { 'en': 'Confluence',  'zh-TW': 'Confluence', 'zh-CN': 'Confluence'},
		'alias': [], 
		'roles': [], 
		'business_units': [],
		'url': 'https://www.moxa.com/en'
	},
	{ 
		'id': 'mhq-employee-portal', 
		'name': { 'en': 'MHQ Employee Portal',  'zh-TW': '摩莎小棧', 'zh-CN': '摩莎小棧'},
		'alias': [], 
		'roles': [], 
		'business_units': ['MHQ', 'MAT'],
		'url': 'https://www.moxa.com/en'
	},
	{ 
		'id': 'sharepoint', 
		'name': { 'en': 'SharePoint',  'zh-TW': 'SharePoint', 'zh-CN': 'SharePoint'},
		'alias': [], 
		'roles': [], 
		'business_units': [],
		'url': 'https://www.moxa.com/en'
	},
	{ 
		'id': 'moxa-college', 
		'name': { 'en': 'Moxa College',  'zh-TW': '摩莎學院', 'zh-CN': '摩莎學院'}, 
		'alias': [], 
		'roles': [], 
		'business_units': [],
		'url': 'https://www.moxa.com/en'
	},
	{ 
		'id': 'oa', 
		'name': { 'en': 'OA',  'zh-TW': 'OA', 'zh-CN': 'OA'}, 
		'alias': [], 
		'roles': [], 
		'business_units': [],
		'url': 'https://www.moxa.com/en'
	},
	{ 
		'id': 'apollo', 
		'name': { 'en': 'Apollo',  'zh-TW': '出勤系統', 'zh-CN': 'Apollo'}, 
		'alias': [], 
		'roles': [], 
		'business_units': ['MHQ', 'MAT'],
		'url': 'https://www.moxa.com/en'
	},
	{ 
		'id': 'foundation', 
		'name': { 'en': 'Foundation',  'zh-TW': '基金會', 'zh-CN': '基金會'}, 
		'alias': [], 
		'roles': [], 
		'business_units': ['MHQ', 'MAT'],
		'url': 'https://www.moxa.com/en'
	},
	{ 
		'id': 'moxa-bravo', 
		'name': { 'en': 'Moxa Bravo',  'zh-TW': 'Moxa Bravo', 'zh-CN': 'Moxa Bravo'},
		'alias': [], 
		'roles': [], 
		'business_units': [],
		'url': 'https://www.moxa.com/en'
	},
	{ 
		'id': 'brand-center', 
		'name': { 'en': 'Brand Center',  'zh-TW': 'Brand Center', 'zh-CN': 'Brand Center'},
		'alias': [], 
		'roles': [], 
		'business_units': [],
		'url': 'https://moxa.sharepoint.com/sites/TheMoxaBrand/SitePages/Business-Templates.aspx'
	}
]