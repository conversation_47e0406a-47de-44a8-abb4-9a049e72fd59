export function speak(text, lang = 'en-US', rate = 1, pitch = 1, volume = 1) {
  return new Promise((resolve, reject) => {
      const utterance = new SpeechSynthesisUtterance(text)
      utterance.lang = lang
      utterance.rate = rate
      utterance.pitch = pitch
      utterance.volume = volume

      utterance.onend = () => {
          resolve()
      }

      utterance.onerror = (event) => {
          reject(event)
      }

      window.speechSynthesis.cancel() // Cancel any ongoing speech
      window.speechSynthesis.speak(utterance)
  })
}

export function cancelSpeak() {
  window.speechSynthesis.cancel() // Cancel any ongoing speech
}