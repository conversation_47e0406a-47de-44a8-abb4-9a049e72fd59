<script setup>
import { ref } from 'vue'

const pipelines = ref([
  {
    name: 'Main',
    stages: [
      { name: 'Build', status: 'success', time: '10m ago' },
      { name: 'Test', status: 'success', time: '8m ago' },
      { name: 'Deploy', status: 'running', time: '2m ago' }
    ],
    lastSuccess: '2h ago',
    health: 98
  },
  {
    name: 'Development',
    stages: [
      { name: 'Build', status: 'success', time: '15m ago' },
      { name: 'Test', status: 'failed', time: '12m ago' },
      { name: 'Deploy', status: 'blocked', time: '-' }
    ],
    lastSuccess: '1d ago',
    health: 85
  }
])

const getStatusColor = (status) => {
  const colors = {
    success: 'success',
    failed: 'error',
    running: 'info',
    blocked: 'grey'
  }
  return colors[status] || 'grey'
}

const getStatusIcon = (status) => {
  const icons = {
    success: 'mdi-check-circle',
    failed: 'mdi-alert-circle',
    running: 'mdi-loading mdi-spin',
    blocked: 'mdi-block-helper'
  }
  return icons[status] || 'mdi-help-circle'
}
</script>

<template>
  <v-card class="cicd-status-card">
    <v-card-title class="d-flex align-center">
      <v-icon icon="mdi-pipe" color="primary" class="mr-2"></v-icon>
      CI/CD Status
    </v-card-title>

    <v-card-text>
      <v-sheet v-for="pipeline in pipelines" :key="pipeline.name" class="mb-6">
        <div class="d-flex justify-space-between align-center mb-3">
          <div class="text-h6">{{ pipeline.name }}</div>
          <div class="text-caption">
            Last Success: {{ pipeline.lastSuccess }}
          </div>
        </div>

        <v-row class="mb-2">
          <v-col v-for="stage in pipeline.stages" :key="stage.name" cols="12" sm="4">
            <v-card variant="outlined" :color="getStatusColor(stage.status)" class="pa-2">
              <div class="d-flex align-center">
                <v-icon :icon="getStatusIcon(stage.status)" size="small" class="mr-2"></v-icon>
                <div>
                  <div class="text-subtitle-2">{{ stage.name }}</div>
                  <div class="text-caption">{{ stage.time }}</div>
                </div>
              </div>
            </v-card>
          </v-col>
        </v-row>

        <div class="d-flex align-center">
          <v-progress-linear
            :model-value="pipeline.health"
            :color="pipeline.health > 90 ? 'success' : 'warning'"
            height="8"
            class="flex-grow-1"
          >
            <template v-slot:default="{ value }">
              <div class="text-caption">Health: {{ Math.ceil(value) }}%</div>
            </template>
          </v-progress-linear>
        </div>
      </v-sheet>
    </v-card-text>
  </v-card>
</template>