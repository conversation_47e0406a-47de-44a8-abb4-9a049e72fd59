# Run this:
# python backup_redis.py

import redis
import json
import os
from datetime import datetime
from app.utils.logger import ColoredLogger

logger = ColoredLogger("app.scripts")

# Configuration
REDIS_URL = os.getenv("REDIS_URL", "redis://localhost:6379")
BACKUP_DIR = os.getenv("BACKUP_DIR", "./backups")
# BACKUP_DIR = os.path.join(os.path.dirname(__file__), 'backups') # Store backups in a 'backups' subdirectory


def get_redis_connection():
    """Establishes a connection to Redis."""
    try:
        r = redis.Redis.from_url(REDIS_URL)
        r.ping()
        logger.info(f"Successfully connected to Redis at {REDIS_URL}")
        return r
    except redis.exceptions.ConnectionError as e:
        logger.error(f"Could not connect to Redis at {REDIS_URL}: {e}")
        raise

def backup_redis_data(r: redis.Redis, backup_file_path: str):
    """Dumps all data from Redis to a JSON file."""
    data_to_backup = {}
    keys = r.keys('*')
    total_keys = len(keys)
    logger.info(f"Found {total_keys} keys to backup.")

    for i, key_bytes in enumerate(keys):
        key = key_bytes.decode('utf-8')
        key_type = r.type(key).decode('utf-8')
        
        value = None
        ttl = r.ttl(key)

        if key_type == 'string':
            value = r.get(key).decode('utf-8')
        elif key_type == 'list':
            value = [item.decode('utf-8') for item in r.lrange(key, 0, -1)]
        elif key_type == 'set':
            value = [item.decode('utf-8') for item in r.smembers(key)]
        elif key_type == 'zset':
            value = {member.decode('utf-8'): score for member, score in r.zrange(key, 0, -1, withscores=True)}
        elif key_type == 'hash':
            value = {k.decode('utf-8'): v.decode('utf-8') for k, v in r.hgetall(key).items()}
        else:
            logger.warning(f"Unsupported key type '{key_type}' for key '{key}'. Skipping.")
            continue
        
        data_to_backup[key] = {
            "type": key_type,
            "value": value,
            "ttl": ttl if ttl != -1 else None # Store TTL, -1 means no TTL
        }
        if (i + 1) % 100 == 0 or (i + 1) == total_keys:
            logger.info(f"Processed {i + 1}/{total_keys} keys...")

    try:
        with open(backup_file_path, 'w') as f:
            json.dump(data_to_backup, f, indent=4)
        logger.info(f"Successfully backed up {len(data_to_backup)} keys to {backup_file_path}")
    except IOError as e:
        logger.error(f"Error writing backup file {backup_file_path}: {e}")
        raise

def main():
    if not os.path.exists(BACKUP_DIR):
        os.makedirs(BACKUP_DIR)
        logger.info(f"Created backup directory: {BACKUP_DIR}")

    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_file_name = f"redis_backup_{timestamp}.json"
    backup_file_path = os.path.join(BACKUP_DIR, backup_file_name)

    try:
        r = get_redis_connection()
        backup_redis_data(r, backup_file_path)
    except Exception as e:
        logger.error(f"An error occurred during the backup process: {e}")

if __name__ == "__main__":
    main()
