import asyncio
import json
import re
import os
import sys
from dotenv import load_dotenv
import shutil
from contextlib import AsyncExitStack
from typing import Any, List, Dict, Optional

from mcp import ClientSession, StdioServerParameters
from mcp.client.streamable_http import streamablehttp_client
from mcp.client.stdio import stdio_client

from app.core.mcp_tool import MCPTool
from app.utils.logger import ColoredLogger

logger = ColoredLogger("app.core.mcp_client")
load_dotenv()


def safe_json_loads(json_str: str) -> dict:
    """安全地解析JSON字符串，處理中文字符編碼問題"""
    if not json_str:
        return {}

    try:
        # 首先嘗試直接解析
        return json.loads(json_str)
    except json.JSONDecodeError as e:
        logging.warning(f"JSON解析失敗，嘗試修復: {e}")
        try:
            # 嘗試處理編碼問題
            if isinstance(json_str, str):
                # 確保字符串是UTF-8編碼
                json_bytes = json_str.encode("utf-8", errors="ignore")
                json_str = json_bytes.decode("utf-8")
                return json.loads(json_str)
        except Exception as e2:
            logging.error(f"JSON修復失敗: {e2}")
            return {}


def safe_str_conversion(obj: Any) -> str:
    """安全地將對象轉換為字符串，保持中文字符"""
    if obj is None:
        return ""

    if isinstance(obj, str):
        return obj

    try:
        # 如果對象有content屬性，優先使用
        if hasattr(obj, "content"):
            content = obj.content
            if isinstance(content, list) and content:
                # 處理TextContent列表
                return "\n".join(
                    str(item.text) if hasattr(item, "text") else str(item)
                    for item in content
                )
            return str(content)

        # 否則直接轉換
        result = str(obj)
        # 確保結果是有效的UTF-8字符串
        result.encode("utf-8")
        return result
    except UnicodeEncodeError:
        # 如果有編碼問題，嘗試清理
        return str(obj).encode("utf-8", errors="ignore").decode("utf-8")
    except Exception as e:
        logging.warning(f"對象轉換為字符串時出錯: {e}")
        return str(obj)


class MCPClient:
    """MCPClient manages connections to MCP server."""

    def __init__(self, name: str, config: dict[str, Any]) -> None:
        self.name: str = name
        self.config: dict[str, Any] = config
        self.stdio_context: Any | None = None
        self.session: ClientSession | None = None
        self._cleanup_lock: asyncio.Lock = asyncio.Lock()
        self.exit_stack: AsyncExitStack = AsyncExitStack()
        self.tools: List[MCPTool] = []
        self._initialized = False

    async def initialize(self, timeout: float = 60.0) -> None:
        """Initialize the server connection."""
        logger.info("Starting initialization for %s", self.name)

        server_type = self.config.get("type", "stdio") # Default to stdio if not specified

        try:
            if server_type == "stdio":
                command = (
                    shutil.which("npx")
                    if self.config["command"] == "npx"
                    else self.config["command"]
                )
                if command is None:
                    raise ValueError("The command must be a valid string and cannot be None.")

                server_params = StdioServerParameters(
                    command=command,
                    args=self.config["args"],
                    env={**os.environ, **self.config["env"]}
                    if self.config.get("env")
                    else None,
                )

                stdio_transport = await asyncio.wait_for(
                    self.exit_stack.enter_async_context(stdio_client(server_params)),
                    timeout=timeout/3
                )

                read, write = stdio_transport
                session = await asyncio.wait_for(
                    self.exit_stack.enter_async_context(ClientSession(read, write)),
                    timeout=timeout/3
                )
            elif server_type == "streamableHttp":
                server_url = self.config.get("url")
                if not server_url:
                    raise ValueError("'url' is required for streamableHttp server type.")
                headers = self.config.get("headers", {})

                # Use the existing connect_to_streamable_http_server logic
                # This part needs to be adapted to fit into the initialize flow
                # Instead of calling a separate method, we integrate its core logic
                _streams_context = streamablehttp_client(
                    url=server_url,
                    headers=headers,
                )
                read_stream, write_stream, _ = await self.exit_stack.enter_async_context(_streams_context)

                session = await asyncio.wait_for(
                    self.exit_stack.enter_async_context(ClientSession(read_stream, write_stream)),
                    timeout=timeout/3
                )
            else:
                raise ValueError(f"Unsupported server type: {server_type}")

            await asyncio.wait_for(
                session.initialize(),
                timeout=timeout/3
            )

            self.session = session
            logger.info("Successfully completed initialization for %s", self.name)

            # Fetch and store available tools
            self.tools = await self.list_tools()
            logger.info("Available tools for %s (x %s)", self.name, len(self.tools))

        except asyncio.TimeoutError as e:
            logger.error("Timeout during initialization of %s", self.name)
            await self.cleanup()
            raise TimeoutError(f"Initialization of {self.name} timed out after {timeout} seconds") from e
        except (RuntimeError, ValueError, asyncio.TimeoutError) as e:
            logger.error("Error initializing server %s: %s", self.name, str(e))
            await self.cleanup()
            raise

    async def list_tools(self) -> List[MCPTool]:
        """List available tools from the server.

        Returns:
            A list of available tools.

        Raises:
            RuntimeError: If the server is not initialized.
        """
        if not self.session:
            raise RuntimeError(f"Server {self.name} not initialized before listing tools")
        
        tools_response = await self.session.list_tools()
        tools = []

            for item in tools_response:
                if isinstance(item, tuple) and item[0] == "tools":
                    for tool in item[1]:
                        tools.append(
                            MCPTool(tool.name, tool.description, tool.inputSchema)
                        )

            return tools
        except Exception as e:
            logging.error("MCPClient: Error listing tools for %s: %s", self.name, e)
            # 如果會話已關閉，嘗試重新初始化
            if "closed" in str(e).lower() or "ClosedResourceError" in str(e):
                logging.info("MCPClient: Session closed, attempting to reinitialize...")
                try:
                    await self.cleanup()
                    await self.initialize()
                    tools_response = await self.session.list_tools()
                    tools = []
                    for item in tools_response:
                        if isinstance(item, tuple) and item[0] == "tools":
                            for tool in item[1]:
                                tools.append(
                                    MCPTool(
                                        tool.name, tool.description, tool.inputSchema
                                    )
                                )
                    return tools
                except Exception as reinit_error:
                    logging.error(
                        "MCPClient: Failed to reinitialize and list tools: %s",
                        reinit_error,
                    )
                    # 返回空列表而不是拋出異常
                    return []
            else:
                # 對於其他錯誤，返回空列表
                return []

    async def execute_tool(
        self,
        tool_name: str,
        arguments: dict[str, Any],
        retries: int = 2,
        delay: float = 1.0,
        timeout: float = 30.0,  # 添加超時參數
    ) -> Any:
        """Execute a tool with retry mechanism and timeout."""
        if not self.session:
            raise RuntimeError(f"Server {self.name} not initialized before executing tool")
        
        attempt = 0
        while attempt < retries:
            try:
                logger.info("Executing %s...", tool_name)
                result = await self.session.call_tool(tool_name, arguments)

                return result

            except asyncio.TimeoutError:
                attempt += 1
                logger.warning(
                    "Error executing tool: %s. Attempt %s of %s.",
                    str(e), attempt, retries
                )
                if attempt < retries:
                    logger.info("Retrying in %s seconds...", delay)
                    await asyncio.sleep(delay)
                else:
                    logger.error("Max retries reached. Failing.")
                    raise

    async def cleanup(self) -> None:
        """Clean up server resources."""
        if not self._initialized and not self.session:
            return

        async with self._cleanup_lock:
            try:
                if hasattr(self, "exit_stack") and self.exit_stack:
                    # Give cleanup some time but don't let it hang
                    await asyncio.wait_for(self.exit_stack.aclose(), timeout=10.0)
            except (asyncio.TimeoutError, asyncio.CancelledError, RuntimeError) as e:
                logging.warning(
                    "MCPClient: Cleanup timeout or error for server %s: %s",
                    self.name,
                    str(e),
                )
                # Force cleanup if normal cleanup fails
                try:
                    # Create a new exit stack to avoid the problematic one
                    self.exit_stack = AsyncExitStack()
                except Exception:
                    pass
            except Exception as e:
                logging.error(
                    "MCPClient: Unexpected error during cleanup of server %s: %s",
                    self.name,
                    str(e),
                )
            finally:
                self.session = None
                self.stdio_context = None
            except (RuntimeError, asyncio.CancelledError) as e:
                logger.error("Error during cleanup of server %s: %s", self.name, str(e))

    async def __aenter__(self):
        """Enter the async context manager."""
        await self.initialize()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Exit the async context manager."""
        await self.cleanup()

    def _extract_tool_calls(self, llm_response: str) -> List[Dict[str, Any]]:
        """Extract tool call JSON objects from LLM response."""
        # Try to parse the entire response as JSON
        try:
            tool_call = json.loads(llm_response)
            if (
                isinstance(tool_call, dict)
                and "tool" in tool_call
                and "arguments" in tool_call
            ):
                return [tool_call]
        except json.JSONDecodeError:
            pass

        # Try to extract all JSON objects from the response
        tool_calls = []
        json_pattern = r"({[^{}]*({[^{}]*})*[^{}]*})"
        json_matches = re.finditer(json_pattern, llm_response)

        for match in json_matches:
            try:
                json_obj = json.loads(match.group(0))
                if (
                    isinstance(json_obj, dict)
                    and "tool" in json_obj
                    and "arguments" in json_obj
                ):
                    tool_calls.append(json_obj)
            except json.JSONDecodeError:
                continue

        return tool_calls
