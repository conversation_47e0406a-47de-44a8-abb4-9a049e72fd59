# Run at the backend root directory:
# python -m app.scripts.import_settings_to_redis
import json
import sys
from app.storage.user_storage import UserStorage

settings = [
	{"upn": "DAVIDWH_CHEN", "roles": ["it"], "floorPlan": { "country": "TW", "building": "i-Tower", "floor": "22F", "seat": 1}},
	{"upn": "RICKY_CHANG", "roles": ["it"], "floorPlan": { "country": "TW", "building": "i-Tower", "floor": "22F", "seat": 2}},
	{"upn": "NICOLESR_CHEN", "roles": ["it"], "floorPlan": { "country": "TW", "building": "i-Tower", "floor": "22F", "seat": 66}},
	{"upn": "ERIC_CHEN", "roles": ["it"], "floorPlan": { "country": "TW", "building": "Bade", "floor": "5F", "seat": 100}},
	{"upn": "SHELLY_LEE", "roles": ["it"], "floorPlan": { "country": "TW", "building": "Bade", "floor": "5F", "seat": 101}},
	{"upn": "ROBINSON_CHEN", "roles": ["it"], "floorPlan": { "country": "TW", "building": "i-Tower", "floor": "22F", "seat": 54}},
	{"upn": "RAYMOND_YANG", "roles": ["it"], "floorPlan": { "country": "TW", "building": "i-Tower", "floor": "22F", "seat": 58}},
	{"upn": "KARENWW_YI", "roles": ["it"], "floorPlan": { "country": "TW", "building": "i-Tower", "floor": "22F", "seat": 55}},
	{"upn": "BECKYBE_LAI", "roles": ["it"], "floorPlan": { "country": "TW", "building": "i-Tower", "floor": "22F", "seat": 57}},
	{"upn": "STEVENHX_HU", "roles": ["it"], "floorPlan": { "country": "TW", "building": "i-Tower", "floor": "22F", "seat": 60}},
	{"upn": "WENDYYH_CHEN", "roles": ["it"], "floorPlan": { "country": "TW", "building": "i-Tower", "floor": "22F", "seat": 56}},
	{"upn": "JEFFJF_LUO", "roles": ["it"], "floorPlan": { "country": "TW", "building": "i-Tower", "floor": "22F", "seat": 59}},
	{"upn": "ANDYJC_HUANG", "roles": ["it"], "floorPlan": { "country": "TW", "building": "i-Tower", "floor": "22F", "seat": 12}},
	{"upn": "CATLYNWL_CHANG", "roles": ["it"], "floorPlan": { "country": "TW", "building": "i-Tower", "floor": "22F", "seat": 63}},
	{"upn": "LESLIEPC_CHANG", "roles": ["it"], "floorPlan": { "country": "TW", "building": "i-Tower", "floor": "22F", "seat": 61}},
	{"upn": "DIANAPH_CHEN", "roles": ["it"], "floorPlan": { "country": "TW", "building": "i-Tower", "floor": "22F", "seat": 52}},
	{"upn": "GRAHAM_LIN", "roles": ["it"], "floorPlan": { "country": "TW", "building": "i-Tower", "floor": "22F", "seat": 16}},
	{"upn": "JACKYY_SHIH", "roles": ["it"], "floorPlan": { "country": "TW", "building": "i-Tower", "floor": "22F", "seat": 17}},
	{"upn": "ROXWC_CHUANG", "roles": ["it"], "floorPlan": { "country": "TW", "building": "i-Tower", "floor": "22F", "seat": 18}},
	{"upn": "VINCENT_CHEN", "roles": ["it"], "floorPlan": { "country": "TW", "building": "i-Tower", "floor": "22F", "seat": 28}},
	{"upn": "SAMPH_WU", "roles": ["it"], "floorPlan": { "country": "TW", "building": "i-Tower", "floor": "22F", "seat": 27}},
	{"upn": "CHESTERCS_LIU", "roles": ["it"], "floorPlan": { "country": "TW", "building": "i-Tower", "floor": "22F", "seat": 25}},
	{"upn": "ELININ_YEH", "roles": ["it"], "floorPlan": { "country": "TW", "building": "i-Tower", "floor": "22F", "seat": 26}},
	{"upn": "ZIVSC_CHO", "roles": ["it"], "floorPlan": { "country": "TW", "building": "i-Tower", "floor": "22F", "seat": 23}},
	{"upn": "SEANHH_LIANG", "roles": ["it"], "floorPlan": { "country": "TW", "building": "i-Tower", "floor": "22F", "seat": 78}},
	{"upn": "GAVIN_LAI", "roles": ["it"], "floorPlan": { "country": "TW", "building": "i-Tower", "floor": "22F", "seat": 68}},
	{"upn": "ALLEN_WANG", "roles": ["it"], "floorPlan": { "country": "TW", "building": "i-Tower", "floor": "22F", "seat": 74}},
	{"upn": "WINSTONYJ_WANG", "roles": ["it"], "floorPlan": { "country": "TW", "building": "i-Tower", "floor": "22F", "seat": 71}},
	{"upn": "JEIMMYCM_HUANG", "roles": ["it"], "floorPlan": { "country": "TW", "building": "i-Tower", "floor": "22F", "seat": 69}},
	{"upn": "POLLYHY_CHEN", "roles": ["it"], "floorPlan": { "country": "TW", "building": "i-Tower", "floor": "22F", "seat": 65}},
	{"upn": "DEREKHJ_SUN", "roles": ["it"], "floorPlan": { "country": "TW", "building": "i-Tower", "floor": "22F", "seat": 73}},
	{"upn": "LEONLY_CHANG", "roles": ["it"], "floorPlan": { "country": "TW", "building": "i-Tower", "floor": "22F", "seat": 62}},
	{"upn": "ANNIEAT_CHIEN", "roles": ["it"], "floorPlan": { "country": "TW", "building": "i-Tower", "floor": "22F", "seat": 70}},
	{"upn": "DANNYKJ_CHIU", "roles": ["it"], "floorPlan": { "country": "TW", "building": "i-Tower", "floor": "22F", "seat": 77}},
	{"upn": "DONCT_YANG", "roles": ["it"], "floorPlan": { "country": "TW", "building": "i-Tower", "floor": "22F", "seat": 80}},
	{"upn": "KALENKL_YANG", "roles": ["it"], "floorPlan": { "country": "TW", "building": "i-Tower", "floor": "22F", "seat": 75}},
	{"upn": "ENZOYC_SU", "roles": ["it"], "floorPlan": { "country": "TW", "building": "i-Tower", "floor": "22F", "seat": 72}},
	{"upn": "EMMAHH_HUNG", "roles": ["it"], "floorPlan": { "country": "TW", "building": "i-Tower", "floor": "22F", "seat": 79}},
	{"upn": "RICHARDJC_WU", "roles": ["it"], "floorPlan": { "country": "TW", "building": "i-Tower", "floor": "22F", "seat": 77}},
	{"upn": "SHANIA_LEE", "roles": ["it"], "floorPlan": { "country": "TW", "building": "i-Tower", "floor": "22F", "seat": 30}},
	{"upn": "CLAIREYC_HUANG", "roles": ["it"], "floorPlan": { "country": "TW", "building": "i-Tower", "floor": "22F", "seat": 11}},
	{"upn": "MATTCM_LIN", "roles": ["it"], "floorPlan": { "country": "TW", "building": "i-Tower", "floor": "22F", "seat": 8}},
	{"upn": "ALICESY_PENG", "roles": ["it"], "floorPlan": { "country": "TW", "building": "i-Tower", "floor": "22F", "seat": 6}},
	{"upn": "ZERAYH_HUANG", "roles": ["it"], "floorPlan": { "country": "TW", "building": "i-Tower", "floor": "22F", "seat": 7}},
	{"upn": "VETERWC_HSU", "roles": ["it"], "floorPlan": { "country": "TW", "building": "i-Tower", "floor": "22F", "seat": 5}},
	{"upn": "MISSI_CHEN", "roles": ["it"], "floorPlan": { "country": "TW", "building": "i-Tower", "floor": "22F", "seat": 19}},
	{"upn": "SHEPHERDCW_FAN", "roles": ["it"], "floorPlan": { "country": "TW", "building": "i-Tower", "floor": "22F", "seat": 17}},
	{"upn": "REDYS_HUNG", "roles": ["it"], "floorPlan": { "country": "TW", "building": "i-Tower", "floor": "22F", "seat": 20}},
	{"upn": "ANGELAYC_KUO", "roles": ["it"], "floorPlan": { "country": "TW", "building": "i-Tower", "floor": "22F", "seat": 21}},
	{"upn": "BRUCETN_TING", "roles": ["it"], "floorPlan": { "country": "TW", "building": "i-Tower", "floor": "22F", "seat": 14}},
	{"upn": "WILLWT_LIN", "roles": ["it"], "floorPlan": { "country": "TW", "building": "i-Tower", "floor": "22F", "seat": 15}},
	{"upn": "JIMYS_TSAI", "roles": ["it"], "floorPlan": { "country": "TW", "building": "i-Tower", "floor": "22F", "seat": 13}},
	{"upn": "BLAKECS_HUANG", "roles": ["it"], "floorPlan": { "country": "TW", "building": "i-Tower", "floor": "22F", "seat": 18}},
	{"upn": "ZANDERCC_CHUANG", "roles": ["it"], "floorPlan": { "country": "TW", "building": "i-Tower", "floor": "22F", "seat": 22}},
	{"upn": "TIMST_LIN", "roles": ["it"], "floorPlan": { "country": "TW", "building": "i-Tower", "floor": "22F", "seat": 10}},
	{"upn": "PETERYH_YU", "roles": ["it"], "floorPlan": { "country": "TW", "building": "i-Tower", "floor": "22F", "seat": 4}},
	{"upn": "TOM_WANG", "roles": ["it"], "floorPlan": { "country": "TW", "building": "i-Tower", "floor": "22F", "seat": 180}},
	{"upn": "LIZFH_YEH", "roles": ["it"], "floorPlan": { "country": "TW", "building": "i-Tower", "floor": "22F", "seat": 177}},
	{"upn": "KEVINPW_YANG", "roles": ["it"], "floorPlan": { "country": "TW", "building": "i-Tower", "floor": "22F", "seat": 179}},
	{"upn": "STEWARD_CHANG", "roles": ["it"], "floorPlan": { "country": "TW", "building": "i-Tower", "floor": "22F", "seat": 90}},
	{"upn": "DYLANLY_WUNG", "roles": ["it"], "floorPlan": { "country": "TW", "building": "i-Tower", "floor": "22F", "seat": 86}},
	{"upn": "ALLENC_YANG", "roles": ["it"], "floorPlan": { "country": "TW", "building": "i-Tower", "floor": "22F", "seat": 176}},
	{"upn": "MARKMY_HSIEH", "roles": ["it"], "floorPlan": { "country": "TW", "building": "i-Tower", "floor": "22F", "seat": 181}},
	{"upn": "CILENTC_LIN", "roles": ["it"], "floorPlan": { "country": "TW", "building": "i-Tower", "floor": "22F", "seat": 3}},
	{"upn": "KAREN_CHEN", "roles": ["it"], "floorPlan": { "country": "TW", "building": "i-Tower", "floor": "22F", "seat": 39}},
	{"upn": "GINAYJ_TSAI", "roles": ["it"], "floorPlan": { "country": "TW", "building": "i-Tower", "floor": "22F", "seat": 38}},
	{"upn": "JERRYYT_LAI", "roles": ["it"], "floorPlan": { "country": "TW", "building": "i-Tower", "floor": "22F", "seat": 33}},
	{"upn": "JAMESCW_CHANG", "roles": ["it"], "floorPlan": { "country": "TW", "building": "i-Tower", "floor": "22F", "seat": 34}},
	{"upn": "STEVENYC_CHIU", "roles": ["it"], "floorPlan": { "country": "TW", "building": "i-Tower", "floor": "22F", "seat": 43}},
	{"upn": "TIMCC_WU", "roles": ["it"], "floorPlan": { "country": "TW", "building": "i-Tower", "floor": "22F", "seat": 36}},
	{"upn": "LILLIANIC_WANG", "roles": ["it"], "floorPlan": { "country": "TW", "building": "i-Tower", "floor": "22F", "seat": 40}},
	{"upn": "ANDREWYC_LEE", "roles": ["it"], "floorPlan": { "country": "TW", "building": "i-Tower", "floor": "22F", "seat": 37}},
	{"upn": "DERRICKCY_CHEN", "roles": ["it"], "floorPlan": { "country": "TW", "building": "i-Tower", "floor": "22F", "seat": 47}},
	{"upn": "KUEICHINKC_HUANG", "roles": ["it"], "floorPlan": { "country": "TW", "building": "i-Tower", "floor": "22F", "seat": 46}},
	{"upn": "ANNEAT_HOU", "roles": ["it"], "floorPlan": { "country": "TW", "building": "i-Tower", "floor": "22F", "seat": 45}},
	{"upn": "JENNYYC_LU", "roles": ["it"], "floorPlan": { "country": "TW", "building": "i-Tower", "floor": "22F", "seat": 44}},
	{"upn": "HANSCH_CHEN", "roles": ["it"], "floorPlan": { "country": "TW", "building": "i-Tower", "floor": "22F", "seat": 42}},
	{"upn": "TIFFANYHJ_CHEN", "roles": ["it"], "floorPlan": { "country": "TW", "building": "i-Tower", "floor": "22F", "seat": 41}},
	{"id": "edc0bb9b-c8ce-4868-a087-3ddd0e7feec1", "roles": [], "floorPlan": { "country": "TW", "building": "i-Tower", "floor": "23F", "seat": 1}},
	{"id": "d7bc817b-2117-4acd-885e-055a01464837", "roles": [], "floorPlan": { "country": "TW", "building": "i-Tower", "floor": "23F", "seat": 2}},
	{"id": "ec8e11ef-9d36-4a6f-b17c-6159d7e3d44b", "roles": ["hr"], "floorPlan": { "country": "TW", "building": "i-Tower", "floor": "22F", "seat": 156}},
	{"id": "c979999f-4c0a-4048-b9f2-a17dfd26f3ec", "roles": ["hr"], "floorPlan": { "country": "TW", "building": "i-Tower", "floor": "22F", "seat": 119}},
	{"id": "bea28208-3907-4fa5-bc19-acd06a81d208", "roles": ["hr"], "floorPlan": { "country": "TW", "building": "i-Tower", "floor": "22F", "seat": 136}},
	{"id": "d331604c-274f-4556-9d27-de01c6926a92", "roles": ["legal"], "floorPlan": { "country": "TW", "building": "i-Tower", "floor": "22F", "seat": 91}},
	{"id": "dece32ce-de35-4ecd-95f8-89c3f980f50d", "roles": ["finance"], "floorPlan": { "country": "TW", "building": "i-Tower", "floor": "23F", "seat": 4}},
	{"id": "ddb2d5b7-f686-4df4-8d58-e47a63732e54", "roles": ["finance"], "floorPlan": { "country": "TW", "building": "i-Tower", "floor": "23F", "seat": 5}},
	{"id": "ca6a69f5-fe21-449c-9a4f-e33f71d21dc7", "roles": ["sales"], "floorPlan": { "country": "TW", "building": "Honhui", "floor": "14F", "seat": 1}},
	{"id": "cad229d7-27a3-4a24-a60b-baf4d37dab4e", "roles": ["sales"], "floorPlan": { "country": "TW", "building": "Honhui", "floor": "14F", "seat": 2}},
	{"id": "e9231655-cf92-4100-96e5-edc7be7f4570", "roles": ["sales"], "floorPlan": { "country": "TW", "building": "Honhui", "floor": "14F", "seat": 3}},
	{"id": "96345590-12d3-400b-8f1e-b7f154ab72ee", "roles": ["sales"], "floorPlan": { "country": "US", "building": "Brea", "floor": "1F", "seat": 1}},
	{"id": "cc0e352b-582b-4143-93b0-371b645214f2", "roles": ["sales"], "floorPlan": { "country": "TW", "building": "Honhui", "floor": "14F", "seat": 4}},
	{"id": "d000c0f4-bc78-4e20-b4b7-0bede23033ad", "roles": ["customer_support"], "floorPlan": { "country": "TW", "building": "Honhui", "floor": "14F", "seat": 5}},
	{"id": "c305e4af-390d-44f9-bc91-d36abb63398d", "roles": ["customer_support"], "floorPlan": { "country": "TW", "building": "Honhui", "floor": "14F", "seat": 6}},
	{"id": "f84d00d4-132a-422e-8677-e8ae42eefa1f", "roles": ["customer_support"], "floorPlan": { "country": "TW", "building": "Honhui", "floor": "14F", "seat": 7}},
	{"id": "0d31a4ee-cae9-425b-88a0-f291fc325435", "roles": ["sales"], "floorPlan": { "country": "CN", "building": "Shanghai", "floor": "11F", "seat": 1}},
	{"id": "d1433f1e-7af3-489d-a590-2c0f49833ff3", "roles": ["engineering"], "floorPlan": { "country": "TW", "building": "Honhui", "floor": "14F", "seat": 8}},
	{"id": "f5f098f0-64c0-4790-a15d-8604a5083e50", "roles": ["engineering"], "floorPlan": { "country": "TW", "building": "Honhui", "floor": "14F", "seat": 9}},
	{"id": "fd43c3d3-c755-4a80-94e1-ce2d60a35d71", "roles": ["engineering"], "floorPlan": { "country": "TW", "building": "Honhui", "floor": "14F", "seat": 10}},
	{"id": "bf5a0ba2-f6f2-4d73-b941-f0566811c90b", "roles": ["engineering"], "floorPlan": { "country": "TW", "building": "Honhui", "floor": "14F", "seat": 11}},
	{"id": "f744e63b-7419-4694-a344-a2a1268f217a", "roles": ["engineering"], "floorPlan": { "country": "TW", "building": "Honhui", "floor": "14F", "seat": 12}},
	{"id": "db0890a9-8628-44d2-8373-8e6d216614b4", "roles": ["engineering"], "floorPlan": { "country": "TW", "building": "Honhui", "floor": "14F", "seat": 13}},
	{"id": "d0bd07b1-fb03-40e9-80d7-6bf445809063", "roles": ["engineering"], "floorPlan": { "country": "TW", "building": "Honhui", "floor": "14F", "seat": 14}},
	{"id": "f30e14eb-80a0-4e01-b82a-0803e85aba13", "roles": ["engineering"], "floorPlan": { "country": "TW", "building": "Honhui", "floor": "14F", "seat": 15}},
	{"id": "dd6ca39c-141b-43e7-ae57-3ff643c77375", "roles": ["quality"], "floorPlan": { "country": "TW", "building": "i-Tower", "floor": "23F", "seat": 6}},
	{"id": "d0fd3387-b7fc-4f25-8cea-c556cd0d3412", "roles": ["quality"], "floorPlan": { "country": "TW", "building": "i-Tower", "floor": "23F", "seat": 7}},
	{"id": "d087014a-693e-4f61-a1cd-84d18ecabc61", "roles": ["quality"], "floorPlan": { "country": "TW", "building": "i-Tower", "floor": "23F", "seat": 9}},
	{"id": "ff1bbb0a-99ea-455f-8c41-08d05f43ab71", "roles": ["marketing"], "floorPlan": { "country": "TW", "building": "Honhui", "floor": "14F", "seat": 16}},
	{"id": "e181517a-0666-405b-a3f2-e36c41997e2d", "roles": ["marketing"], "floorPlan": { "country": "TW", "building": "i-Tower", "floor": "23F", "seat": 8}},
	{"id": "ddaed33f-e7d0-40a6-a3b3-516f2f83e1ca", "roles": ["marketing"], "floorPlan": { "country": "TW", "building": "Honhui", "floor": "14F", "seat": 17}},
	{"id": "26ed2143-9e5f-4cb4-b70a-8a7294bfa6ea", "roles": ["logistics"], "floorPlan": { "country": "TW", "building": "Bade", "floor": "5F", "seat": 1}},
	{"id": "e2d4ff8d-1d13-476a-8d3a-fd758fe1677b", "roles": ["logistics"], "floorPlan": { "country": "TW", "building": "Bade", "floor": "5F", "seat": 2}},
	{"id": "cfd8eb02-0954-4e52-b7b3-fabc07d5632a", "roles": ["logistics"], "floorPlan": { "country": "TW", "building": "Bade", "floor": "5F", "seat": 3}},
	{"id": "2078cc0d-df03-47f4-a171-21420246a2ef", "roles": ["manufacturing"], "floorPlan": { "country": "TW", "building": "Bade", "floor": "5F", "seat": 4}},
	{"id": "c2597251-1f54-41d0-bc15-3f3fa1579730", "roles": ["manufacturing"], "floorPlan": { "country": "TW", "building": "Bade", "floor": "5F", "seat": 5}},
	{"id": "c984e70f-d9cc-4254-8f3c-a42da2882f06", "roles": ["logistics"], "floorPlan": { "country": "TW", "building": "Bade", "floor": "1F", "seat": 1}},
	{"id": "c8b0b6dc-0e16-4e06-8fa9-08e90527b720", "roles": ["logistics"], "floorPlan": { "country": "TW", "building": "Bade", "floor": "5F", "seat": 6}}
]

def import_settings():
    user_storage = UserStorage()
    for setting in settings:
        new_setting = {}
        if 'upn' in setting:
            upn = (setting['upn'] + '@moxa.com').lower()
            user_id = user_storage.get_aad_id_by_upn(upn)
            print(f"User {upn} found, user_id: {user_id}")
            if user_id is None:
                print(f"User {upn} not found")
                continue
        else:
            user_id = setting['id']

        new_setting['roles'] = setting['roles']
        new_setting['floorPlan'] = setting['floorPlan']

        user_storage.set_user_settings(user_id, new_setting)
        print(f"Imported settings for user {user_id}")

if __name__ == '__main__':
    import_settings()