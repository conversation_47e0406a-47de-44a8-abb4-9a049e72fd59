<script setup>
import { ref, computed, onMounted, onBeforeUnmount } from 'vue'
import { useI18n } from 'vue-i18n'

const { t, locale } = useI18n()

const countdownConfig = ref({
  targetDate: '',
  description: '',
  displayFormat: 'days'
})

const emit = defineEmits(['update:countdownConfig'])

const localConfig = ref({
  targetDate: countdownConfig.targetDate || addDays(10), // ISO 8601 format for no,
  description: countdownConfig.description || 'Project Review',
  displayFormat: countdownConfig.displayFormat || 'days'
})

const remaining = ref({
  days: 0,
  hours: 0,
  minutes: 0,
  seconds: 0
})

function addDays(days) {
    const result = new Date();
    result.setDate(result.getDate() + days);
    return result;
}

const isEditing = ref(false)
const intervalId = ref(null)

const updateCountdown = () => {
  const now = Date.now();
  const target = new Date(localConfig.value.targetDate).getTime();
  const difference = target - now;

  let newRemaining;
  if (difference > 0) {
    newRemaining = {
      days: Math.floor(difference / (1000 * 60 * 60 * 24)),
      hours: Math.floor((difference % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60)),
      minutes: Math.floor((difference % (1000 * 60 * 60)) / (1000 * 60)),
      seconds: Math.floor((difference % (1000 * 60)) / 1000)
    };
  } else {
    newRemaining = { days: 0, hours: 0, minutes: 0, seconds: 0 };
  }
  // Only update if values actually changed
  if (JSON.stringify(remaining.value) !== JSON.stringify(newRemaining)) {
    remaining.value = newRemaining;
  }
}

const displayValue = computed(() => {
  switch (localConfig.value.displayFormat) {
    case 'days':
      return `${remaining.value.days} ${t('countdownCard.days')}`
    case 'hours':
      return `${remaining.value.days * 24 + remaining.value.hours} ${t('countdownCard.hours')}`
    case 'seconds':
      const totalSeconds = remaining.value.days * 24 * 60 * 60 +
                          remaining.value.hours * 60 * 60 +
                          remaining.value.minutes * 60 +
                          remaining.value.seconds
      return `${totalSeconds} ${t('countdownCard.seconds')}`
    default:
      return `${remaining.value.days} ${t('countdownCard.days')}`
  }
})

const saveChanges = () => {
  emit('update:countdownConfig', { ...localConfig.value })
  isEditing.value = false
}

onMounted(() => {
  updateCountdown()
  intervalId.value = setInterval(updateCountdown, 1000)
})

onBeforeUnmount(() => {
  if (intervalId.value) {
    clearInterval(intervalId.value)
  }
})
</script>

<template>
  <v-card class="countdown-timer-card" height="100%">
    <v-card-title class="d-flex justify-space-between align-center">
      <div class="d-flex align-center">
        <v-icon icon="mdi-timer" color="primary" class="mr-2"></v-icon>
        {{ t('countdownCard.title') }}
      </div>
      <v-btn icon="mdi-pencil" size="small" @click="isEditing = !isEditing" />
    </v-card-title>

    <v-card-text>
      <v-slide-y-transition>
        <div v-if="isEditing" class="pa-2">
          <v-text-field
            v-model="localConfig.description"
            :label="t('countdownCard.config.description')"
            variant="outlined"
            density="compact"
          />
          <v-text-field
            v-model="localConfig.targetDate"
            :label="t('countdownCard.config.targetDate')"
            type="datetime-local"
            variant="outlined"
            density="compact"
          />
          <v-select
            v-model="localConfig.displayFormat"
            :items="['days', 'hours', 'seconds']"
            :label="t('countdownCard.config.displayFormat')"
            variant="outlined"
            density="compact"
          />
          <v-btn
            color="primary"
            block
            @click="saveChanges"
          >
            {{ t('common.save') }}
          </v-btn>
        </div>
        <div v-else class="text-center mb-2">
          <div class="text-h5 font-weight-bold primary--text">
            {{ displayValue }}
          </div>
          <div class="text-subtitle-2">
            {{ localConfig.description }}
          </div>
        </div>
      </v-slide-y-transition>
    </v-card-text>
  </v-card>
</template>

<style scoped>
.countdown-timer-card {
  transition: all 0.3s ease;
}
</style>