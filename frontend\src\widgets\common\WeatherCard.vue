<script setup>
import { ref, onMounted } from 'vue'
import { useI18n } from 'vue-i18n'

const { t, locale } = useI18n()

const weatherData = ref({
  temperature: 25,
  description: 'partly cloudy',
  humidity: 65,
  icon: 'http://openweathermap.org/img/w/02d.png'
})

const loading = ref(false)
const error = ref(null)

// Mock data is used instead of API call
onMounted(() => {
  // Simulate a brief loading state
  setTimeout(() => {
    loading.value = false
  }, 500)
})
</script>

<template>
  <v-card class="weather-card">
    <v-card-title class="text-h6 font-weight-bold">
      <v-icon start icon="mdi-weather-partly-cloudy" class="mr-2"></v-icon>
      {{ t('weatherCard.title')  }}
    </v-card-title>
    <v-card-text>
      <v-container v-if="!loading && !error">
        <v-row align="center" justify="center">
          <v-col cols="12" class="text-center">
            <v-img :src="weatherData.icon" width="64" height="64" class="mx-auto"></v-img>
            <div class="text-h4 mt-2">{{ weatherData.temperature }}°C</div>
            <div class="text-subtitle-1 text-capitalize">{{ weatherData.description }}</div>
            <div class="text-body-2 mt-2">
              <v-icon icon="mdi-water-percent" size="small"></v-icon>
              {{ t('weatherCard.humidity')  }}: {{ weatherData.humidity }}%
            </div>
          </v-col>
        </v-row>
      </v-container>
      <v-container v-else-if="loading">
        <v-row align="center" justify="center">
          <v-col cols="12" class="text-center">
            <v-progress-circular indeterminate></v-progress-circular>
          </v-col>
        </v-row>
      </v-container>
      <v-container v-else>
        <v-row align="center" justify="center">
          <v-col cols="12" class="text-center text-error">
            {{ error }}
          </v-col>
        </v-row>
      </v-container>
    </v-card-text>
  </v-card>
</template>

<style scoped>
.weather-card {
  height: 100%;
}
</style>