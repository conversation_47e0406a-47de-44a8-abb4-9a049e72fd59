import asyncio
import json
import logging
import aiohttp
from typing import Any, Dict, List
from app.core.mcp_tool import MCPTool


class HTTPMCPClient:
    def __init__(self, name: str, config: dict[str, Any]) -> None:
        self.name = name
        self.config = config
        self.base_url = config.get("MCP_HTTP_URL", "http://localhost:8002")
        self.timeout = config.get("timeout", 30.0)
        self.retry_count = config.get("retry_count", 2)
        self.session = None
        self.tools = []
        self._initialized = False

    async def initialize(self, timeout: float = 30.0) -> None:
        """Initialize HTTP client"""
        logging.info("HTTPMCPClient: Starting initialization for %s", self.name)

        try:
            # Create HTTP session
            self.session = aiohttp.ClientSession(
                timeout=aiohttp.ClientTimeout(total=timeout),
                headers={
                    "Content-Type": "application/json",
                    "Accept": "application/json",
                },
            )

            # Test connection
            await self._test_connection()

            # Get available tools
            self.tools = await self.list_tools()

            self._initialized = True
            logging.info("HTTPMCPClient: Successfully initialized %s", self.name)

        except Exception as e:
            logging.error("HTTPMCPClient: Failed to initialize %s: %s", self.name, e)
            await self.cleanup()
            raise

    async def _test_connection(self) -> None:
        """Test connection to MCP server"""
        try:
            async with self.session.get(f"{self.base_url}/health") as response:
                if response.status != 200:
                    raise ConnectionError(f"Health check failed: {response.status}")
        except Exception as e:
            logging.warning(
                "HTTPMCPClient: Health check failed, continuing anyway: %s", e
            )

    async def list_tools(self) -> List[MCPTool]:
        """List available tools"""
        if not self.session:
            raise RuntimeError(f"HTTPMCPClient {self.name} not initialized")

        try:
            async with self.session.get(f"{self.base_url}/tools") as response:
                if response.status == 200:
                    data = await response.json()
                    tools = []
                    for tool_data in data.get("tools", []):
                        tools.append(
                            MCPTool(
                                name=tool_data["name"],
                                description=tool_data.get("description", ""),
                                input_schema=tool_data.get("inputSchema", {}),
                            )
                        )
                    return tools
                else:
                    logging.warning(
                        "HTTPMCPClient: Failed to list tools: %s", response.status
                    )
                    return []
        except Exception as e:
            logging.error("HTTPMCPClient: Error listing tools: %s", e)
            return []

    async def execute_tool(
        self,
        tool_name: str,
        arguments: dict[str, Any],
        retries: int = None,
        delay: float = 1.0,
        timeout: float = None,
    ) -> Any:
        """Execute tool"""
        if not self.session:
            raise RuntimeError(f"HTTPMCPClient {self.name} not initialized")

        # Use config parameters if not provided
        if retries is None:
            retries = self.retry_count
        if timeout is None:
            timeout = self.timeout

        attempt = 0
        while attempt < retries:
            try:
                logging.info(
                    "HTTPMCPClient: Executing %s with timeout %s seconds...",
                    tool_name,
                    timeout,
                )
                logging.debug("HTTPMCPClient: Tool arguments: %s", arguments)

                # Prepare request data
                request_data = {"tool": tool_name, "arguments": arguments}

                # Send HTTP request
                async with self.session.post(
                    f"{self.base_url}/execute",
                    json=request_data,
                    timeout=aiohttp.ClientTimeout(total=timeout),
                ) as response:
                    if response.status == 200:
                        result = await response.json()
                        logging.info(
                            "HTTPMCPClient: Tool %s executed successfully", tool_name
                        )
                        logging.debug("HTTPMCPClient: Tool result: %s", result)
                        return result
                    else:
                        error_text = await response.text()
                        raise RuntimeError(f"HTTP {response.status}: {error_text}")

            except asyncio.TimeoutError:
                attempt += 1
                logging.warning(
                    "HTTPMCPClient: Tool execution timed out after %s seconds. Attempt %s of %s.",
                    timeout,
                    attempt,
                    retries,
                )
                if attempt < retries:
                    logging.info("HTTPMCPClient: Retrying in %s seconds...", delay)
                    await asyncio.sleep(delay)
                else:
                    raise TimeoutError(
                        f"Tool {tool_name} execution timed out after {retries} attempts"
                    )

            except Exception as e:
                attempt += 1
                logging.warning(
                    "HTTPMCPClient: Error executing tool: %s. Attempt %s of %s.",
                    str(e),
                    attempt,
                    retries,
                )
                if attempt < retries:
                    logging.info("HTTPMCPClient: Retrying in %s seconds...", delay)
                    await asyncio.sleep(delay)
                else:
                    raise

    async def cleanup(self) -> None:
        """Clean up resources"""
        if self.session:
            try:
                await self.session.close()
            except Exception as e:
                logging.warning("HTTPMCPClient: Error during cleanup: %s", e)
            finally:
                self.session = None
                self._initialized = False

    async def __aenter__(self):
        """Enter async context manager"""
        await self.initialize()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Exit async context manager"""
        await self.cleanup()


# Direct query function to bypass MCP communication issues
async def direct_minima_query(query: str, timeout: float = 30.0) -> str:
    """Direct query to minima indexer, bypassing MCP communication issues"""

    indexer_url = "http://localhost:8001/query"

    async with aiohttp.ClientSession() as session:
        try:
            request_data = {"query": query}

            async with session.post(
                indexer_url,
                json=request_data,
                timeout=aiohttp.ClientTimeout(total=timeout),
                headers={
                    "Content-Type": "application/json; charset=utf-8",
                    "Accept": "application/json",
                },
            ) as response:
                if response.status == 200:
                    data = await response.json()
                    result = data.get("result", {})
                    output = result.get("output", "")
                    links = result.get("links", [])

                    # Format response
                    formatted_result = {
                        "content": [{"type": "text", "text": output}],
                        "isError": False,
                        "_meta": {"links": links},
                    }

                    logging.info("Direct query successful: %s", query[:50])
                    return formatted_result
                else:
                    error_text = await response.text()
                    raise RuntimeError(f"Indexer HTTP {response.status}: {error_text}")

        except Exception as e:
            logging.error("Direct query failed: %s", e)
            raise
