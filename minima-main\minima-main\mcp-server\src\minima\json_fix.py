import json
import logging
import re
from typing import Any, Dict, Optional

logger = logging.getLogger(__name__)

def fix_json_string(json_str: str) -> str:
    """
    修復可能包含Unicode轉義序列的JSON字符串
    """
    if not isinstance(json_str, str):
        return json_str
        
    # 記錄原始字符串的信息
    logger.debug(f"Fixing JSON string: {json_str[:50]}... (length: {len(json_str)})")
    
    # 移除尾部的換行符
    json_str = json_str.rstrip('\n')
    
    # 嘗試修復Unicode轉義序列
    try:
        # 檢查是否有未配對的代理項
        surrogate_pattern = re.compile(r'\\ud[89abcdef][0-9a-f]{2}(?!\\ud[c-f][0-9a-f]{2})', re.IGNORECASE)
        if surrogate_pattern.search(json_str):
            logger.debug("Found unpaired surrogate in JSON string, attempting to fix")
            # 替換未配對的代理項為問號
            json_str = surrogate_pattern.sub('?', json_str)
        
        # 嘗試解析修復後的JSON
        parsed = json.loads(json_str)
        # 將解析後的對象轉換回JSON字符串，確保正確編碼
        return json.dumps(parsed)
    except json.JSONDecodeError as e:
        logger.error(f"Failed to fix JSON: {e}")
        # 如果仍然無法解析，返回原始字符串
        return json_str

def safe_json_loads(json_str: str) -> Optional[Dict[str, Any]]:
    """
    安全地解析JSON字符串，處理各種編碼問題
    """
    if not json_str:
        return None
        
    try:
        # 首先嘗試直接解析
        return json.loads(json_str)
    except json.JSONDecodeError as e:
        logger.debug(f"Initial JSON parse failed: {e}")
        
        # 嘗試修復JSON字符串
        fixed_json = fix_json_string(json_str)
        if fixed_json != json_str:
            try:
                return json.loads(fixed_json)
            except json.JSONDecodeError as e2:
                logger.error(f"Failed to parse fixed JSON: {e2}")
        
        # 嘗試處理編碼問題
        try:
            # 嘗試不同的編碼方式
            encoded = json_str.encode('utf-8', errors='ignore')
            decoded = encoded.decode('utf-8')
            return json.loads(decoded)
        except Exception as e3:
            logger.error(f"Failed to handle encoding: {e3}")
            
        return None