<script setup>
import { ref } from 'vue'
import { useI18n } from 'vue-i18n';

const { t, locale } = useI18n();

// const props = defineProps({
//   ehsNews: {
//     type: Array,
//     required: true,
//     default: () => []
//   }
// })

const ehsNews = ref([
  {
    id: 1,
    title: '安心旅遊不『麻』煩，防護撇步快收藏!',
    date: '2023-07-15',
    content: '近日疾病管制署又再度公布國內新確診麻疹病例，有國內感染病例、也有境外移入病例，甚至國際旅遊疫情也時常出現旅遊警示情形...',
    image: 'https://picsum.photos/800/400?random=4'
  },
  {
    id: 2,
    title: '健康檢查的正確知識-破解常見迷思!',
    date: '2023-07-10',
    content: '許多人對於健康檢查有錯誤的觀念，可能導致過度期待或錯誤篩檢，甚至影響自身健康管理。',
    image: 'https://picsum.photos/800/400?random=5'
  },
  {
    id: 3,
    title: '2025 國健署五大癌症篩檢',
    date: '2023-07-05',
    content: '癌症初期可能沒有明顯症狀，因此早期發現與治療至關重要。根據國民健康署的數據，透過定期篩檢，可以大幅降低癌症死亡率並提高存活率。',
    image: 'https://picsum.photos/800/400?random=6'
  }
])

const formatDate = (dateString) => {
  return new Date(dateString).toLocaleDateString(locale.value, {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  })
}

</script>

<template>
  <v-card height="100%" class="ehs-news-card">
    <v-card-title class="d-flex align-center">
      <v-icon icon="mdi-newspaper" color="primary" class="mr-2"></v-icon>
      {{ t('ehsNews.title') }}
    </v-card-title>
    <v-card-text>
      <v-carousel
        cycle
        interval="6000"
        height="400"
        hide-delimiter-background
        show-arrows="hover"
      >
        <v-carousel-item
          v-for="news in ehsNews"
          :key="news.title"
        >
          <v-sheet height="100%" class="d-flex flex-column">
            <v-img
              :src="news.image"
              height="300"
              cover
              class="flex-grow-0"
            ></v-img>
            <v-sheet class="pa-4">
              <div class="d-flex align-center justify-space-between mb-2">
                <div class="text-h6">{{ news.title }}</div>
                <div class="text-caption text-grey">{{ formatDate(news.date) }}</div>
              </div>
              <div class="text-body-1">{{ news.content }}</div>
            </v-sheet>
          </v-sheet>
        </v-carousel-item>
      </v-carousel>
    </v-card-text>
  </v-card>
</template>