import asyncio
import logging
import sys
import os
from .server import main as server_main
from .patch_mcp import patch_mcp

# 設置日誌記錄，確保中文字符正確顯示
logging.basicConfig(
    level=logging.DEBUG,
    format="%(asctime)s - %(levelname)s - %(message)s",
    handlers=[
        logging.FileHandler("app.log", encoding="utf-8"),
        logging.StreamHandler(),
    ],
)

logger = logging.getLogger(__name__)


def main():
    try:
        logger.info("Starting Minima main function")
        logger.info(f"Python version: {sys.version}")

        # 設置環境變量以確保正確的編碼
        os.environ["PYTHONIOENCODING"] = "utf-8"
        logger.info("Set PYTHONIOENCODING to utf-8")

        # 應用MCP補丁
        patch_mcp()

        # 運行服務器
        asyncio.run(server_main())
    except KeyboardInterrupt:
        logger.info("Received keyboard interrupt, shutting down")
    except Exception as e:
        logger.exception(f"Unhandled exception: {e}")


# Optionally expose other important items at package level
__all__ = ["main", "server"]
