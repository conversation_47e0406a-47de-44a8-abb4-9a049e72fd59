__pycache__/
*.py[cod]
*$py.class

*.log

.cache
*.cover

.python-version
.env
*.env
.venv

.vscode/

local_files/
qdrant_data/
indexer_data/
ollama/

*.db
.envrc
.DS_Store

logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
firebase-debug.log*
firebase-debug.*.log*

.firebase/

pids
*.pid
*.seed
*.pid.lock

lib-cov
coverage
.nyc_output
.grunt
bower_components
.lock-wscript
build/Release
node_modules/
.npm
.eslintcache
.node_repl_history
*.tgz
.yarn-integrity
.dataconnect

node_modules/
*.local