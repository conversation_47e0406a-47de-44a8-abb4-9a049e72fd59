<template>
  <v-card height="100%" class="company-calendar-card">
    <v-card-title class="d-flex align-center">
      <v-icon icon="mdi-calendar" color="primary" class="mr-2"></v-icon>
      Company Calendar
    </v-card-title>
    <v-card-text>
        <v-row class="fill-height">
          <v-col>
            <v-sheet height="600">
              <v-calendar
                ref="calendar"
                v-model="today"
                :events="events"
                color="primary"
                type="month"
                >
              </v-calendar>
            </v-sheet>
          </v-col>
        </v-row>
    </v-card-text>
  </v-card>
</template>

<script setup>
import { ref } from 'vue'
import { VCalendar } from 'vuetify/labs/VCalendar'

const today = ref(new Date())

const events = ref([
  {
    title: 'Event 1',
    start: new Date('2025-03-21'),
    end: new Date('2025-03-21'),
    color: 'primary',
    allDay: true,
  },
  {
    title: 'Event 2',
    start: new Date('2025-03-23'),
    end: new Date('2025-03-23'),
    color: 'secondary',
    allDay: true, 
  },
  {
    title: 'Event 3',
    start: new Date('2025-03-25'),
    end: new Date('2025-03-25'),
    color: 'info',
    allDay: true,  
  },
  {
    title: 'Event 4',
    start: new Date('2025-03-27'),
    end: new Date('2025-03-27'),
    color: 'success',
    allDay: true,  
  },
])

</script>
