from datetime import datetime, timezone
from fastapi import APIRouter, Depends
from functools import lru_cache
from app.services.floorplan_service import FloorplanService
from app.utils.logger import ColoredLogger

logger = ColoredLogger("app.api.floorplan")

router = APIRouter()

@lru_cache()
def get_floorplan_service():
    return FloorplanService()

@router.get("/buildings")
async def get_all_buildings(
    service: FloorplanService = Depends(get_floorplan_service)
):
    try:
        response = service.get_all_buildings()
        return {
            "success": True,
            "data": response,
            "timestamp": datetime.now(timezone.utc).isoformat()
        }
    except Exception as e:
        logger.error("GET /buildings: %s", str(e))
        return {
            "success": False,
            "error": str(e),
            "timestamp": datetime.now(timezone.utc).isoformat()
        }

@router.get("/buildings/{building_id}/floors/{floor_id}/areas")
async def get_areas(
    building_id: str,
    floor_id: str,
    service: FloorplanService = Depends(get_floorplan_service)
):
    try:
        response = service.get_areas(building_id, floor_id)
        if response:
            return {
                "success": True,
                "data": response,
                "timestamp": datetime.now(timezone.utc).isoformat()
            }
        return {
            "success": False,
            "error": "No data found",
            "timestamp": datetime.now(timezone.utc).isoformat()
        }
    except Exception as e:
        logger.error(f"GET /buildings/{building_id}/floors/{floor_id}/areas: %s", str(e))
        return {
            "success": False,
            "error": str(e),
            "timestamp": datetime.now(timezone.utc).isoformat()
        }


@router.get("/seats/{user_id}")
async def get_user_seat(
    user_id: str,
    service: FloorplanService = Depends(get_floorplan_service)
):
    try:
        response = service.get_user_seat(user_id)
        if response:
            return {
                "success": True,
                "data": response,
                "timestamp": datetime.now(timezone.utc).isoformat()
            }
        return {
            "success": False,
            "error": "No data found",
            "timestamp": datetime.now(timezone.utc).isoformat()
        }
    except Exception as e:
        logger.error(f"GET /seats/{user_id}: %s", str(e))
        return {
            "success": False,
            "error": str(e),
            "timestamp": datetime.now(timezone.utc).isoformat()
        }