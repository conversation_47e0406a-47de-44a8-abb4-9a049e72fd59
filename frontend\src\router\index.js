import { createRouter, createWebHistory } from 'vue-router'
import CompanyCalendarPage from '@/pages/CompanyCalendarPage.vue'
import OfficeSimulationPage from '@/pages/OfficeSimulationPage.vue'
import OrgChartPage from '@/pages/OrgChartPage.vue'
import ChatBotPage from '@/pages/ChatBotPage.vue'

const routes = [
  {
    path: '/',
    redirect: '/dashboard'
  },
  {
    path: '/dashboard',
    name: 'Dashboard',
    component: () => import('../App.vue')
  },
  {
    path: '/calendar',
    name: 'Calendar',
    component: CompanyCalendarPage
  },
  {
    path: '/office-floorplan',
    name: 'OfficeFloorplan',
    component: OfficeSimulationPage
  },
  {
    path: '/organization-chart',
    name: 'OrganizationChart',
    component: OrgChartPage
  },
  {
    path: '/chat',
    name: 'ChatBot',
    component: ChatBotPage 
  },
  {
    path: '/admin',
    name: 'Admin',
    component: () => import('@/pages/AdminPage.vue')
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

export default router