### Pathfinding with Three.js

import * as THREE from 'three'
import { use<PERSON>oa<PERSON>, use<PERSON><PERSON><PERSON>, useThree } from '@react-three/fiber'
import { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader'
import { useGLTF } from '@react-three/drei'

import { Pathfinding, PathfindingHelper } from 'three-pathfinding'
import { useEffect, useRef } from 'react'

const ZONE = 'level'
const SPEED = 5
const OFFSET = 0.2

export default function Scene() {
  const { scene } = useThree()
  const pathfinder = useRef()
  const helper = useRef()

  //const gltf = useLoader(GLTFLoader, '/level.glb')
  const { nodes: levelNodes } = useGLTF('/level.glb')
  const { nodes: navNodes } = useGLTF('/level.nav.glb')
  const playerPosition = useRef(new THREE.Vector3(-3.5, 0.5, 5.5))
  const targetPosition = useRef(new THREE.Vector3())
  const level = useRef()
  const navmesh = useRef()
  const groupID = useRef()
  const path = useRef()
  useEffect(() => {
    pathfinder.current = new Pathfinding()
    helper.current = new PathfindingHelper()
    scene.add(helper.current)
    // why
    window.level = level.current

    // use loaded geometry
    console.time('createZone()')
    // Builds a zone/node set from navigation mesh geometry
    const zone = Pathfinding.createZone(navNodes.Navmesh_Mesh.geometry)
    console.timeEnd('createZone()')
    // Sets data for the given zone
    pathfinder.current.setZoneData(ZONE, zone)
    // Returns closest node group ID for given position
    groupID.current = pathfinder.current.getGroup(ZONE, playerPosition.current)

    helper.current.setPlayerPosition(new THREE.Vector3(-3.5, 0.5, 5.5)).setTargetPosition(new THREE.Vector3(-3.5, 0.5, 5.5))
  }, [])
  function clickMesh(e) {
    console.log('click')

    // safe clicked position in ref
    targetPosition.current.copy(e.point)

    // remove old helpers and set clicked pos
    helper.current.reset().setPlayerPosition(playerPosition.current)

    // Returns closest node group ID for given position
    const targetGroupID = pathfinder.current.getGroup(ZONE, targetPosition.current, true)

    // Returns the closest node to the target position
    const closestTargetNode = pathfinder.current.getClosestNode(targetPosition.current, ZONE, targetGroupID, true)
    helper.current.setTargetPosition(targetPosition.current)
    if (closestTargetNode) helper.current.setNodePosition(closestTargetNode.centroid)
    // Calculate a path to the target and store it
    path.current = pathfinder.current.findPath(playerPosition.current, targetPosition.current, ZONE, groupID.current)
    if (path.current && path.current.length) {
      helper.current.setPath(path.current)
    } else {
      const closestPlayerNode = pathfinder.current.getClosestNode(playerPosition.current, ZONE, groupID.current)
      const clamped = new THREE.Vector3()

      // Clamps a step along the navmesh, given start and desired endpoint.
      // May be used to constrain first-person / WASD controls
      pathfinder.current.clampStep(
        playerPosition.current,
        targetPosition.current.clone(),
        closestPlayerNode,
        ZONE,
        groupID.current,
        clamped
      )

      helper.current.setStepPosition(clamped)
    }
  }

  useFrame((_, delta) => {
    if (!level || !(path.current || []).length) return

    let targetPosition = path.current[0]
    const velocity = targetPosition.clone().sub(playerPosition.current)

    if (velocity.lengthSq() > 0.05 * 0.05) {
      velocity.normalize()
      // Move player to target
      playerPosition.current.add(velocity.multiplyScalar(delta * SPEED))
      helper.current.setPlayerPosition(playerPosition.current)
    } else {
      // Remove node from the path we calculated
      path.current.shift()
    }
  })
  return (
    <group dispose={null}>
      {/* 3D scene */}
      <mesh
        ref={level}
        castShadow
        receiveShadow
        geometry={levelNodes.Cube.geometry}
        material={
          new THREE.MeshStandardMaterial({
            color: 'yellow',
            flatShading: true,
            roughness: 1,
            metalness: 0
          })
        }
      />
      {/* movable mesh wireframe to see nodes */}
      <mesh
        position={[0, OFFSET / 2, 0]}
        geometry={navNodes.Navmesh_Mesh.geometry}
        material={
          new THREE.MeshBasicMaterial({
            color: 0x808080,
            wireframe: true
          })
        }></mesh>
      {/* movable mesh */}
      <mesh
        onClick={clickMesh}
        ref={navmesh}
        geometry={navNodes.Navmesh_Mesh.geometry}
        material={
          new THREE.MeshBasicMaterial({
            color: 0xffffff,
            opacity: 0.75,
            transparent: true
          })
        }></mesh>
    </group>
  )
}
