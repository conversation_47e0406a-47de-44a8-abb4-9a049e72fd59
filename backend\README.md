# Overview
This is a FastAPI backend server for Moxa Employee Portal application. It provides endpoints for interacting with AI chat models and managing user data.

# Development
## Prerequisites
- Python 3.12.9
- uv package manager
- Docker (for Redis)
- Git
## Git Workflow
**TODO**: define git workflow here.

# Installation
First, clone the repository, then create a virtual environment and install the dependencies:

```bash
uv venv --python 3.12.9
.venv\scripts\activate
```
```bash
uv pip install .
```

# Environment Variables
Before running the app, make sure to set up your environment variables. Copy the `app\.env.example` to `app\.env` and replace the settings there.
Key environment variables needed:
- `FRONTEND_URL`: Frontend application URL
- `REDIS_URL`: Redis connection URL
- `BACKUP_DIR`: Redis backup directory, e.g. `./backups`

This is to get user profile information.
- `GRAPH_API_BASE`: "https://graph.microsoft.com/v1.0"
- `AAD_CLIENT_ID`: Your client ID
- `AAD_CLIENT_SECRET`: Your client secret
- `AAD_TENANT_ID`: Your tenant ID
- `AAD_REDIRECT_URI`: http://localhost:8000/auth/callback

This is to use AI chat model. To set up available providers and models, see `app/core/llm_model_manager.py` for more information.
## OpenAI
- `OPENAI_API_BASE`: "https://api.openai.com/v1"
- `OPENAI_API_KEY`: Your API key
## Aliyun
- `ALIYUN_API_BASE`: "https://dashscope.aliyuncs.com/compatible-mode/v1"
- `ALIYUN_API_KEY`: Your API key
- `ALIYUN_TTS_BASE`: "https://dashscope.aliyuncs.com/api/v1/services/aigc/multimodal-generation/generation"
## Google
- `GOOGLE_API_BASE`: "https://generativelanguage.googleapis.com/v1beta/openai/"
- `GOOGLE_API_KEY`: Your API key
## DeepSeek
- `DEEPSEEK_API_BASE`: "https://api.deepseek.com"
- `DEEPSEEK_API_KEY`: Your API key

This is to use the mcp-atlassian MCP server (see https://github.com/sooperset/mcp-atlassian for installation).
- `JIRA_URL`: https://jira-dc.moxa.com 
- `JIRA_EMAIL`: Jira account email
- `JIRA_PERSONAL_TOKEN`: Jira personal access token
- `CONFLUENCE_URL`: https://wiki.moxa.com
- `CONFLUENCE_EMAIL`: Confluence account email
- `CONFLUENCE_PERSONAL_TOKEN`: Confluence personal access token

See `.env.example` for all required variables.

# Before Running
First, make sure you have the Redis server running.
```bash
docker run -d --name redis-stack-server -p 6379:6379 redis/redis-stack-server:latest
```

Then use app/scripts/restore_redis.py to restore Redis data from backup.

# Running the App
Activate the virtual environment and run the app. The backend will be running on http://localhost:8000.

```bash
uvicorn app.main:app
```

This backend server's API docs are available at http://localhost:8000/docs.

# Troubleshooting
## Common Issues
1. MCP Client Initialization Failures
   - Check if all required environment variables are set
   - Verify MCP server configurations in `config/mcp_servers.py`. You can set is_active=False to bypass MCP client initialization.
   - Ensure MCP server paths/commands/arguments are correctly set in `config/mcp_servers.py`. If you have a new MCP server, you need to add it here.
   - Check if required services (Redis, etc.) are running

2. Redis Connection Issues
   - Ensure Redis is running: `docker ps`
   - Verify Redis connection URL in .env
   - Check if Redis port (6379) is not in use

3. Package Installation Issues
   - Try removing .venv and recreating it
   - Update uv: `pip install -U uv`
   - Check Python version matches requirement (3.12.9)

## Getting Help
For issues not covered here:
1. Check the logs
2. Review configuration files
3. Contact Graham for help

# Project Structure
```bash
Backend/
├── app/
│    ├── api/                                # All API routers grouped by business domain
│    │    ├── ai_chat.py                     # AI chat endpoints
│    │    ├── user.py                        # user endpoints, **TODO**: OAuth2 authentication
│    │    ├── floorplan.py                   # floorplan endpoints
│    │    └── widgets.py                     # **TODO**: Widgets endpoints
│    ├── config/                             # Some static configurations
│    │    ├── mcp_servers.py                 # MCP server configuration
│    │    └── prompts.py                     # Prompts for AI chat
│    ├── core/                               # Core app setup (auth, config, security, database connection)
│    │    ├── ai_chat_engine.py              # Handles chat completions and dynamically selects LLM providers & models.
│    │    ├── llm_provider_base.py           # Abstract base class for LLM providers.
│    │    ├── llm_model_manager.py           # Centralized management of providers, models, and their corresponding classes.
│    │    ├── aliyun_llm_provider.py         # You can add your own LLM provider here. This is for Aliyun.
│    │    ├── deepseek_llm_provider.py       # 
│    │    ├── google_llm_provider.py         #
│    │    ├── openai_llm_provider.py         #
│    │    ├── mcp_client.py                  # MCP client (currently only stdio transport is supported, TODO: supporting streamable HTTP)
│    │    └── mcp_tool.py                    # MCP tool
│    ├── models/                             # **TODO**: Database models
│    ├── schemas/                            # Schemas
│    │    ├── ai_chat.py                     # AI chat schemas
│    │    ├── user.py                        # User schemas
│    │    └── widget.py                      # **TODO**: Widget schemas
│    ├── scripts/                            # Utility scripts
│    │    ├── import_floorplan_to_redis.py   # Import script for floorplan data
│    │    ├── backup_redis.py                # Backup Redis data
│    │    └── restore_redis.py               # Restore Redis data
│    ├── services/                           # Business logic layer (more complex logic)
│    │    ├── ai_chat_service.py             # AI chat service
│    │    ├── user_service.py                # User service
│    │    ├── floorplan_service.py           # Floorplan service
│    │    └── widget_service.py              # **TODO**: widget service
│    ├── storage/                            # Storage layer (Redis...)
│    │    ├── ai_chat_storage.py
│    │    ├── user_storage.py
│    │    └── floorplan_storage.py           # For floorplan data
│    ├── utils/                              # Utility functions (authentication, helper functions, etc.)
│    └── main.py                             # FastAPI app entrypoint
├── tests/                                   # **TODO**: Unit and integration tests
│    ├── api/
│    ├── crud/
│    └── services/
├── alembic/                                 # **TODO**: DB migrations
├── .env                                     # Environment variables (e.g., DB_URL, SECRET_KEY)
├── pyproject.toml                           # Python package list
└── README.md
```
