<script setup>
import { ref, computed, onMounted, onBeforeUnmount } from 'vue'
import { fabric } from 'fabric'
import { areas, employees, addArea, updateArea, deleteArea, assignEmployeeToArea, unassignEmployee } from '../stores/areaStore'
import { parseAreaFromId, parseSVGAreas } from '@/utils/svgParser'

const canvasContainer = ref(null)
const canvas = ref(null)
const selectedArea = ref(null)
const selectedEmployee = ref(null)
const prevSelectedPolygon = ref(null)
const prevSelectedEmployee = ref(null)

// Computed property to format employee names
const assignedEmployeeNames = computed(() => {
  if (selectedArea.value && selectedArea.value.assignedEmployee) {
    const employee = employees.value.find(e => e.id === selectedArea.value.assignedEmployee);
    return employee ? employee.name : '';
  }
  return '';
});

// Handle window resize
const handleResize = () => {
  if (canvas.value) {
    canvas.value.setDimensions({
      width: canvasContainer.value.parentElement.clientWidth,
      height: canvasContainer.value.parentElement.clientHeight
    })
    canvas.value.renderAll()
  }
}

onMounted(async () => {
  // Initialize Fabric.js canvas with proper dimensions
  const container = canvasContainer.value.parentElement
  const containerWidth = container.clientWidth
  const containerHeight = container.clientHeight
  
  canvas.value = new fabric.Canvas(canvasContainer.value, {
    width: containerWidth,
    height: containerHeight,
    selection: false,
    preserveObjectStacking: true,
    backgroundColor: '#ffffff'
  })

  window.addEventListener('resize', handleResize)

  const svgPath = new URL('../stores/iTower_22F.svg', import.meta.url).href
  fabric.loadSVGFromURL(svgPath, (objects, options) => {
    if (!objects || objects.length === 0) {
      console.error('Failed to load SVG floor plan');
      // Fallback to loading a PNG if SVG loading fails
      canvas.value.setBackgroundImage('/floorplan/iTower_22F.png', canvas.value.renderAll.bind(canvas.value), {
        backgroundImageOpacity: 0.5,
        backgroundImageStretch: true,
      });
      return;
    }

    const svg = fabric.util.groupSVGElements(objects, options);
    
    // Set the SVG group as the background image
    canvas.value.setBackgroundImage(svg, canvas.value.renderAll.bind(canvas.value), {
      backgroundImageOpacity: 1, // Set opacity to 1 for full visibility
      backgroundImageStretch: true, // Stretch to fit the canvas
    });

    // Calculate scale to fit the canvas while maintaining aspect ratio
    const scaleX = canvas.value.width / svg.width
    const scaleY = canvas.value.height / svg.height
    const scale = Math.min(scaleX, scaleY) * 0.95 // 95% of the available space
    
    svg.scale(scale)
    
    // Position SVG at top-left corner
    const left = 0
    const top = 0
    
    svg.set({
      left: left,
      top: top,
      selectable: false,
      evented: false,
      opacity: 1,
      // fill: '#ffffff',
      stroke: '#000000',
      strokeWidth: 1
    })
    
    canvas.value.add(svg)
    canvas.value.sendToBack(svg)
    // canvas.value.renderAll()

    // Parse and add pre-defined areas from SVG
    parseSVGAreas(svgPath).then(svgAreas => {
      svgAreas.forEach(areaInfo => {
        // Check if any employee already has this area assigned
        const employeeWithArea = employees.value.find(e => e.areaId === areaInfo.id);
        const area = {
          id: areaInfo.id,
          name: areaInfo.name,
          type: areaInfo.type,
          coordinates: areaInfo.coordinates,
          assignedEmployee: employeeWithArea ? employeeWithArea.id : null,
        }
        addArea(area)

        // Convert coordinates to Fabric.js format and scale them relative to SVG
        const points = area.coordinates.map(coord => ({
          x: coord[0] * scale + left,
          y: coord[1] * scale + top
        }))

        const polygon = new fabric.Polygon(points, {
          fill: area.type === 'seat' ? '#4CAF50' : '#2196F3',
          originalFill: area.type === 'seat' ? '#4CAF50' : '#2196F3',
          opacity: 0.2,
          stroke: area.type === 'seat' ? '#4CAF50' : '#2196F3',
          strokeWidth: 2,
          objectCaching: false,
          evented: true,
          selectable: true,
          lockMovementX: true,
          lockMovementY: true,
          hasControls: false,
          hasBorders: true,
        })

        polygon.areaId = area.id
        canvas.value.add(polygon)
        
        // Add employee thumbnail if area has an assigned employee
        if (area.assignedEmployee) {
          const employee = employees.value.find(e => e.id === area.assignedEmployee);
          if (employee && employee.picture) {
            // Calculate center of the polygon
            const centerX = points.reduce((sum, point) => sum + point.x, 0) / points.length;
            const centerY = points.reduce((sum, point) => sum + point.y, 0) / points.length;
            
            // Create and position employee thumbnail
            fabric.Image.fromURL(employee.picture.thumbnail, img => {
              img.set({
                left: centerX - img.width / 4,
                top: centerY - img.height / 4, 
                scaleX: 0.5,
                scaleY: 0.5,
                selectable: true,
                hasControls: false,
                lockMovementX: true,
                lockMovementY: true
              });
              img.employeeId = employee.id;

              canvas.value.add(img);
              // Add click handler for area selection
              img.on('selected', (options) => {
                const object = options.target;
                if (object && object.employeeId) {
                  selectedEmployee.value = object.employeeId;
                  canvas.value.renderAll();
                  prevSelectedEmployee.value = object;
                }
                else {
                  selectedEmployee.value = null;
                  prevSelectedEmployee.value = null;
                }
              })
            });
          }
        }

        // Add click handler for area selection
        polygon.on('selected', (options) => {
          const object = options.target;
          if (prevSelectedPolygon.value && prevSelectedPolygon.value !== object) {
            prevSelectedPolygon.value.set({ fill: prevSelectedPolygon.value.originalFill });
            canvas.value.renderAll();
          }
          if (object && object.areaId) {
            selectedArea.value = parseAreaFromId(object.areaId)
            object.set({ fill: 'yellow' });
            canvas.value.renderAll();
            prevSelectedPolygon.value = object;
          }
          else {
            selectedArea.value = null;
            prevSelectedPolygon.value = null;
          }
        })
      })
    })

    canvas.value.renderAll()
  })

  // Enable panning and zooming
  canvas.value.setZoom(1); // Initial zoom level
  canvas.value.viewportTransform = [1, 0, 0, 1, 0, 0]; // Reset viewport

  canvas.value.on('mouse:wheel', function(opt) {
    var delta = opt.e.deltaY;
    var zoom = canvas.value.getZoom();
    zoom *= 0.999 ** delta;
    if (zoom > 20) zoom = 20;
    if (zoom < 0.01) zoom = 0.01;
    canvas.value.setZoom(zoom);
    opt.e.preventDefault();
    opt.e.stopPropagation();
  });
  canvas.value.on('mouse:down', function(opt) {
    var evt = opt.e;
    // if (evt.altKey === true) {
      this.isDragging = true;
      this.selection = false;
      this.lastPosX = evt.clientX;
      this.lastPosY = evt.clientY;
    // }
  });
  canvas.value.on('mouse:move', function(opt) {
    if (this.isDragging) {
      var e = opt.e;
      var vpt = this.viewportTransform;
      vpt[4] += e.clientX - this.lastPosX;
      vpt[5] += e.clientY - this.lastPosY;
      this.requestRenderAll();
      this.lastPosX = e.clientX;
      this.lastPosY = e.clientY;
    }
  });
  canvas.value.on('mouse:up', function(opt) {
    // on mouse up we want to recalculate new interaction
    // for all objects, so we call setViewportTransform
    this.setViewportTransform(this.viewportTransform);
    this.isDragging = false;
    this.selection = true;
  });

})

onBeforeUnmount(() => {
  window.removeEventListener('resize', handleResize)
  if (canvas.value) {
    canvas.value.dispose()
  }
})

// Function to handle employee assignment
const assignEmployee = () => {
  if (selectedArea.value && selectedEmployee.value) {
    // Assuming selectedEmployee.value contains the employee ID
    const employeeId = selectedEmployee.value; // You might need to adjust this based on your data structure
    assignEmployeeToArea(selectedArea.value.id, employeeId);
    selectedEmployee.value = null; // Clear the combobox after assignment
  }
};

</script>

<template>
  <v-card class="office-floorplan-card" height="100%">
    <v-card-title class="d-flex align-center text-h5">
      <v-icon icon="mdi-floor-plan" color="primary" class="mr-2" size="large"></v-icon>
      Office Floorplan
    </v-card-title>
    <v-card-text class="pa-0 d-flex flex-row">
      <v-card class="area-info-panel pa-4" style="width: 300px; min-width: 300px; margin-right: 20px;">
        <h3>Information</h3>
        <v-list v-if="selectedArea">
          <v-list-item :title="'ID: ' + selectedArea?.id"></v-list-item>
          <v-list-item :title="'Building: ' + selectedArea?.building"></v-list-item>  <!-- Assuming 'building' property exists -->
          <v-list-item :title="'Floor: ' + selectedArea?.floor"></v-list-item>  <!-- Assuming 'floor' property exists -->
          <v-list-item :title="'Type: ' + selectedArea?.type"></v-list-item>
          <v-list-item :title="'AssignedEmployee: ' + selectedArea?.assignedEmployee"></v-list-item>
        </v-list>
        <div v-else>
          <p>No area selected</p>
        </div>
      </v-card>
      <div class="floorplan-container flex-grow-1">  <!-- Added flex-grow-1 -->
        <div class="canvas-container" style="height: calc(100vh - 200px); flex: 1;">
          <canvas ref="canvasContainer"></canvas>
        </div>
      </div>
    </v-card-text>
  </v-card>
</template>

<style scoped>
.office-floorplan-card {
  display: flex;
  flex-direction: column;
}

.search-field {
  max-width: 300px;
}

.area-panel {
  border-left: 1px solid rgba(0, 0, 0, 0.12);
  background-color: #f5f5f5;
}

.canvas-container {
  position: relative;
}

canvas {
  position: absolute;
  top: 0;
  left: 0;
  width: 100% !important;
  height: 100% !important;
}
.area-info-panel {
  border-right: 1px solid rgba(0, 0, 0, 0.12);
  background-color: #f5f5f5;
}
</style>