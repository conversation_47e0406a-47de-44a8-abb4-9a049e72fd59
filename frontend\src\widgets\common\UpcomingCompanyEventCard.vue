<template>
  <v-card height="100%" class="upcoming-company-event-card">
    <v-card-title class="d-flex align-center">
      <v-icon icon="mdi-calendar" color="primary" class="mr-2"></v-icon>
      {{ t('companyEvents.upcomingTitle') }}
      <v-spacer></v-spacer>
      <v-btn
        variant="text"
        color="primary"
        :to="'/calendar'"
        class="text-none"
      >
        {{ t('common.viewAll')  }}
      </v-btn>
    </v-card-title>
    <v-card-text>
      <v-list lines="two">
        <v-list-item
          v-for="event in upcomingEvents"
          :key="event.start + event.title"
        >
          <template v-slot:prepend>
            <v-avatar
              :color="event.color"
              size="36"
              class="mr-3"
            >
              <v-icon color="white" size="20">mdi-calendar-blank</v-icon>
            </v-avatar>
          </template>
          <div>
            <v-list-item-title class="text-subtitle-1">
                {{ event.title }}
            </v-list-item-title>
            <v-list-item-subtitle class="text-caption">
                {{ formatDate(event.start) }} - {{ formatDate(event.end) }}
            </v-list-item-subtitle>
          </div>
        </v-list-item>
      </v-list>
    </v-card-text>
  </v-card>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useI18n } from 'vue-i18n';
import { events } from '@/stores/eventStore'

const { t, locale } = useI18n();

const upcomingEvents = computed(() => {
  const now = new Date()
  return events.value
    .filter(event => new Date(event.start) >= now)
    .sort((a, b) => new Date(a.start) - new Date(b.start))
    .slice(0, 5)
})

function formatDate(date) {
  return new Date(date).toLocaleDateString(locale.value, {
    month: 'long',
    day: 'numeric',
    weekday: 'long'
  })
}


</script>