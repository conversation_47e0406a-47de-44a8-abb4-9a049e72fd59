import { ref, reactive, computed, nextTick } from 'vue'
import { useI18n } from 'vue-i18n'
import { useLocalStorage } from '@vueuse/core'
import { useUserStore } from '@/stores/userStore'
import {
  getAvailableTools, startNewChat, getSession, getSessions, deleteAllSessions,
  sendChatMsg, sendStreamingChatMsg, getProviders, updateMessageFeedback
} from '@/services/chatService'
import { speak, cancelSpeak } from '@/utils/speak'
import { speakStream } from '@/utils/speakStream'

export function useChat() {
  const { t, locale } = useI18n()
  const userStore = useUserStore()
  const MESSAGES_PER_PAGE = 20
  const currentPage = ref(1)
  const state = reactive({
    chatHistory: [],
    chatInput: '',
    selectedTag: '',
    isTyping: false,
    error: null,
    isRecording: false,
    isPlaying: false,
    playNextMessage: false,
    isLoadingMore: false,
    isStreamingEnabled: true,
    isToolsEnabled: true,
    showToolsDialog: false,
    showHistoryDialog: false,
    conversations: [],
    currentConversationId: useLocalStorage('moxart-current-conversation', null),
    answeringMode: 'research',
    providers: {},
    providerName: '',
    modelName: '',
  })

  const messageCache = new Map()
  const getCachedMessage = (message) => {
    messageCache.set(message.id, { ...message })
    return messageCache.get(message.id)
  }

  const visibleMessages = computed(() => {
    const start = Math.max(0, state.chatHistory.length - (currentPage.value * MESSAGES_PER_PAGE))
    return state.chatHistory.slice(start).map(getCachedMessage)
  })

  // ...move all chat logic here, e.g. createNewChat, clearHistory, updateFeedback, etc.
  // Example:
  const createNewChat = async () => {
    try {
      const title = t('chatbot.newConversation')
      const response = await startNewChat(userStore.currentUser.id, title)
      if (response && response.session_id) {
        const newConversation = {
          id: response.session_id,
          messages: [],
          title,
          createdAt: formatDatetime(),
        }
        state.conversations.unshift(newConversation)
        state.currentConversationId = response.session_id
        state.chatHistory = []
        state.chatHistory.push({
          id: Date.now(),
          message: t('chatbot.welcomeMessage', { user_name: userStore.currentUser.displayName }),
          sender: 'bot',
          tools: [],
          think: '',
          evaluate: '',
          refine: '',
          refs: [],
          followups: [],
          mermaid: '',
          feedback: 'neutral',
          timestamp: formatDatetime()
        })
      } else {
        state.error = t('chatbot.errorMessage') + ' (Failed to create new chat session)'
      }
    } catch (error) {
      state.error = t('chatbot.errorMessage') + ' (Failed to create new chat session)'
    }
  }

  // ...add other methods (clearHistory, updateFeedback, fetchProviders, etc.)

  const formatDatetime = (timestamp) => {
    const dateObj = timestamp ? new Date(timestamp) : new Date()
    return dateObj.toLocaleDateString(locale.value, {
      month: 'long',
      day: 'numeric',
      weekday: 'long',
      year: 'numeric',
      hour: 'numeric',
      minute: 'numeric',
      second: 'numeric'
    })
  }

  return {
    state,
    currentPage,
    visibleMessages,
    createNewChat,
    // ...export other methods here
    formatDatetime,
  }
}