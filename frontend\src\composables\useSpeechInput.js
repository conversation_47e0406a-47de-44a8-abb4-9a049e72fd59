// composables/useSpeechInput.ts
export function useSpeechInput(onResult) {
    const recognition = window.SpeechRecognition || window.webkitSpeechRecognition
    if (!recognition) return { start: () => {}, stop: () => {} }
  
    const recog = new recognition()
    recog.lang = 'en-US'
    recog.continuous = false
    recog.interimResults = false
  
    recog.onresult = (event) => {
      const transcript = event.results[0][0].transcript
      onResult(transcript)
    }
  
    return {
      start: () => recog.start(),
      stop: () => recog.stop(),
    }
  }
  