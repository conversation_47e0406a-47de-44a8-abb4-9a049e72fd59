{"common": {"name": "Name", "amount": "Amount", "lastUpdated": "Last Updated", "save": "Save", "cancel": "Cancel", "close": "Close", "delete": "Delete", "add": "Add", "edit": "Edit", "search": "Enter keywords and press Enter to search", "view": "View", "viewAll": "View All", "action": "Action", "refresh": "Refresh", "title": "Title", "open": "Open", "dueDate": "Due Date", "type": "Type", "status": "Status", "priority": "Priority", "description": "Description", "deleteConfirmation": "Are you sure you want to delete this item?", "loading": "Loading..."}, "menus": {"aboutMoxa": {"title": "About Moxa", "submenus": {"companyProfile": "Company Profile", "qualityPolicy": "Quality & Safety Policy", "ehsSustainability": "EHS Sustainability", "mibHeadquarters": "MIB Headquarters"}}, "announcements": {"title": "Announcements", "submenus": {"career": "Career Opportunities", "events": "Corporate Events", "channelMoxa": "Channel Moxa"}}, "benefitsAndMe": {"title": "Benefits & Me", "submenus": {"ewc": "Employee Wellness Committee", "groupIssurance": "Group Life Insurance", "travelIssurance": "Travel Insurance", "employeeCare": "Employee Care"}}, "culture": {"title": "Culture", "submenus": {"holidays": "Holidays", "volunteer": "Volunteer Opportunities", "readingHours": "Reading Hours", "rdValues": "R&D Values"}}, "myColleagues": {"title": "My Colleagues", "submenus": {"whereColleagues": "Where Are My Colleagues", "newEmployees": "New Employees", "organizationChart": "Organization Chart"}}, "foundation": {"title": "Foundation", "submenus": {"foundationWebsite": "Foundation Website", "moxaLava": "<PERSON><PERSON>"}}, "departments": {"title": "Departments"}, "systems": {"title": "Systems"}}, "announcements": {"title": "Announcements", "released": "Released"}, "benefitPoints": {"title": "Benefit Points", "granted": "Granted", "remaining": "Remaining"}, "annualLeaves": {"title": "Annual Leaves", "granted": "Granted", "remaining": "Remaining"}, "toDoCard": {"title": "To-Do List", "completed": "Completed", "total": "Total", "dueDate": "Due Date", "status": "Status", "done": "Done", "inProgress": "In Progress", "notStarted": "Not Started", "overdue": "Overdue", "pending": "Pending"}, "companyEvents": {"title": "Company Events", "upcomingTitle": "Upcoming Company Events"}, "companyNews": {"title": "Company News"}, "ehsNews": {"title": "EHS News"}, "countdownCard": {"title": "Countdown", "config": {"description": "Description", "targetDate": "Target Date", "displayFormat": "Display Format"}, "days": "days", "hours": "hours", "minutes": "minutes", "seconds": "seconds"}, "salesKPICard": {"title": "Sales KPI", "target": "Target", "actual": "Actual", "percentage": "Percentage"}, "weatherCard": {"title": "Weather", "humidity": "<PERSON><PERSON><PERSON><PERSON>"}, "snakeGameCard": {"title": "Snake Game", "score": "Score", "gameOver": "Game Over", "play": "play", "pause": "pause", "resume": "resume", "restart": "restart", "playing": "Playing", "paused": "Paused", "instruction": "Press Space to"}, "officeFloorplan": {"title": "Office Floorplan", "buildings": "Buildings", "floors": "Floors", "departments": "Departments", "areaInformation": "Area Information", "noAreaSelected": "No area selected"}, "usefulApps": {"common": "Common Apps", "sales": "Sales Apps", "engineering": "Engineering Apps", "customerSupport": "Customer Support Apps", "logistics": "Logistics Apps", "hr": "HR Apps"}, "ticketBoard": {"title": "Ticket Board", "type": "Type", "status": "Status", "priority": "Priority"}, "bugCount": {"title": "Bug Count", "totalBugs": "Total Bugs", "assignedBugs": "Assigned", "assignedRatio": "<PERSON><PERSON>"}, "contractStatus": {"title": "Contract Status", "client": "Client"}, "chatbot": {"title": "Mo<PERSON>rt", "inputPlaceholder": "Type your message here", "enable": "Enable", "enableTools": "Enable Tools", "disableTools": "Disable Tools", "enableStreaming": "Enable Streaming", "disableStreaming": "Disable Streaming", "availableTools": "Available Tools", "showHistory": "Show Chat History", "clearHistory": "Clear Chat History", "deleteConversation": "Delete Conversation", "newChat": "New Chat", "chatHistory": "Chat History", "newConversation": "New Conversation", "welcomeMessage": "Hello {user_name}! I am <PERSON><PERSON><PERSON>, your Moxa AI assistant. How can I help you today?", "tagQuick": "Quick", "tagSearch": "Search", "tagReason": "Reason", "tagDeepResearch": "Deep research", "tagCreateImage": "Create image", "copyMessage": "Copy", "speakMessage": "Read aloud", "questionsToFollowUp": "Questions to follow up", "resend": "Resend", "dictate": "Dictate", "callTool": "Calling tool", "reasoning": "Reasoning", "evaluate": "Evaluate", "refine": "Refine", "reference": "Reference", "knowledgeBase": "Knowledge Base", "goodResponse": "Good response", "badResponse": "Bad response", "diagram": "Diagram", "downloadDiagram": "Download Diagram", "contentModerationError": "I apologize, but I cannot provide a response as it might contain inappropriate content. Please rephrase your question.", "unexpectedError": "Sorry, I encountered an unexpected error. Please try again later.", "errorMessage": "Sorry, I encountered an error:"}, "userProfile": {"switch": "Switch", "settings": "Settings", "reset": "Reset"}}