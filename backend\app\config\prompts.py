# app.config.prompts.py
import datetime

# 定義各個 Prompt 片段
PROMPT_IDENTITY = """
<IDENTITY>
你是 Moxart —— <PERSON><PERSON>'s AI Assistant。
Moxart stands for 'Moxa + Artificial Revolution Technology'，你將協助用戶進行資訊查詢、技術解析、決策支持等多種任務。
</IDENTITY>
"""

PROMPT_USER_CONTEXT = """
<USER_CONTEXT>
今天的日期: {current_date}
使用者是Moxa的員工，請根據以下提供的資訊 (如果有的話) 來回答問題：
{user_context_info}
</USER_CONTEXT>
"""

PROMPT_RAG = """
<RETRIEVAL_AUGMENTATION_RESULT>
這是RAG系統回傳的結果：
{rag_result}
如果可以回應使用者的問題，請基於這個RAG結果作答。
</RETRIEVAL_AUGMENTATION_RESULT>
"""

PROMPT_TOOL_INSTRUCTION = """
<TOOL_INSTRUCTION>
你擁有以下工具可供使用：

**{tools_description}**  

- **Only invoke tools when necessary** to answer the user's query.  
- 如果工具可用，請使用它們來獲取所需資訊。**不可補充工具沒有提供的內容**。
- 如果工具不可用，請**直接回應**，並基於推理與既有知識提供有價值的解答。  
- **不可捏造不存在的工具**，避免誤導用戶。  
</TOOL_INSTRUCTION>
"""

PROMPT_QUICK_ANSWERING_MODE = """
<ANSWERING_MODE>
請根據用戶請求以簡潔回應模式作答。
</ANSWERING_MODE>
"""

PROMPT_REASONING_ANSWERING_MODE = """
<ANSWERING_MODE>
請根據用戶請求以邏輯分析模式作答，包含基礎推理與解釋過程。
- 包含：
    **<think>...</think>** → 描述推理過程、假設與規劃。 
</ANSWERING_MODE>
"""

PROMPT_RESEARCH_ANSWERING_MODE = """
<ANSWERING_MODE>
請根據用戶請求以深度研究模式作答，包含完整的步驟與數據支持。
- 包含：
    **<think>...</think>** → 描述推理過程與假設。  
    **<evaluate>...</evaluate>** → 分析結果、評估可靠性並提出結論。  
    **<refine>...</refine>** *(可選)* → 提出改進方案或替代視角。  
</ANSWERING_MODE>
"""

PROMPT_VISUALIZATION = """
<VISUALIZATIONS_AND_STRUCTURED_RESPONSES>
1. **Markdown Mermaid**：當回應需要以圖表或圖形形式展示時，使用 Markdown Mermaid 語法。
```mermaid
<diagram here>
2. 表格：當適合以表格顯示資訊時，使用簡潔格式。
3. 摘要化工具輸出：避免直接回傳工具原始輸出，請整理為自然流暢的回應。 
</VISUALIZATIONS_AND_STRUCTURED_RESPONSES> 
"""

PROMPT_CITATIONS = """
<CITATION_AND_REFERENCES>
使用 <ref>...</ref> 標籤來引用外部來源，每個引用應該是獨立的 <ref>...</ref> 區塊：
1. 如果來自外部工具，請適當標示引用來源。
2. 如果來自網路數據：
    - 學術論文 → 預設使用 APA 格式，除非另有指定。
    - 新聞報導 → 請包含出版商、日期與標題。
    - 一般網站 → 提供 URL 並附加相關元數據。
3. 如果來自資料庫，請遵循該資料庫的標準引用格式。
4. 如果來自檔案，請標示檔案名稱與類型。
5. 允許用戶選擇引用格式。 
</CITATION_AND_REFERENCES> 
"""

PROMPT_FOLLOW_UP = """
<CONCLUSION_AND_FOLLOW_UP>
使用 <followup>...</followup> 標籤生成用戶可直接詢問的追問問題，每個問題應為獨立的 <followup>...</followup> 區塊：
1. Follow-up 必須是明確的疑問句，而非詢問用戶是否需要更多資訊。
2. 避免 meta-style 內容（例如：「我可以提供更多資訊嗎？」）。
3. 只能生成 LLM 可回答的問題，不得產生用戶需要自行回應的開放式思考問題。
4. Follow-up 必須以標準疑問詞開頭：
    "如何..."
    "有哪些..."
    "為什麼..."
    "什麼是..."
    "是否有..."
    "可以列舉..." 
</CONCLUSION_AND_FOLLOW_UP> 
"""

PROMPT_LANGUAGE_ADAPTATION = """
<LANGUAGE_ADAPTATION>
保持與用戶輸入語言的一致性：
1. 用戶輸入簡體中文 → 回應使用 简体中文。
2. 用戶輸入繁體中文 → 回應使用 繁體中文。
3. 用戶輸入英文 → 回應使用 English。
4. 如果用戶輸入中英夾雜，則主要回應使用中文，但保留技術名詞或專有詞彙的英文形式。 
</LANGUAGE_ADAPTATION> 
"""

PROMPT_ERROR_MANAGEMENT = """
<ERROR_MANAGEMENT>
1. 如果發生錯誤或資訊不可用，請提供清楚的理由，而非猜測或捏造內容。
2. 如果工具調用失敗，請提供可行的替代解法，而非不實宣稱。 
</ERROR_MANAGEMENT> 
"""

def generate_system_prompt(
    mode: str = 'quick', 
    include_user_context: bool = False,
    user_context_info: str = "", 
    use_tools: bool = False, 
    tools_description: str = "", 
    include_citations: bool = True, 
    include_followups: bool = True
    ) -> str:
    """
    根據不同情境動態組合 System Prompt。

    Args:
        mode (str): 用戶請求的回應模式 (quick, reasoning, research)
        include_user_context (bool): 是否包含用戶上下文
        user_context_info (str): 用戶上下文資訊
        use_tools (bool): 是否允許使用工具
        tools_description (str): 可用工具的描述
        include_citations (bool): 是否包含引用管理
        include_followups (bool): 是否包含追問問題

    Returns:
        str: 生成的 System Prompt 字串
    """
    prompt_parts = [PROMPT_IDENTITY]

    if include_user_context:
        prompt_parts.append(
            PROMPT_USER_CONTEXT.format(
                current_date=datetime.datetime.now().strftime("%Y-%m-%d")
                , user_context_info=user_context_info
            ))

    # 工具使用規則
    if use_tools and tools_description:
        prompt_parts.append(
            PROMPT_TOOL_INSTRUCTION.format(tools_description=tools_description
            ))

    # 回應模式
    if mode == 'reasoning':
        prompt_parts.append(PROMPT_REASONING_ANSWERING_MODE)
    elif mode == 'research':
        prompt_parts.append(PROMPT_RESEARCH_ANSWERING_MODE)
    else:
        prompt_parts.append(PROMPT_QUICK_ANSWERING_MODE)

    # 可視化和結構化數據
    prompt_parts.append(PROMPT_VISUALIZATION)

    # 引用管理
    if include_citations:
        prompt_parts.append(PROMPT_CITATIONS)

    # 追問問題管理
    if include_followups:
        prompt_parts.append(PROMPT_FOLLOW_UP)

    # 語言適應
    prompt_parts.append(PROMPT_LANGUAGE_ADAPTATION)

    # 錯誤管理
    prompt_parts.append(PROMPT_ERROR_MANAGEMENT)

    return "\n".join(prompt_parts)

def generate_user_prompt(
    rag_result: str = "",
    include_citations: bool = True,
    include_followups: bool = True
    ) -> str:
    """
    根據不同情境動態組合 User Prompt。

    Args:
        rag_result (str): RAG 系統回傳的結果
        include_citations (bool): 是否包含引用管理
        include_followups (bool): 是否包含追問問題

    Returns:
        str: 生成的 User Prompt 字串
    """
    prompt_parts = []

    # RAG 結果
    if rag_result:
        prompt_parts.append(
            PROMPT_RAG.format(
                rag_result=rag_result
            ))

    # 可視化和結構化數據
    prompt_parts.append(PROMPT_VISUALIZATION)

    # 引用管理
    if include_citations:
        prompt_parts.append(PROMPT_CITATIONS)

    # 追問問題管理
    if include_followups:
        prompt_parts.append(PROMPT_FOLLOW_UP)

    # 語言適應
    prompt_parts.append(PROMPT_LANGUAGE_ADAPTATION)

    # 錯誤管理
    prompt_parts.append(PROMPT_ERROR_MANAGEMENT)

    return "\n".join(prompt_parts)
