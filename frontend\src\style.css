:root {
  font-family: system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;

  color-scheme: light;
  color: #202020;
  background-color: #FFFFFF;

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

a {
  font-weight: 500;
  color: #008787;
  text-decoration: inherit;
}
a:hover {
  color: #3EE9CB;
}

body {
  margin: 0;
  min-width: 320px;
  min-height: 100vh;
}

h1 {
  font-size: 3.2em;
  line-height: 1.1;
}

button {
  border-radius: 8px;
  border: 1px solid transparent;
  padding: 0.6em 1.2em;
  font-size: 1em;
  font-weight: 500;
  font-family: inherit;
  background-color: #008787;
  color: #FFFFFF;
  cursor: pointer;
  transition: all 0.25s;
}
button:hover {
  background-color: #3EE9CB;
  color: #202020;
}
button:focus,
button:focus-visible {
  outline: 4px auto #204a88;
}

.card {
  padding: 2em;
}

#app {
  width: 100%;
  margin: 0;
  padding: 0;
}

/* Remove focus outline from icon buttons */
.v-btn--icon.v-btn--variant-text:focus {
  outline: none !important;
  box-shadow: none !important;
}

/* Maintain accessibility for keyboard navigation */
.v-btn--icon.v-btn--variant-text:focus-visible {
  outline: none !important;
  box-shadow: none !important;
}
